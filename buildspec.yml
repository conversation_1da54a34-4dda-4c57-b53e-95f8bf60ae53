version: 0.2

phases:
  install:
    runtime-versions:
      docker: 20
  pre_build:
    commands:
      - echo Logging in to Alibaba Cloud Container Registry...
      # 如果需要登录到私有仓库
      - docker login --username=$DOCKER_USER --password=$DOCKER_PASS $DOCKER_REGISTRY
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      # 启用 BuildKit 和层缓存
      - export DOCKER_BUILDKIT=1
      - export BUILDKIT_INLINE_CACHE=1
      # 使用云效的缓存机制
      - docker build --cache-from cantv-ding:latest -t cantv-ding:latest .
      - docker tag cantv-ding:latest $IMAGE_URI:latest
      - docker tag cantv-ding:latest $IMAGE_URI:$CODEBUILD_BUILD_NUMBER
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $IMAGE_URI:latest
      - docker push $IMAGE_URI:$CODEBUILD_BUILD_NUMBER