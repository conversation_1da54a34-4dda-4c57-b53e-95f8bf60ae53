# 线上权限系统问题解决方案

## 问题描述
线上环境中角色和权限无法获取，可能的原因包括：
1. 数据库迁移未执行
2. 权限系统未初始化
3. Docker容器启动时缺少初始化步骤

## 解决方案

### 1. 立即诊断问题

在线上环境中运行诊断脚本：

```bash
# 进入容器
docker exec -it <container_name> sh

# 运行诊断脚本
node src/scripts/diagnosePermissions.cjs
```

### 2. 检查健康状态

访问健康检查端点：
```
GET /api/health
```

返回示例：
```json
{
  "status": "healthy",
  "timestamp": "2024-06-22T10:00:00.000Z",
  "database": "connected",
  "permissions": {
    "roles": 4,
    "permissions": 30,
    "initialized": true
  },
  "redis": "connected"
}
```

### 3. 手动修复步骤

如果权限系统未初始化，在容器中执行：

```bash
# 1. 确保数据库迁移已执行
pnpm db:deploy

# 2. 运行权限初始化检查
node src/scripts/checkAndInitPermissions.cjs

# 3. 如果仍有问题，运行诊断
node src/scripts/diagnosePermissions.cjs
```

### 4. 重新部署解决方案

更新后的Dockerfile已包含以下改进：

1. **自动数据库迁移**：启动时自动执行 `prisma migrate deploy`
2. **权限系统检查**：启动时检查并初始化权限系统
3. **健康检查**：提供 `/api/health` 端点监控系统状态

#### 新的启动流程：

```bash
# 1. 等待数据库连接
# 2. 执行数据库迁移
# 3. 检查并初始化权限系统
# 4. 启动应用服务
```

### 5. 验证修复

部署后验证：

1. **检查健康状态**：
   ```bash
   curl http://your-domain/api/health
   ```

2. **测试角色API**：
   ```bash
   curl http://your-domain/api/roles
   ```

3. **测试权限API**：
   ```bash
   curl http://your-domain/api/permissions
   ```

## 预防措施

### 1. 监控脚本

创建监控脚本定期检查权限系统状态：

```bash
#!/bin/bash
# monitor-permissions.sh

HEALTH_URL="http://localhost:3000/api/health"
RESPONSE=$(curl -s $HEALTH_URL)

if echo "$RESPONSE" | grep -q '"initialized":true'; then
    echo "✅ 权限系统正常"
else
    echo "❌ 权限系统异常"
    echo "$RESPONSE"
    # 发送告警通知
fi
```

### 2. 数据库备份

定期备份权限相关表：

```sql
-- 备份权限系统表
pg_dump -h localhost -U username -d database_name \
  -t roles -t permissions -t role_permissions \
  -t user_roles -t department_roles \
  > permissions_backup.sql
```

### 3. 环境变量检查

确保以下环境变量正确配置：

```env
DATABASE_URL=postgresql://username:password@host:port/database
NODE_ENV=production
```

## 常见问题排查

### Q1: 权限API返回空数组
**原因**：权限系统未初始化
**解决**：运行 `node src/scripts/checkAndInitPermissions.cjs`

### Q2: 数据库连接失败
**原因**：数据库配置错误或网络问题
**解决**：检查 `DATABASE_URL` 环境变量和网络连接

### Q3: 迁移执行失败
**原因**：数据库权限不足或表结构冲突
**解决**：检查数据库用户权限，必要时手动清理冲突表

### Q4: 容器启动失败
**原因**：启动脚本权限问题
**解决**：确保 `start.sh` 有执行权限

## 应急恢复

如果权限系统完全损坏，可以使用以下步骤恢复：

```bash
# 1. 备份现有数据
pg_dump database_name > backup.sql

# 2. 重置权限相关表
psql -d database_name -c "
  TRUNCATE role_permissions, user_roles, department_roles;
  DELETE FROM roles WHERE is_system = true;
  DELETE FROM permissions WHERE is_system = true;
"

# 3. 重新初始化
node src/scripts/checkAndInitPermissions.cjs

# 4. 验证恢复
node src/scripts/diagnosePermissions.cjs
```

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 健康检查端点返回结果
2. 诊断脚本输出
3. 应用日志（最近100行）
4. 数据库连接状态

```bash
# 收集诊断信息
curl http://your-domain/api/health > health.json
node src/scripts/diagnosePermissions.cjs > diagnosis.log
docker logs <container_name> --tail 100 > app.log
```
