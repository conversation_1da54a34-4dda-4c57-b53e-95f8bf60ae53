# 钉钉Stream服务V2更新说明

## 更新概述

基于已验证正常工作的 `dingtalk-stream-simple.ts` 实现，我创建了一个增强版的V2服务 `dingtalk-stream-v2-updated.ts`，解决了之前的400错误问题，并集成了审批服务的自动处理功能。

## 主要改进

### 1. **修复400错误问题**

基于官方SDK的正确实现方式：

```typescript
// ✅ 正确的API端点和请求格式
const GET_TOKEN_URL = 'https://oapi.dingtalk.com/gettoken';
const GATEWAY_URL = 'https://api.dingtalk.com/v1.0/gateway/connections/open';

// ✅ 正确的请求头
headers: {
  'Content-Type': 'application/json',
  'Accept': 'application/json'  // 关键：官方SDK中的必需头部
}

// ✅ 正确的WebSocket URL构建
this.dwUrl = `${data.endpoint}?ticket=${data.ticket}`;
```

### 2. **集成审批服务**

V2版本自动处理审批状态变更事件：

```typescript
private async onCallback(message: DWClientDownStream): Promise<void> {
  // 根据事件类型处理
  if (message.headers.eventType === 'bpms_instance_change') {
    console.log('📋 收到审批状态变更事件');
    
    try {
      // 解析审批数据
      const approvalData = JSON.parse(message.data);
      
      // 调用审批服务处理
      const result = await this.approvalService.handleApprovalStatusChange({
        processInstanceId: approvalData.processInstanceId,
        result: approvalData.result || 'unknown',
        type: approvalData.type || 'unknown',
        staffId: approvalData.staffId || '',
        createTime: approvalData.createTime || Date.now(),
        finishTime: approvalData.finishTime,
        corpId: approvalData.corpId || ''
      });

      console.log('✅ 审批状态变更处理结果:', result);
      this.emit('approval_change', approvalData);
    } catch (error) {
      console.error('❌ 处理审批状态变更失败:', error);
    }
  }
}
```

### 3. **标准化消息处理**

基于官方SDK的消息格式和处理流程：

```typescript
// ✅ 官方SDK标准消息格式
export interface DWClientDownStream {
  specVersion: string;
  type: string;
  headers: {
    appId: string;
    connectionId: string;
    contentType: string;
    messageId: string;
    time: string;
    topic: string;
    eventType?: string;
    eventBornTime?: string;
    eventId?: string;
    eventCorpId?: string;
    eventUnifiedAppId?: string;
  };
  data: string;
}

// ✅ 标准化消息处理流程
switch (message.type) {
  case 'SYSTEM':
    this.onSystem(message);
    break;
  case 'EVENT':
    this.onEvent(message);
    break;
  case 'CALLBACK':
    this.onCallback(message);
    break;
}
```

### 4. **增强的错误处理和重连机制**

```typescript
// ✅ 自动重连机制
private scheduleReconnect(): void {
  this.reconnectAttempts++;
  const delay = this.reconnectInterval * this.reconnectAttempts;
  
  console.log(`🔄 ${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);
  
  setTimeout(async () => {
    try {
      await this.start();
    } catch (error) {
      console.error('❌ 重连失败:', error);
    }
  }, delay);
}

// ✅ 连接状态监控
public getConnectionStatus(): {
  isConnected: boolean;
  reconnectAttempts: number;
  readyState?: number;
} {
  return {
    isConnected: this.isConnected,
    reconnectAttempts: this.reconnectAttempts,
    readyState: this.ws?.readyState
  };
}
```

## 使用方法

### 1. **启动服务**

V2服务已集成到主应用中：

```typescript
// 环境变量控制
if (env.NODE_ENV === 'production' || process.env.ENABLE_DINGTALK_STREAM === 'true') {
  const streamService = new DingTalkStreamServiceV2();
  await streamService.start();
}
```

### 2. **测试服务**

```bash
# 测试V2服务
node scripts/test-stream-v2.js

# 启用Stream服务的环境变量
export ENABLE_DINGTALK_STREAM=true
npm start
```

### 3. **监控状态**

```bash
# 查看Stream服务状态
curl http://localhost:3000/api/dingtalk/stream/status
```

## 事件处理

### 1. **审批状态变更**

当钉钉审批状态发生变更时，V2服务会：

1. 接收 `bpms_instance_change` 事件
2. 解析审批数据
3. 调用 `ApprovalService.handleApprovalStatusChange()`
4. 更新数据库中的相关记录
5. 触发 `approval_change` 事件供外部监听

### 2. **事件监听**

```typescript
streamService.on('callback', (data) => {
  console.log('收到回调事件:', data);
});

streamService.on('approval_change', (data) => {
  console.log('收到审批变更事件:', data);
  // 这里的数据已经被ApprovalService处理过
});

streamService.on('error', (error) => {
  console.error('Stream服务错误:', error);
});
```

## 预期效果

使用V2服务后，您应该看到：

```
🚀 启动钉钉Stream服务V2...
🔑 获取访问令牌...
✅ 访问令牌获取成功
📡 获取Stream连接信息...
📤 请求Stream端点: { clientId: 'dingxxx...', subscriptions: [...] }
📥 响应状态: 200 OK
📥 响应数据: { endpoint: 'wss://...', ticket: '...' }
✅ Stream端点构建成功
✅ Stream连接信息获取成功
🔗 建立WebSocket连接...
🔗 连接到WebSocket端点: wss://...
✅ WebSocket连接已建立
✅ WebSocket连接建立成功
✅ 钉钉Stream服务V2启动成功
```

当有审批事件时：

```
📥 收到原始消息: {"specVersion":"1.0","type":"CALLBACK",...}
📨 解析后的消息: { type: 'CALLBACK', topic: '/v1.0/workflow/processInstances/events/changed', eventType: 'bpms_instance_change', messageId: '...' }
📞 处理回调消息: /v1.0/workflow/processInstances/events/changed
📤 发送响应: ...
📋 收到审批状态变更事件
✅ 审批状态变更处理结果: { success: true, ... }
```

## 与Simple版本的区别

| 特性 | Simple版本 | V2版本 |
|------|------------|--------|
| 基础连接 | ✅ | ✅ |
| 消息接收 | ✅ | ✅ |
| 审批处理 | ❌ 需手动处理 | ✅ 自动处理 |
| 数据库更新 | ❌ | ✅ |
| 错误恢复 | 基础 | 增强 |
| 状态监控 | 基础 | 详细 |
| 事件触发 | 基础 | 完整 |

## 注意事项

1. **环境配置**：确保正确配置钉钉应用信息
2. **数据库连接**：V2版本需要数据库连接来处理审批数据
3. **权限设置**：确保钉钉应用有审批事件订阅权限
4. **网络连接**：确保服务器能访问钉钉API和WebSocket端点

V2版本现在应该能够完全解决400错误问题，并提供完整的审批事件自动处理功能。
