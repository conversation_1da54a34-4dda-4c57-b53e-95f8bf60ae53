# 收入确认功能 API 文档

## 概述

收入确认功能允许用户确认项目收入的实际金额，并将收入状态更新为已确认。支持单个确认和批量确认两种方式。

## 功能特性

- ✅ 单个收入确认
- ✅ 批量收入确认
- ✅ 实际金额记录
- ✅ 确认时间记录
- ✅ 确认备注
- ✅ 状态自动更新
- ✅ 参数验证
- ✅ 错误处理

## API 接口

### 1. 确认单个收入

**接口地址**: `PUT /api/revenues/{id}/confirm`

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {jwt_token}
```

**路径参数**:
- `id` (string, 必填): 收入ID

**请求体**:
```json
{
  "actualAmount": 95000,
  "confirmedDate": "2024-06-25",
  "notes": "收入已确认，实际金额略低于预期"
}
```

**请求参数说明**:
- `actualAmount` (number, 必填): 实际收入金额，必须大于等于0
- `confirmedDate` (string, 可选): 确认收入时间，格式为 YYYY-MM-DD，默认为当前日期
- `notes` (string, 可选): 确认备注

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "revenue-123",
    "title": "项目第一阶段收入",
    "revenueType": "project_income",
    "status": "received",
    "plannedAmount": 100000,
    "actualAmount": 95000,
    "confirmedDate": "2024-06-25",
    "notes": "收入已确认，实际金额略低于预期",
    "projectId": "project-001",
    "createdAt": "2024-06-01T00:00:00.000Z",
    "updatedAt": "2024-06-25T10:30:00.000Z",
    "createdBy": "user-001",
    "updatedBy": "user-001"
  },
  "message": "确认收入成功"
}
```

### 2. 批量确认收入

**接口地址**: `PUT /api/revenues/batch-confirm`

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {jwt_token}
```

**请求体**:
```json
{
  "revenues": [
    {
      "id": "revenue-123",
      "actualAmount": 95000,
      "confirmedDate": "2024-06-25",
      "notes": "第一个收入确认"
    },
    {
      "id": "revenue-124",
      "actualAmount": 80000,
      "confirmedDate": "2024-06-25",
      "notes": "第二个收入确认"
    },
    {
      "id": "revenue-125",
      "actualAmount": 120000,
      "confirmedDate": "2024-06-25"
    }
  ]
}
```

**请求参数说明**:
- `revenues` (array, 必填): 要确认的收入列表，最少1个，最多100个
  - `id` (string, 必填): 收入ID
  - `actualAmount` (number, 必填): 实际收入金额，必须大于等于0
  - `confirmedDate` (string, 可选): 确认收入时间，格式为 YYYY-MM-DD
  - `notes` (string, 可选): 确认备注

**响应示例**:
```json
{
  "success": true,
  "data": {
    "successCount": 2,
    "failureCount": 1,
    "results": [
      {
        "id": "revenue-123",
        "success": true,
        "data": {
          "id": "revenue-123",
          "status": "received",
          "actualAmount": 95000,
          "confirmedDate": "2024-06-25"
        }
      },
      {
        "id": "revenue-124",
        "success": true,
        "data": {
          "id": "revenue-124",
          "status": "received",
          "actualAmount": 80000,
          "confirmedDate": "2024-06-25"
        }
      },
      {
        "id": "revenue-125",
        "success": false,
        "error": "收入不存在"
      }
    ]
  },
  "message": "批量确认收入完成，成功 2 个，失败 1 个"
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 参数验证失败 |
| 401 | 未授权访问 |
| 404 | 收入不存在 |
| 500 | 服务器内部错误 |

## 错误响应示例

### 参数验证失败
```json
{
  "success": false,
  "message": "参数验证失败",
  "errors": [
    {
      "code": "invalid_type",
      "expected": "number",
      "received": "string",
      "path": ["actualAmount"],
      "message": "实际收入金额不能为负数"
    }
  ]
}
```

### 收入不存在
```json
{
  "success": false,
  "message": "项目收入不存在"
}
```

## 业务规则

1. **状态更新**: 确认收入后，状态自动更新为 `received`（已收款）
2. **金额验证**: 实际金额必须大于等于0
3. **日期格式**: 确认日期必须为有效的日期格式 (YYYY-MM-DD)
4. **批量限制**: 批量确认最多支持100个收入
5. **权限控制**: 需要有效的JWT认证
6. **审计日志**: 所有确认操作都会记录操作者和时间

## 使用示例

### JavaScript/Node.js
```javascript
// 确认单个收入
const confirmRevenue = async (revenueId, actualAmount, notes) => {
  const response = await fetch(`/api/revenues/${revenueId}/confirm`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      actualAmount,
      confirmedDate: new Date().toISOString().split('T')[0],
      notes
    })
  });
  
  return await response.json();
};

// 批量确认收入
const batchConfirmRevenues = async (revenues) => {
  const response = await fetch('/api/revenues/batch-confirm', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ revenues })
  });
  
  return await response.json();
};
```

### cURL
```bash
# 确认单个收入
curl -X PUT "http://localhost:3000/api/revenues/revenue-123/confirm" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "actualAmount": 95000,
    "confirmedDate": "2024-06-25",
    "notes": "收入已确认"
  }'

# 批量确认收入
curl -X PUT "http://localhost:3000/api/revenues/batch-confirm" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "revenues": [
      {
        "id": "revenue-123",
        "actualAmount": 95000,
        "confirmedDate": "2024-06-25",
        "notes": "第一个收入确认"
      },
      {
        "id": "revenue-124",
        "actualAmount": 80000,
        "confirmedDate": "2024-06-25",
        "notes": "第二个收入确认"
      }
    ]
  }'
```

## 注意事项

1. **幂等性**: 重复确认同一个收入会更新其确认信息
2. **事务性**: 批量确认中的失败不会影响其他收入的确认
3. **性能**: 批量确认建议每次不超过50个收入以保证性能
4. **权限**: 确保用户有权限确认相关项目的收入
5. **数据一致性**: 确认后的数据会立即生效，影响相关统计报表
