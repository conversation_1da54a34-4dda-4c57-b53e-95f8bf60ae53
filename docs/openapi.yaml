openapi: 3.0.3
info:
  title: 项目管理系统 API
  description: |
    项目管理系统的完整API接口文档，包括品牌管理、项目管理、统计分析和文件上传功能。
    
    ## 功能特性
    - 品牌库管理
    - 项目立项和管理
    - 预算成本控制
    - 利润自动计算
    - 统计分析报表
    - 文件上传管理
    
    ## 技术栈
    - Node.js + TypeScript
    - PostgreSQL + Prisma ORM
    - 钉钉企业应用集成
  version: 1.0.0
  contact:
    name: API Support
    url: http://localhost:3000/api-docs.html
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3000/api
    description: 开发环境
  - url: https://your-domain.com/api
    description: 生产环境

tags:
  - name: brands
    description: 品牌管理
  - name: projects
    description: 项目管理
  - name: stats
    description: 统计分析
  - name: upload
    description: 文件上传

paths:
  /brands:
    get:
      tags: [brands]
      summary: 获取品牌列表
      description: 获取品牌列表，支持分页、搜索和状态筛选
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
        - name: status
          in: query
          description: 品牌状态
          schema:
            type: string
            enum: [active, inactive]
        - name: keyword
          in: query
          description: 搜索关键字
          schema:
            type: string
      responses:
        '200':
          description: 成功获取品牌列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

    post:
      tags: [brands]
      summary: 创建品牌
      description: 创建新的品牌
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBrandRequest'
      responses:
        '200':
          description: 品牌创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '422':
          $ref: '#/components/responses/ValidationError'

    put:
      tags: [brands]
      summary: 更新品牌
      description: 更新现有品牌信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBrandRequest'
      responses:
        '200':
          description: 品牌更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  /brands/{id}:
    get:
      tags: [brands]
      summary: 获取单个品牌
      description: 根据ID获取品牌详细信息
      parameters:
        - name: id
          in: path
          required: true
          description: 品牌ID
          schema:
            type: string
      responses:
        '200':
          description: 成功获取品牌信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandResponse'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags: [brands]
      summary: 删除品牌
      description: 删除指定的品牌
      parameters:
        - name: id
          in: path
          required: true
          description: 品牌ID
          schema:
            type: string
      responses:
        '200':
          description: 品牌删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'

  /projects:
    get:
      tags: [projects]
      summary: 获取项目列表
      description: 获取项目列表，支持多种筛选条件
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          description: 项目状态
          schema:
            $ref: '#/components/schemas/ProjectStatus'
        - name: brandId
          in: query
          description: 品牌ID
          schema:
            type: string
        - name: contractType
          in: query
          description: 合同类型
          schema:
            $ref: '#/components/schemas/ContractType'
        - name: keyword
          in: query
          description: 项目名称关键字
          schema:
            type: string
      responses:
        '200':
          description: 成功获取项目列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectListResponse'

    post:
      tags: [projects]
      summary: 创建项目
      description: 创建新的项目
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProjectRequest'
      responses:
        '200':
          description: 项目创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectResponse'

  /projects/{id}:
    get:
      tags: [projects]
      summary: 获取单个项目
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取项目信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectResponse'

    delete:
      tags: [projects]
      summary: 删除项目
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 项目删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /projects/stats:
    get:
      tags: [stats]
      summary: 获取项目统计
      description: 获取项目的统计数据
      responses:
        '200':
          description: 成功获取统计数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectStatsResponse'

  /upload:
    post:
      tags: [upload]
      summary: 上传文件
      description: 上传项目相关文件
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 要上传的文件
              required:
                - file
      responses:
        '200':
          description: 文件上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadResponse'

components:
  schemas:
    # 基础响应结构
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
        - success
        - message

    SuccessResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            success:
              type: boolean
              example: true

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            success:
              type: boolean
              example: false
            code:
              type: string
            details:
              type: object

    # 枚举类型
    BrandStatus:
      type: string
      enum: [active, inactive]
      description: 品牌状态

    ProjectStatus:
      type: string
      enum: [draft, active, completed, cancelled]
      description: 项目状态

    ContractType:
      type: string
      enum: [annual_frame, quarterly_frame, single, po_order, jing_task]
      description: 合同类型

    DocumentType:
      type: string
      enum: [project_initiation]
      description: 单据类型

    # 品牌相关
    Brand:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        logo:
          type: string
        status:
          $ref: '#/components/schemas/BrandStatus'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        createdBy:
          type: string
      required:
        - id
        - name
        - status

    CreateBrandRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        description:
          type: string
        logo:
          type: string
          format: uri
      required:
        - name

    UpdateBrandRequest:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        logo:
          type: string
        status:
          $ref: '#/components/schemas/BrandStatus'
      required:
        - id

    BrandResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Brand'

    BrandListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                brands:
                  type: array
                  items:
                    $ref: '#/components/schemas/Brand'
                total:
                  type: integer
                page:
                  type: integer
                pageSize:
                  type: integer
                totalPages:
                  type: integer

    # 项目相关
    ProjectBudget:
      type: object
      properties:
        planningBudget:
          type: number
          format: decimal
        influencerBudget:
          type: number
          format: decimal
        adBudget:
          type: number
          format: decimal
        otherBudget:
          type: number
          format: decimal
      required:
        - planningBudget
        - influencerBudget
        - adBudget
        - otherBudget

    ProjectCost:
      type: object
      properties:
        influencerCost:
          type: number
          format: decimal
        adCost:
          type: number
          format: decimal
        otherCost:
          type: number
          format: decimal
        estimatedInfluencerRebate:
          type: number
          format: decimal
      required:
        - influencerCost
        - adCost
        - otherCost
        - estimatedInfluencerRebate

    ProjectPeriod:
      type: object
      properties:
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
      required:
        - startDate
        - endDate

    Project:
      type: object
      properties:
        id:
          type: string
        documentType:
          $ref: '#/components/schemas/DocumentType'
        brandId:
          type: string
        projectName:
          type: string
        period:
          $ref: '#/components/schemas/ProjectPeriod'
        budget:
          $ref: '#/components/schemas/ProjectBudget'
        cost:
          $ref: '#/components/schemas/ProjectCost'
        executorPM:
          type: string
        contentMediaIds:
          type: array
          items:
            type: string
        contractType:
          $ref: '#/components/schemas/ContractType'
        settlementRules:
          type: string
        kpi:
          type: string
        status:
          $ref: '#/components/schemas/ProjectStatus'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateProjectRequest:
      type: object
      properties:
        documentType:
          $ref: '#/components/schemas/DocumentType'
        brandId:
          type: string
        projectName:
          type: string
        period:
          $ref: '#/components/schemas/ProjectPeriod'
        budget:
          $ref: '#/components/schemas/ProjectBudget'
        cost:
          $ref: '#/components/schemas/ProjectCost'
        executorPM:
          type: string
        contentMediaIds:
          type: array
          items:
            type: string
        contractType:
          $ref: '#/components/schemas/ContractType'
        settlementRules:
          type: string
        kpi:
          type: string
      required:
        - documentType
        - brandId
        - projectName
        - period
        - budget
        - cost
        - executorPM
        - contentMediaIds
        - contractType
        - settlementRules
        - kpi

    ProjectResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Project'

    ProjectListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                projects:
                  type: array
                  items:
                    $ref: '#/components/schemas/Project'
                total:
                  type: integer
                page:
                  type: integer
                pageSize:
                  type: integer
                totalPages:
                  type: integer

    # 统计相关
    ProjectStats:
      type: object
      properties:
        totalProjects:
          type: integer
        activeProjects:
          type: integer
        completedProjects:
          type: integer
        totalBudget:
          type: number
          format: decimal
        totalProfit:
          type: number
          format: decimal
        averageGrossMargin:
          type: number
          format: decimal

    ProjectStatsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ProjectStats'

    # 文件上传
    FileUpload:
      type: object
      properties:
        id:
          type: string
        filename:
          type: string
        originalName:
          type: string
        size:
          type: integer
        mimeType:
          type: string
        url:
          type: string

    FileUploadResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/FileUpload'

  responses:
    BadRequest:
      description: 请求参数无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Conflict:
      description: 资源冲突
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    ValidationError:
      description: 数据验证失败
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
