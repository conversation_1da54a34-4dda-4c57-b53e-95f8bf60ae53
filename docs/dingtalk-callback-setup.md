# 钉钉回调配置简易指南

## 🎯 只需要配置一个地址！

在钉钉开放平台后台，您只需要配置这一个回调地址：

```
https://your-domain.com/api/dingtalk/callback
```

## 📋 配置步骤

### 1. 钉钉后台配置

1. **登录钉钉开放平台** → 应用管理 → 选择您的应用
2. **找到"事件与回调"或"回调配置"**
3. **在"请求网址URL"输入框中填入**：
   ```
   https://your-domain.com/api/dingtalk/callback
   ```
4. **获取并配置加密信息**：
   - Token（令牌）
   - AES Key（数据加密密钥）
   - 记录您的 App Key（作为 Suite Key）

### 2. 服务器环境配置

在您的 `.env` 文件中配置：

```env
# 钉钉回调加密配置
DINGTALK_CALLBACK_TOKEN=从钉钉后台获取的Token
DINGTALK_AES_KEY=从钉钉后台获取的AESKey
DINGTALK_SUITE_KEY=您的应用AppKey
```

### 3. 验证配置

1. **保存钉钉后台配置** - 钉钉会自动发送验证请求
2. **检查服务器日志** - 应该看到 "✅ 回调验证成功"
3. **测试回调** - 可以访问测试接口：
   ```
   GET https://your-domain.com/api/dingtalk/callback/test
   ```

## 🔄 支持的事件类型

这个统一回调地址会自动处理以下事件：

### 📋 审批事件
- `bpms_instance_change` - 审批实例状态变更

### 👥 通讯录事件
- `user_add_org` - 用户加入企业
- `user_modify_org` - 用户信息修改
- `user_leave_org` - 用户离开企业
- `org_admin_add` - 管理员添加
- `org_admin_remove` - 管理员移除
- `org_dept_create` - 部门创建
- `org_dept_modify` - 部门修改
- `org_dept_remove` - 部门删除

## 🚨 常见问题

### Q: 钉钉提示"回调地址验证失败"
**A:** 检查以下几点：
1. 服务器是否可以公网访问
2. `.env` 文件中的 Token 和 AES Key 是否正确
3. 服务器是否正常运行

### Q: 收不到回调事件
**A:** 确认：
1. 钉钉后台是否已启用相应的事件订阅
2. 回调地址配置是否正确
3. 查看服务器日志是否有错误信息

### Q: 本地开发如何测试
**A:** 可以使用 ngrok 等工具：
```bash
# 安装 ngrok
npm install -g ngrok

# 启动本地服务
npm run dev

# 在另一个终端暴露本地服务
ngrok http 3000

# 使用 ngrok 提供的 https 地址配置钉钉回调
```

## 📝 日志示例

成功的回调处理日志：
```
🔔 收到钉钉回调: { signature: "xxx", timestamp: "xxx", nonce: "xxx" }
📨 回调数据: { EventType: "bpms_instance_change", ... }
📋 处理审批事件
✅ 回调处理完成: { success: true, message: "回调处理成功" }
```

## 🔧 高级配置

如果您需要分别处理不同类型的事件，也可以使用专门的路由：

- **仅审批事件**: `/api/dingtalk/callback/approval`
- **仅通讯录事件**: `/api/dingtalk/callback/general`

但推荐使用统一入口 `/api/dingtalk/callback`，系统会自动分发处理。
