# 系统时区配置说明

## 概述

本系统已完整配置了中国标准时间（Asia/Shanghai）作为默认时区，确保所有时间相关的操作都使用正确的时区。

## 配置内容

### 1. 环境变量配置

在 `.env` 和 `.env.prod` 文件中添加了时区配置：

```env
# 时区配置
TIMEZONE=Asia/Shanghai
```

### 2. 应用启动时区设置

在 `src/index.ts` 中设置进程时区：

```typescript
// 设置时区
process.env.TZ = env.TIMEZONE;
console.log(`🌍 时区设置为: ${env.TIMEZONE}`);
```

### 3. Docker容器时区配置

在 `Dockerfile` 中配置：

```dockerfile
# 安装时区数据
RUN apk add --no-cache tzdata curl

# 设置环境变量
ENV TZ=Asia/Shanghai
ENV TIMEZONE=Asia/Shanghai
```

### 4. 时区工具类

创建了 `src/utils/timezone.ts` 工具类，提供以下功能：

#### 基本时间操作
- `TimezoneUtils.now()` - 获取当前时间
- `TimezoneUtils.getTimezone()` - 获取当前时区
- `TimezoneUtils.getTimezoneOffset()` - 获取时区偏移信息

#### 时间格式化
- `TimezoneUtils.formatToLocal(date)` - 格式化为本地时间字符串
- `TimezoneUtils.formatToISO(date)` - 格式化为ISO字符串
- `TimezoneUtils.formatToLocalDate(date)` - 格式化为本地日期（YYYY-MM-DD）
- `TimezoneUtils.formatToLocalTime(date)` - 格式化为本地时间（HH:mm:ss）

#### 时间范围获取
- `TimezoneUtils.getTodayStart()` - 今天开始时间
- `TimezoneUtils.getTodayEnd()` - 今天结束时间
- `TimezoneUtils.getWeekStart()` - 本周开始时间（周一）
- `TimezoneUtils.getWeekEnd()` - 本周结束时间（周日）
- `TimezoneUtils.getMonthStart()` - 本月开始时间
- `TimezoneUtils.getMonthEnd()` - 本月结束时间
- `TimezoneUtils.getYearStart()` - 本年开始时间
- `TimezoneUtils.getYearEnd()` - 本年结束时间

#### 日期计算
- `TimezoneUtils.addDays(date, days)` - 添加天数
- `TimezoneUtils.addHours(date, hours)` - 添加小时
- `TimezoneUtils.daysBetween(date1, date2)` - 计算天数差

#### 日期判断
- `TimezoneUtils.isToday(date)` - 是否为今天
- `TimezoneUtils.isThisWeek(date)` - 是否为本周
- `TimezoneUtils.isThisMonth(date)` - 是否为本月

#### 持续时间格式化
- `TimezoneUtils.formatDuration(milliseconds)` - 格式化持续时间

## 使用示例

### 在业务代码中使用

```typescript
import { TimezoneUtils } from '../utils/timezone.js';

// 获取当前时间
const now = TimezoneUtils.now();

// 格式化时间
const localTime = TimezoneUtils.formatToLocal(now);
console.log(`当前时间: ${localTime}`); // 输出: 当前时间: 2024/06/22 18:30:00

// 获取今天的时间范围
const todayStart = TimezoneUtils.getTodayStart();
const todayEnd = TimezoneUtils.getTodayEnd();

// 查询今天的数据
const todayData = await prisma.project.findMany({
  where: {
    createdAt: {
      gte: todayStart,
      lte: todayEnd
    }
  }
});
```

### 在API响应中使用

```typescript
// 健康检查API中包含时区信息
{
  "status": "healthy",
  "timestamp": "2024-06-22T10:30:00.000Z",
  "timezone": "Asia/Shanghai",
  "localTime": "2024/06/22 18:30:00",
  "database": "connected"
}
```

## 测试验证

### 运行时区测试

```bash
npm run test:timezone
```

测试内容包括：
- 基本时区信息验证
- 时间格式化测试
- 时间范围计算测试
- 日期操作测试
- 数据库时间对比
- 不同时区格式对比

### 健康检查验证

访问健康检查端点验证时区配置：

```bash
curl http://localhost:3000/api/health
```

## 注意事项

### 1. 数据库时间

- 数据库存储的时间为UTC时间
- 应用层负责时区转换
- 查询时需要考虑时区偏移

### 2. 前端时间显示

前端应该使用服务器返回的格式化时间，或者：

```javascript
// 前端时区转换示例
const serverTime = '2024-06-22T10:30:00.000Z';
const localTime = new Date(serverTime).toLocaleString('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit'
});
```

### 3. 日志时间

所有日志都会显示正确的本地时间：

```
2024/06/22 18:30:00 [INFO] 用户登录成功
```

### 4. 定时任务

使用 `node-cron` 的定时任务会按照本地时区执行：

```typescript
import cron from 'node-cron';

// 每天上午9点执行（北京时间）
cron.schedule('0 9 * * *', () => {
  console.log('定时任务执行:', TimezoneUtils.formatToLocal(new Date()));
});
```

## 常见问题

### Q1: 为什么选择 Asia/Shanghai？
A: Asia/Shanghai 是中国标准时间的标准时区标识符，包含夏令时历史信息，比 GMT+8 更准确。

### Q2: 如何修改时区？
A: 修改环境变量 `TIMEZONE` 的值，重新部署应用即可。

### Q3: 数据库中的时间是什么时区？
A: 数据库存储UTC时间，应用层进行时区转换。

### Q4: 如何处理不同用户的时区需求？
A: 可以在用户表中添加时区字段，根据用户偏好进行个性化时区转换。

## 部署注意事项

### Docker部署

确保Dockerfile中包含时区配置：

```dockerfile
# 安装时区数据
RUN apk add --no-cache tzdata

# 设置时区环境变量
ENV TZ=Asia/Shanghai
ENV TIMEZONE=Asia/Shanghai
```

### 服务器部署

确保服务器时区正确：

```bash
# 检查服务器时区
timedatectl

# 设置服务器时区（如果需要）
sudo timedatectl set-timezone Asia/Shanghai
```

## 监控和维护

### 时区配置监控

定期检查时区配置是否正确：

```bash
# 检查应用时区
curl http://your-domain/api/health | jq '.timezone'

# 运行时区测试
npm run test:timezone
```

### 日志监控

监控时间相关的日志，确保时间显示正确：

```bash
# 检查应用日志中的时间格式
docker logs <container_name> | head -10
```
