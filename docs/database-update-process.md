# 数据库字段更新流程

## 概述

本文档描述了在项目管理系统中更新数据库字段的标准流程，以居间费字段的添加为例。

## 更新流程

### 1. 修改 Prisma Schema

首先在 `prisma/schema.prisma` 文件中添加新字段：

```prisma
model Project {
  // ... 其他字段
  
  // 成本信息
  influencerCost            Decimal @db.Decimal(15, 2) // 达人成本
  adCost                    Decimal @db.Decimal(15, 2) // 投流成本
  otherCost                 Decimal @db.Decimal(15, 2) // 其他成本
  intermediaryCost          Decimal @default(0) @db.Decimal(15, 2) // 居间费
  estimatedInfluencerRebate Decimal @db.Decimal(15, 2) // 预估达人返点
  
  // ... 其他字段
}
```

### 2. 生成数据库迁移

运行以下命令生成迁移文件：

```bash
npx prisma migrate dev --name "add_intermediary_cost_field"
```

这会：
- 创建新的迁移文件
- 自动应用迁移到开发数据库
- 生成更新的 Prisma Client

### 3. 生成 Prisma Client

如果需要单独生成客户端：

```bash
npx prisma generate
```

### 4. 更新 TypeScript 类型定义

更新相关的接口定义：

```typescript
// src/types/project.ts
export interface ProjectCost {
  influencerCost: number;      // 达人成本
  adCost: number;             // 投流成本
  otherCost: number;          // 其他成本
  intermediaryCost: number;   // 居间费
  estimatedInfluencerRebate: number; // 预估达人返点
}
```

### 5. 更新 API 验证 Schema

更新控制器中的验证规则：

```typescript
// src/controllers/project.ts
const createProjectSchema = z.object({
  // ... 其他字段
  cost: z.object({
    influencerCost: z.number().min(0, '达人成本不能为负数'),
    adCost: z.number().min(0, '投流成本不能为负数'),
    otherCost: z.number().min(0, '其他成本不能为负数'),
    intermediaryCost: z.number().min(0, '居间费不能为负数').default(0),
    estimatedInfluencerRebate: z.number().min(0, '预估达人返点不能为负数')
  }),
  // ... 其他字段
});
```

### 6. 更新 API 路由定义

更新 Fastify 路由的 schema：

```typescript
// src/routes/project.ts
cost: {
  type: 'object',
  required: ['influencerCost', 'adCost', 'otherCost', 'estimatedInfluencerRebate'],
  properties: {
    influencerCost: { type: 'number', minimum: 0, description: '达人成本' },
    adCost: { type: 'number', minimum: 0, description: '投流成本' },
    otherCost: { type: 'number', minimum: 0, description: '其他成本' },
    intermediaryCost: { type: 'number', minimum: 0, default: 0, description: '居间费' },
    estimatedInfluencerRebate: { type: 'number', minimum: 0, description: '预估达人返点' }
  }
}
```

### 7. 更新业务逻辑

更新所有涉及成本计算的业务逻辑：

```typescript
// 毛利计算
private calculateProfit(budget: Project['budget'], cost: Project['cost']): ProjectProfit {
  const totalCost = cost.influencerCost + cost.adCost + cost.otherCost + cost.intermediaryCost;
  const profit = budget.planningBudget - totalCost + cost.estimatedInfluencerRebate;
  const grossMargin = budget.planningBudget > 0 ? (profit / budget.planningBudget) * 100 : 0;

  return {
    profit: Math.round(profit * 100) / 100,
    grossMargin: Math.round(grossMargin * 100) / 100
  };
}
```

### 8. 更新数据库服务

确保数据库操作包含新字段：

```typescript
// src/services/database.ts
const projectData: Prisma.ProjectCreateInput = {
  // ... 其他字段
  intermediaryCost: new Decimal(data.cost.intermediaryCost || 0),
  // ... 其他字段
};
```

### 9. 验证构建

运行构建命令确保没有类型错误：

```bash
npm run build
```

### 10. 更新测试

更新相关的测试文件：

```typescript
// src/tests/notification.test.ts
cost: {
  influencerCost: 35000,
  adCost: 25000,
  otherCost: 8000,
  intermediaryCost: 5000,
  estimatedInfluencerRebate: 2000
}
```

## 最佳实践

### 字段设计
- **使用描述性的字段名称**
- **设置合理的默认值**
- **选择合适的数据类型**
- **添加必要的约束**

### 迁移管理
- **使用描述性的迁移名称**
- **在迁移中包含必要的注释**
- **测试迁移的回滚操作**
- **考虑大表迁移的性能影响**

### 向后兼容性
- **为新字段提供默认值**
- **保持API的向后兼容**
- **渐进式更新业务逻辑**

## 注意事项

### 开发环境
- ✅ 确保本地数据库连接正常
- ✅ 备份重要的开发数据
- ✅ 测试迁移的正确性

### 生产环境
- ⚠️ **必须备份生产数据库**
- ⚠️ 在维护窗口期间执行迁移
- ⚠️ 准备回滚计划
- ⚠️ 监控迁移执行过程
- ⚠️ 验证迁移后的数据完整性

### 团队协作
- 📢 通知团队成员数据库结构变更
- 📢 更新API文档
- 📢 协调前端代码更新
- 📢 更新部署脚本

## 常用命令

```bash
# 查看迁移状态
npx prisma migrate status

# 生成迁移（开发环境）
npx prisma migrate dev --name "描述性名称"

# 应用迁移（生产环境）
npx prisma migrate deploy

# 重置数据库（仅开发环境）
npx prisma migrate reset

# 从数据库拉取最新结构
npx prisma db pull

# 推送schema到数据库（不生成迁移）
npx prisma db push

# 生成Prisma客户端
npx prisma generate
```

## 故障排除

### 常见问题
1. **迁移失败**: 检查数据库连接和权限
2. **类型错误**: 确保所有相关文件都已更新
3. **默认值问题**: 验证默认值是否符合业务逻辑
4. **性能问题**: 考虑为大表添加索引

### 解决方案
- 查看详细的错误日志
- 检查数据库约束冲突
- 验证数据类型兼容性
- 测试迁移的回滚操作
