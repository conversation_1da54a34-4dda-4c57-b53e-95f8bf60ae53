# 项目收入管理 API 文档

## 概述

项目收入管理模块提供了完整的项目预计收入、实际收入追踪和统计分析功能。支持多种收入类型、状态管理和时间维度的收入预测。

## 数据模型

### 收入状态 (RevenueStatus)
- `receiving` - 收款中
- `received` - 已收款
- `cancelled` - 已取消

### 收入类型 (RevenueType)
- `influencer_income` - 达人收入
- `project_income` - 项目收入
- `other` - 其他收入

### 项目收入对象 (ProjectRevenue)
```typescript
interface ProjectRevenue {
  id: string;                    // 收入ID
  title: string;                 // 收入标题/描述
  revenueType: RevenueType;      // 收入类型
  status: RevenueStatus;         // 收入状态
  plannedAmount: number;         // 预计收入金额
  actualAmount?: number;         // 实际收入金额
  invoiceAmount?: number;        // 开票金额
  plannedDate: Date;             // 预计收入时间
  confirmedDate?: Date;          // 确认收入时间
  invoiceDate?: Date;            // 开票时间
  receivedDate?: Date;           // 实际收款时间
  milestone?: string;            // 里程碑描述
  invoiceNumber?: string;        // 发票号码
  paymentTerms?: string;         // 付款条件
  notes?: string;                // 备注说明
  projectId: string;             // 关联项目ID
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
  createdBy: string;             // 创建者
  updatedBy: string;             // 更新者
}
```

## API 接口

### 1. 创建项目收入

**POST** `/api/projects/{projectId}/revenues`

创建新的项目收入记录。

#### 请求参数

**路径参数:**
- `projectId` (string, required) - 项目ID

**请求体:**
```json
{
  "title": "第一阶段里程碑收入",
  "revenueType": "milestone",
  "plannedAmount": 500000,
  "plannedDate": "2024-03-31",
  "milestone": "项目启动阶段完成",
  "paymentTerms": "收到发票后30天内付款",
  "notes": "项目启动阶段的收入"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": "revenue-001",
    "title": "第一阶段里程碑收入",
    "revenueType": "milestone",
    "status": "planned",
    "plannedAmount": 500000,
    "plannedDate": "2024-03-31T00:00:00.000Z",
    "milestone": "项目启动阶段完成",
    "paymentTerms": "收到发票后30天内付款",
    "notes": "项目启动阶段的收入",
    "projectId": "project-001",
    "createdAt": "2024-01-20T10:00:00.000Z",
    "updatedAt": "2024-01-20T10:00:00.000Z",
    "createdBy": "user-001",
    "updatedBy": "user-001"
  },
  "message": "创建项目收入成功"
}
```

### 2. 获取项目收入列表

**GET** `/api/revenues`

获取项目收入列表，支持分页和过滤。

#### 查询参数
- `page` (number, optional) - 页码，默认1
- `pageSize` (number, optional) - 每页数量，默认20
- `projectId` (string, optional) - 项目ID过滤
- `status` (RevenueStatus, optional) - 收入状态过滤
- `revenueType` (RevenueType, optional) - 收入类型过滤
- `startDate` (string, optional) - 开始日期过滤 (YYYY-MM-DD)
- `endDate` (string, optional) - 结束日期过滤 (YYYY-MM-DD)
- `sortBy` (string, optional) - 排序字段 (plannedDate|plannedAmount|actualAmount|createdAt)
- `sortOrder` (string, optional) - 排序方向 (asc|desc)

#### 响应示例
```json
{
  "success": true,
  "data": {
    "revenues": [
      {
        "id": "revenue-001",
        "title": "第一阶段里程碑收入",
        "revenueType": "milestone",
        "status": "confirmed",
        "plannedAmount": 500000,
        "actualAmount": 500000,
        "plannedDate": "2024-03-31T00:00:00.000Z",
        "confirmedDate": "2024-03-30T00:00:00.000Z",
        "projectId": "project-001"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": "获取项目收入列表成功"
}
```

### 3. 获取单个项目收入

**GET** `/api/revenues/{id}`

获取指定ID的项目收入详情。

#### 路径参数
- `id` (string, required) - 收入ID

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": "revenue-001",
    "title": "第一阶段里程碑收入",
    "revenueType": "milestone",
    "status": "received",
    "plannedAmount": 500000,
    "actualAmount": 500000,
    "invoiceAmount": 500000,
    "plannedDate": "2024-03-31T00:00:00.000Z",
    "confirmedDate": "2024-03-30T00:00:00.000Z",
    "invoiceDate": "2024-04-01T00:00:00.000Z",
    "receivedDate": "2024-04-15T00:00:00.000Z",
    "milestone": "项目启动阶段完成",
    "invoiceNumber": "INV-2024-001",
    "paymentTerms": "收到发票后30天内付款",
    "notes": "项目启动阶段的收入",
    "projectId": "project-001",
    "createdAt": "2024-01-20T10:00:00.000Z",
    "updatedAt": "2024-04-15T14:30:00.000Z",
    "createdBy": "user-001",
    "updatedBy": "user-002"
  },
  "message": "获取项目收入成功"
}
```

### 4. 更新项目收入

**PUT** `/api/revenues/{id}`

更新项目收入信息，包括状态变更、实际金额等。

#### 路径参数
- `id` (string, required) - 收入ID

#### 请求体示例
```json
{
  "status": "invoiced",
  "actualAmount": 500000,
  "invoiceAmount": 500000,
  "invoiceDate": "2024-04-01",
  "invoiceNumber": "INV-2024-001",
  "notes": "已开具发票，等待收款"
}
```

### 5. 删除项目收入

**DELETE** `/api/revenues/{id}`

删除指定的项目收入记录。

#### 路径参数
- `id` (string, required) - 收入ID

#### 响应示例
```json
{
  "success": true,
  "message": "删除项目收入成功"
}
```

### 6. 获取收入统计

**GET** `/api/revenues/stats`

获取收入统计信息，包括总收入、按状态分组、按类型分组等。

#### 响应示例
```json
{
  "success": true,
  "data": {
    "totalPlannedRevenue": 2000000,
    "totalActualRevenue": 1500000,
    "totalInvoicedRevenue": 1200000,
    "totalReceivedRevenue": 1000000,
    "revenueByStatus": [
      {
        "status": "received",
        "count": 5,
        "totalAmount": 1000000
      },
      {
        "status": "invoiced",
        "count": 3,
        "totalAmount": 200000
      }
    ],
    "revenueByType": [
      {
        "type": "milestone",
        "count": 8,
        "totalAmount": 1500000
      },
      {
        "type": "monthly",
        "count": 12,
        "totalAmount": 500000
      }
    ],
    "monthlyRevenueTrend": [
      {
        "month": "2024-01",
        "plannedAmount": 500000,
        "actualAmount": 450000
      },
      {
        "month": "2024-02",
        "plannedAmount": 600000,
        "actualAmount": 550000
      }
    ]
  },
  "message": "获取收入统计成功"
}
```

## 业务流程

### 收入管理流程
1. **创建预计收入** - 项目启动时创建各阶段的预计收入
2. **确认收入** - 达到里程碑或时间节点时确认收入
3. **开票管理** - 向客户开具发票，记录发票信息
4. **收款跟踪** - 跟踪实际收款情况
5. **逾期管理** - 监控逾期未收款的收入

### 状态流转
```
planned → confirmed → invoiced → received
   ↓         ↓          ↓
cancelled  cancelled  overdue
```

## 注意事项

1. **权限控制**: 所有接口都需要JWT认证
2. **数据验证**: 金额字段必须为非负数
3. **日期格式**: 使用ISO 8601格式 (YYYY-MM-DD)
4. **级联删除**: 删除项目时会自动删除相关收入记录
5. **审计日志**: 所有操作都会记录操作者和时间
