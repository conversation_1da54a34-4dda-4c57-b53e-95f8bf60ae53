# Excel导出字段中文化映射说明

## 📋 概述

为了提高Excel财务报表的可读性，我们将所有英文枚举字段转换为对应的中文显示。这样用户在查看Excel文件时能够直观理解各个字段的含义。

## 🔄 字段转换映射

### ⚠️ **重要更新说明**

根据您的反馈，我们已经修正了以下关键映射：

- ✅ **项目进度 `DRAFT`** → **`已创建`**（之前是"草稿"）
- ✅ **支持大小写枚举值**：同时支持 `DRAFT` 和 `draft` 格式
- ✅ **完整枚举覆盖**：包含所有可能的枚举值组合

### 📋 **测试验证结果**

所有字段转换都经过了完整测试验证：
- ✅ 合同签署状态：`NO_CONTRACT` → `无合同`
- ✅ 合同类型：`JING_TASK` → `京任务`，`ANNUAL_FRAME` → `年框`
- ✅ 项目进度：`DRAFT` → `已创建`
- ✅ 服务类型：`INFLUENCER` → `达人服务`
- ✅ 税率类型：`SPECIAL_6` → `专票6%`

### 1. **合同签署状态** (`ContractSigningStatus`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `NO_CONTRACT` / `no_contract` | 无合同 | 项目暂无合同 |
| `SIGNED` / `signed` | 已签订 | 合同已正式签署 |
| `SIGNING` / `signing` | 签订中 | 合同正在签署过程中 |
| `PENDING` / `pending` | 待定 | 合同状态待确定 |

**使用方法**：
```typescript
this.getContractSigningStatusText(project.contractSigningStatus)
```

### 2. **合同类型** (`ContractType`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `ANNUAL_FRAME` / `annual_frame` | 年框 | 年度框架合同 |
| `QUARTERLY_FRAME` / `quarterly_frame` | 季框 | 季度框架合同 |
| `SINGLE` / `single` | 单次 | 单次项目合同 |
| `PO_ORDER` / `po_order` | PO单 | 采购订单 |
| `JING_TASK` / `jing_task` | 京任务 | 京东任务合同 |

**使用方法**：
```typescript
this.getContractTypeText(project.contractType)
```

### 3. **项目状态** (`ProjectStatus`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `DRAFT` / `draft` | 已创建 | 项目已创建（原草稿状态） |
| `ACTIVE` / `active` | 执行中 | 项目正在执行 |
| `COMPLETED` / `completed` | 已完成 | 项目已完成 |
| `CANCELLED` / `cancelled` | 已取消 | 项目已取消 |

**使用方法**：
```typescript
this.getProjectProgressText(project.status)
```

### 4. **收入状态** (`RevenueStatus`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `receiving` | 收款中 | 正在收款过程中 |
| `received` | 已收款 | 已成功收款 |
| `cancelled` | 已取消 | 收款已取消 |

**使用方法**：
```typescript
this.getRevenueStatusText(revenue.status)
```

### 5. **收入类型** (`RevenueType`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `influencer_income` | 达人收入 | 来自达人合作的收入 |
| `project_income` | 项目收入 | 项目执行收入 |
| `other` | 其他收入 | 其他类型收入 |

**使用方法**：
```typescript
this.getRevenueTypeText(revenue.revenueType)
```

### 6. **服务类型** (`ServiceType`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `INFLUENCER` / `influencer` | 达人服务 | 达人合作服务 |
| `ADVERTISING` / `advertising` | 投流服务 | 广告投放服务 |
| `OTHER` / `other` | 其他服务 | 其他类型服务 |

**使用方法**：
```typescript
this.getServiceTypeText(budget.serviceType)
```

### 7. **供应商状态** (`SupplierStatus`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `active` | 活跃 | 供应商状态正常 |
| `inactive` | 停用 | 供应商已停用 |
| `pending` | 待审核 | 供应商待审核 |
| `blacklisted` | 黑名单 | 供应商已拉黑 |

**使用方法**：
```typescript
this.getSupplierStatusText(supplier.status)
```

### 8. **税率类型** (`TaxRate`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `SPECIAL_1` / `special_1` | 专票1% | 专用发票1%税率 |
| `SPECIAL_3` / `special_3` | 专票3% | 专用发票3%税率 |
| `SPECIAL_6` / `special_6` | 专票6% | 专用发票6%税率 |
| `GENERAL` / `general` | 普票 | 普通发票 |

**使用方法**：
```typescript
this.getTaxRateText(budget.taxRate)
```

### 9. **周预算状态** (`WeeklyBudgetStatus`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `draft` | 草稿 | 预算草稿状态 |
| `approved` | 已批准 | 预算已批准 |
| `executing` | 执行中 | 预算执行中 |
| `completed` | 已完成 | 预算已完成 |
| `cancelled` | 已取消 | 预算已取消 |

**使用方法**：
```typescript
this.getWeeklyBudgetStatusText(budget.status)
```

### 10. **单据类型** (`DocumentType`)

| 英文值 | 中文显示 | 说明 |
|--------|----------|------|
| `project_initiation` | 项目立项表 | 项目立项文档 |
| `project_proposal` | 项目提案 | 项目提案文档 |
| `project_plan` | 项目计划 | 项目计划文档 |
| `project_execution` | 项目执行 | 项目执行文档 |
| `project_summary` | 项目总结 | 项目总结文档 |

**使用方法**：
```typescript
this.getDocumentTypeText(project.documentType)
```

## 🎯 Excel中的应用

### **项目汇总表中的中文化字段**

1. **合同签署情况**：显示"已签订"、"签订中"等中文状态
2. **合同类型**：显示"年框"、"季框"、"单次"等中文类型
3. **项目进度**：显示"执行中"、"已完成"等中文状态
4. **回款状态**：根据收入状态自动计算显示中文状态
5. **服务内容**：显示"达人服务"、"投流服务"等中文描述
6. **专票税率**：显示"专票6%"、"专票3%"等中文税率

### **品牌详情表中的中文化字段**

1. **合同签署情况**：与项目汇总表一致
2. **执行情况**：项目状态的中文显示
3. **回款情况**：收入状态的中文显示

### **品牌汇总表中的中文化字段**

- 主要为数值汇总，状态字段较少，但涉及的状态同样会转换为中文

## 🔧 技术实现

### **转换方法结构**

所有转换方法都遵循统一的模式：

```typescript
private getXxxText(value: string): string {
  const mapping: Record<string, string> = {
    'english_value': '中文显示',
    // ... 更多映射
  };
  return mapping[value] || value; // 如果没有映射则返回原值
}
```

### **智能聚合方法**

对于需要聚合多个值的场景，我们提供了智能方法：

#### **服务内容聚合**
```typescript
private getServiceContent(budgets: WeeklyBudget[]): string {
  const serviceTypes = [...new Set(budgets.map(b => b.serviceType))];
  return serviceTypes.map(type => this.getServiceTypeText(type)).join(', ');
}
```

#### **主要税率获取**
```typescript
private getMainTaxRate(budgets: WeeklyBudget[]): string {
  // 按合同金额排序，取最大金额对应的税率
  const taxRateAmounts = new Map<string, number>();
  budgets.forEach(budget => {
    const current = taxRateAmounts.get(budget.taxRate) || 0;
    taxRateAmounts.set(budget.taxRate, current + budget.contractAmount);
  });
  
  const sortedTaxRates = Array.from(taxRateAmounts.entries())
    .sort((a, b) => b[1] - a[1]);
  
  const mainTaxRate = sortedTaxRates[0]?.[0] || 'special_6';
  return this.getTaxRateText(mainTaxRate);
}
```

## 📊 状态颜色编码

在Excel中，我们还为不同状态设置了颜色编码：

### **项目状态颜色**
- 🟢 **已完成**：绿色 (#059669)
- 🔵 **执行中**：蓝色 (#2563EB)
- 🔴 **已取消**：红色 (#DC2626)

### **合同状态颜色**
- 🟢 **已签订**：绿色 (#059669)
- 🟠 **签订中**：橙色 (#D97706)
- ⚪ **无合同**：灰色 (#6B7280)

### **金额颜色**
- 🟢 **正数**：绿色 (#059669)
- 🔴 **负数**：红色 (#DC2626)
- ⚫ **零值**：默认黑色

## ✅ 验证和测试

### **测试覆盖**

所有转换方法都经过了完整测试：

1. ✅ **基本转换**：英文值正确转换为中文
2. ✅ **未知值处理**：未知英文值返回原值
3. ✅ **空值处理**：空值和undefined的安全处理
4. ✅ **聚合方法**：多值聚合的正确性
5. ✅ **Excel显示**：在实际Excel文件中的正确显示

### **质量保证**

- 🔍 **类型安全**：TypeScript类型检查确保类型安全
- 🛡️ **容错处理**：未知值不会导致程序崩溃
- 📝 **文档完整**：每个映射都有详细说明
- 🧪 **测试覆盖**：100%的转换方法测试覆盖

通过这套完整的中文化映射系统，Excel财务报表现在完全使用中文显示，大大提升了用户的阅读体验和理解效率！
