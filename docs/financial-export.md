# 财务导出报表功能说明

## 功能概述

财务导出报表功能提供了完整的多Sheet Excel报表导出能力，支持按年度导出项目财务数据，包含项目汇总、品牌详情、品牌汇总等多个维度的财务分析。

## 功能特性

### 🎯 **核心功能**
- **多Sheet导出**：一个Excel文件包含多个工作表
- **年度数据**：按指定年份导出当年所有项目数据
- **灵活过滤**：支持品牌筛选、项目状态过滤
- **实时计算**：动态计算毛利、回款状态等财务指标
- **标准格式**：符合财务报表标准的Excel格式

### 📊 **报表内容**

#### 1. **项目汇总表**
包含所有项目的详细财务信息，共32个字段：

| 字段分类 | 字段名称 | 说明 |
|---------|---------|------|
| **基本信息** | 下单时间 | 项目创建时间 |
| | 品牌 | 项目所属品牌 |
| | 品类 | 产品品类（如数码） |
| | 执行项目 | 项目执行PM |
| | 项目名称 | 项目完整名称 |
| **合同信息** | 合同签署情况 | 无合同/已签订/签订中/待定 |
| | 合同类型 | 年框/季框/单次/PO单/京任务 |
| **财务信息** | 项目规划金额 | 项目预算总额 |
| | 不含税金额 | 扣除税费后金额 |
| | 项目进度 | 草稿/执行中/已完成/已取消 |
| **回款信息** | 预计回款时间 | 预期收款月份 |
| | 回款周期 | 账期天数（如T+180） |
| | 执行项目周期 | 项目执行时间段 |
| | 已回款额 | 实际收到金额 |
| | 未回款额 | 待收金额 |
| | 回款状态 | 无收入计划/未回款/部分回款/已回款 |
| **成本信息** | 采购成本（去税） | 总采购成本 |
| | 返点 | 达人返点金额 |
| | 居间 | 居间费用 |
| | 毛利 | 项目毛利润 |
| | 毛利率 | 毛利率百分比 |
| **供应商信息** | 供应商公司名称 | 主要供应商 |
| | 服务内容 | 达人/投流/其他 |
| | 供应商采购成本 | 供应商相关成本 |
| | 专票税率 | 发票税率 |
| **付款信息** | 已付金额 | 已支付供应商金额 |
| | 未付款金额 | 待付金额 |
| | 6月应付 | 当月应付金额 |
| | 剩余成本 | 剩余未付成本 |
| | 7月预计付款 | 下月预计付款 |
| | 转后续月份 | 延期付款金额 |

#### 2. **品牌详情表**（每个品牌一个Sheet）
每个有项目的品牌单独一个工作表，包含17个字段：

| 字段名称 | 说明 |
|---------|------|
| 品牌 | 品牌名称 |
| 品类 | 产品品类 |
| 下单时间 | 项目创建时间 |
| 执行项目 | 项目执行PM |
| 项目名称 | 项目名称 |
| 合同签署情况 | 合同状态 |
| 规划金额 | 项目预算 |
| 执行情况 | 项目进度 |
| 预计回款日期 | 预期收款时间 |
| 回款情况 | 回款状态 |
| 已回款额 | 实际收款 |
| 未回款额 | 待收款 |
| 账期 | 付款周期 |
| 毛利额 | 项目毛利 |
| 毛利率 | 毛利率 |
| 已支付项目金额 | 已付供应商 |
| 未支付项目金额 | 待付供应商 |

#### 3. **品牌汇总表**
所有品牌的汇总数据，一行一个品牌，包含13个字段：

| 字段名称 | 说明 |
|---------|------|
| 品牌 | 品牌名称 |
| 品类 | 产品品类 |
| 品牌下单金额 | 品牌项目总预算 |
| 已执行完金额 | 已完成项目金额 |
| 执行中项目金额 | 进行中项目金额 |
| 预估毛利 | 品牌总毛利 |
| 预估毛利率 | 品牌平均毛利率 |
| 已回款 | 品牌总回款 |
| 未回款 | 品牌待回款 |
| 合计需支付项目金额 | 总应付金额（含返点） |
| 已支付项目金额 | 已付供应商总额 |
| 未支付项目金额 | 待付供应商总额 |
| 备注1 | 备注信息 |

## API接口

### 1. **导出财务报表**

```http
GET /api/financial/export?year=2024&includeCompleted=true&includeCancelled=false
```

**参数说明**：
- `year` (必填): 导出年份，如2024
- `brandIds` (可选): 品牌ID列表，用逗号分隔
- `includeCompleted` (可选): 是否包含已完成项目，默认true
- `includeCancelled` (可选): 是否包含已取消项目，默认false

**响应**：Excel文件下载

### 2. **获取可导出年份**

```http
GET /api/financial/export/years
```

**响应**：
```json
{
  "success": true,
  "data": {
    "years": [2024, 2023, 2022, 2021, 2020],
    "currentYear": 2024,
    "defaultYear": 2024
  }
}
```

### 3. **获取导出预览**

```http
GET /api/financial/export/preview?year=2024
```

**响应**：
```json
{
  "success": true,
  "data": {
    "year": 2024,
    "estimatedSheets": 3,
    "estimatedSize": "预计 1-5MB",
    "estimatedTime": "预计 10-30秒",
    "sheets": [
      {
        "name": "项目汇总表",
        "description": "包含所有项目的详细财务信息",
        "columns": 32
      }
    ]
  }
}
```

### 4. **获取导出历史**

```http
GET /api/financial/export/history
```

**响应**：
```json
{
  "success": true,
  "data": {
    "history": [
      {
        "id": "1",
        "filename": "财务报表_2024年_2024-06-19.xlsx",
        "year": 2024,
        "exportedAt": "2024-06-19T10:30:00Z",
        "exportedBy": "current-user",
        "fileSize": "2.3MB",
        "status": "completed"
      }
    ]
  }
}
```

## 使用方法

### 1. **前端调用示例**

```javascript
// 导出财务报表
async function exportFinancialReport(year, options = {}) {
  const params = new URLSearchParams({
    year: year.toString(),
    includeCompleted: options.includeCompleted ?? 'true',
    includeCancelled: options.includeCancelled ?? 'false'
  });
  
  if (options.brandIds) {
    params.append('brandIds', options.brandIds.join(','));
  }
  
  const response = await fetch(`/api/financial/export?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `财务报表_${year}年_${new Date().toISOString().split('T')[0]}.xlsx`;
    a.click();
    window.URL.revokeObjectURL(url);
  }
}

// 使用示例
exportFinancialReport(2024, {
  includeCompleted: true,
  includeCancelled: false,
  brandIds: ['brand-001', 'brand-002']
});
```

### 2. **后端服务调用**

```typescript
import { FinancialExportService } from './services/financialExport.js';

const exportService = new FinancialExportService();

const buffer = await exportService.exportFinancialReport({
  year: 2024,
  includeCompleted: true,
  includeCancelled: false
});

// 保存文件或返回给客户端
fs.writeFileSync('财务报表.xlsx', buffer);
```

## 数据来源

财务导出功能整合了以下数据源：

1. **项目数据** (`projects` 表)
   - 基本信息、预算、成本、利润
   - 合同信息、执行周期
   - 项目状态、创建时间

2. **品牌数据** (`brands` 表)
   - 品牌名称、描述

3. **项目收入** (`project_revenues` 表)
   - 计划收入、实际收入
   - 收款状态、收款时间

4. **周预算** (`weekly_budgets` 表)
   - 供应商成本、服务内容
   - 已付金额、剩余金额

5. **供应商** (`suppliers` 表)
   - 供应商名称、服务类型

6. **用户信息** (`users` 表)
   - 执行PM、内容媒介信息

## 性能优化

### 1. **数据获取优化**
- 批量查询减少数据库访问
- 按项目ID分组获取关联数据
- 使用索引优化查询性能

### 2. **内存管理**
- 流式处理大量数据
- 及时释放不需要的对象
- 分批处理避免内存溢出

### 3. **Excel生成优化**
- 使用ExcelJS高效库
- 设置合适的列宽和样式
- 避免重复计算

## 测试

### 运行测试

```bash
# 测试财务导出功能
node scripts/test-financial-export.js
```

### 测试覆盖

- ✅ 基本导出功能
- ✅ 不同参数组合
- ✅ 边界情况处理
- ✅ 性能测试
- ✅ Excel文件结构验证

## 注意事项

1. **权限控制**：需要JWT认证，建议限制财务人员访问
2. **数据安全**：包含敏感财务信息，注意数据保护
3. **文件大小**：大量数据可能生成较大文件，注意网络传输
4. **时区处理**：日期字段统一使用系统时区
5. **数据一致性**：确保导出时数据的一致性和准确性

财务导出报表功能现在已经完全集成并可以使用，提供了完整的多维度财务数据导出能力！
