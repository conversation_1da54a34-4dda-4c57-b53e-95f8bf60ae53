/**
 * 项目管理系统 API 客户端
 * 
 * 使用方法：
 * 1. 复制此文件到你的前端项目中
 * 2. 创建API客户端实例
 * 3. 调用相应的方法
 * 
 * 示例：
 * const api = new ProjectManagementAPI('http://localhost:3000/api');
 * const projects = await api.getProjects({ page: 1, pageSize: 20 });
 */

class ProjectManagementAPI {
  /**
   * 构造函数
   * @param {string} baseURL - API基础URL
   * @param {string} [token] - 认证令牌
   */
  constructor(baseURL = 'http://localhost:3000/api', token = null) {
    this.baseURL = baseURL;
    this.token = token;
  }

  /**
   * 发送HTTP请求
   * @param {string} endpoint - 接口端点
   * @param {RequestInit} [options] - 请求选项
   * @returns {Promise<any>} 响应数据
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (!data.success) {
        throw new Error(data.message || '请求失败');
      }

      return data.data;
    } catch (error) {
      console.error(`API请求失败 [${options.method || 'GET'} ${url}]:`, error);
      throw error;
    }
  }

  /**
   * 构建查询字符串
   * @param {Object} params - 查询参数
   * @returns {string} 查询字符串
   */
  buildQueryString(params) {
    const searchParams = new URLSearchParams();
    
    Object.entries(params || {}).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });
    
    return searchParams.toString();
  }

  // ==================== 品牌管理 API ====================

  /**
   * 获取品牌列表
   * @param {Object} [params] - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.pageSize=50] - 每页数量
   * @param {string} [params.status] - 品牌状态
   * @param {string} [params.keyword] - 搜索关键字
   * @param {string} [params.sortBy='name'] - 排序字段
   * @param {string} [params.sortOrder='asc'] - 排序方向
   * @returns {Promise<Object>} 品牌列表响应
   */
  async getBrands(params = {}) {
    const query = this.buildQueryString(params);
    return this.request(`/brands?${query}`);
  }

  /**
   * 获取单个品牌
   * @param {string} id - 品牌ID
   * @returns {Promise<Object>} 品牌信息
   */
  async getBrand(id) {
    return this.request(`/brands/${id}`);
  }

  /**
   * 创建品牌
   * @param {Object} data - 品牌数据
   * @param {string} data.name - 品牌名称
   * @param {string} [data.description] - 品牌描述
   * @param {string} [data.logo] - 品牌Logo URL
   * @returns {Promise<Object>} 创建的品牌信息
   */
  async createBrand(data) {
    return this.request('/brands', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * 更新品牌
   * @param {Object} data - 更新数据
   * @param {string} data.id - 品牌ID
   * @param {string} [data.name] - 品牌名称
   * @param {string} [data.description] - 品牌描述
   * @param {string} [data.logo] - 品牌Logo URL
   * @param {string} [data.status] - 品牌状态
   * @returns {Promise<Object>} 更新后的品牌信息
   */
  async updateBrand(data) {
    return this.request('/brands', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  /**
   * 删除品牌
   * @param {string} id - 品牌ID
   * @returns {Promise<void>}
   */
  async deleteBrand(id) {
    return this.request(`/brands/${id}`, {
      method: 'DELETE',
    });
  }

  // ==================== 项目管理 API ====================

  /**
   * 获取项目列表
   * @param {Object} [params] - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.pageSize=20] - 每页数量
   * @param {string} [params.documentType] - 单据类型
   * @param {string} [params.brandId] - 品牌ID
   * @param {string} [params.contractType] - 合同类型
   * @param {string} [params.executorPM] - 执行PM用户ID
   * @param {string} [params.status] - 项目状态
   * @param {string} [params.keyword] - 项目名称关键字
   * @param {string} [params.startDate] - 开始日期 (YYYY-MM-DD)
   * @param {string} [params.endDate] - 结束日期 (YYYY-MM-DD)
   * @param {string} [params.sortBy='createdAt'] - 排序字段
   * @param {string} [params.sortOrder='desc'] - 排序方向
   * @returns {Promise<Object>} 项目列表响应
   */
  async getProjects(params = {}) {
    const query = this.buildQueryString(params);
    return this.request(`/projects?${query}`);
  }

  /**
   * 获取单个项目
   * @param {string} id - 项目ID
   * @returns {Promise<Object>} 项目信息
   */
  async getProject(id) {
    return this.request(`/projects/${id}`);
  }

  /**
   * 创建项目
   * @param {Object} data - 项目数据
   * @param {string} data.documentType - 单据类型
   * @param {string} data.brandId - 品牌ID
   * @param {string} data.projectName - 项目名称
   * @param {Object} data.period - 项目执行周期
   * @param {string} data.period.startDate - 开始日期
   * @param {string} data.period.endDate - 结束日期
   * @param {Object} data.budget - 预算信息
   * @param {number} data.budget.planningBudget - 项目规划预算
   * @param {number} data.budget.influencerBudget - 达人预算
   * @param {number} data.budget.adBudget - 投流预算
   * @param {number} data.budget.otherBudget - 其他预算
   * @param {Object} data.cost - 成本信息
   * @param {number} data.cost.influencerCost - 达人成本
   * @param {number} data.cost.adCost - 投流成本
   * @param {number} data.cost.otherCost - 其他成本
   * @param {number} data.cost.estimatedInfluencerRebate - 预估达人返点
   * @param {string} data.executorPM - 执行PM用户ID
   * @param {string[]} data.contentMediaIds - 内容媒介用户ID列表
   * @param {string} data.contractType - 合同类型
   * @param {string} data.settlementRules - 项目结算规则
   * @param {string} data.kpi - KPI
   * @returns {Promise<Object>} 创建的项目信息
   */
  async createProject(data) {
    return this.request('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * 更新项目
   * @param {Object} data - 更新数据（包含项目ID和要更新的字段）
   * @returns {Promise<Object>} 更新后的项目信息
   */
  async updateProject(data) {
    return this.request('/projects', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  /**
   * 删除项目
   * @param {string} id - 项目ID
   * @returns {Promise<void>}
   */
  async deleteProject(id) {
    return this.request(`/projects/${id}`, {
      method: 'DELETE',
    });
  }

  // ==================== 统计分析 API ====================

  /**
   * 获取项目统计数据
   * @returns {Promise<Object>} 统计数据
   */
  async getProjectStats() {
    return this.request('/projects/stats');
  }

  // ==================== 文件上传 API ====================

  /**
   * 上传文件
   * @param {File} file - 文件对象
   * @returns {Promise<Object>} 上传结果
   */
  async uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    const url = `${this.baseURL}/upload`;
    const headers = {};

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (!data.success) {
        throw new Error(data.message || '文件上传失败');
      }

      return data.data;
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 设置认证令牌
   * @param {string} token - 认证令牌
   */
  setToken(token) {
    this.token = token;
  }

  /**
   * 清除认证令牌
   */
  clearToken() {
    this.token = null;
  }

  /**
   * 计算项目利润
   * @param {Object} budget - 预算信息
   * @param {Object} cost - 成本信息
   * @returns {Object} 利润信息
   */
  static calculateProjectProfit(budget, cost) {
    const totalCost = cost.influencerCost + cost.adCost + cost.otherCost;
    const profit = budget.planningBudget - totalCost + cost.estimatedInfluencerRebate;
    const grossMargin = budget.planningBudget > 0 ? (profit / budget.planningBudget) * 100 : 0;
    
    return {
      profit: Math.round(profit * 100) / 100,
      grossMargin: Math.round(grossMargin * 100) / 100
    };
  }

  /**
   * 格式化金额
   * @param {number} amount - 金额
   * @param {string} [currency='¥'] - 货币符号
   * @returns {string} 格式化后的金额
   */
  static formatCurrency(amount, currency = '¥') {
    return `${currency}${amount.toLocaleString()}`;
  }

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @param {string} [format='YYYY-MM-DD'] - 日期格式
   * @returns {string} 格式化后的日期
   */
  static formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    switch (format) {
      case 'YYYY-MM-DD':
        return `${year}-${month}-${day}`;
      case 'YYYY/MM/DD':
        return `${year}/${month}/${day}`;
      case 'MM/DD/YYYY':
        return `${month}/${day}/${year}`;
      default:
        return d.toLocaleDateString();
    }
  }

  /**
   * 获取项目状态显示文本
   * @param {string} status - 项目状态
   * @returns {Object} 状态信息
   */
  static getProjectStatusInfo(status) {
    const statusMap = {
      'draft': { label: '草稿', color: '#6c757d' },
      'active': { label: '进行中', color: '#28a745' },
      'completed': { label: '已完成', color: '#007bff' },
      'cancelled': { label: '已取消', color: '#dc3545' }
    };
    
    return statusMap[status] || { label: status, color: '#6c757d' };
  }

  /**
   * 获取合同类型显示文本
   * @param {string} contractType - 合同类型
   * @returns {string} 显示文本
   */
  static getContractTypeLabel(contractType) {
    const typeMap = {
      'annual_frame': '年框',
      'quarterly_frame': '季框',
      'single': '单次',
      'po_order': 'PO单',
      'jing_task': '京任务'
    };
    
    return typeMap[contractType] || contractType;
  }
}

// ==================== 使用示例 ====================

/**
 * 使用示例
 */
async function example() {
  // 创建API客户端实例
  const api = new ProjectManagementAPI('http://localhost:3000/api');

  try {
    // 获取品牌列表
    const brands = await api.getBrands({ page: 1, pageSize: 10 });
    console.log('品牌列表:', brands);

    // 创建新品牌
    const newBrand = await api.createBrand({
      name: '新品牌',
      description: '这是一个新品牌'
    });
    console.log('创建的品牌:', newBrand);

    // 获取项目列表
    const projects = await api.getProjects({
      page: 1,
      pageSize: 20,
      status: 'active'
    });
    console.log('项目列表:', projects);

    // 创建新项目
    const newProject = await api.createProject({
      documentType: 'project_initiation',
      brandId: newBrand.id,
      projectName: '新项目',
      period: {
        startDate: '2024-02-01',
        endDate: '2024-02-29'
      },
      budget: {
        planningBudget: 100000,
        influencerBudget: 40000,
        adBudget: 30000,
        otherBudget: 10000
      },
      cost: {
        influencerCost: 35000,
        adCost: 28000,
        otherCost: 8000,
        estimatedInfluencerRebate: 2000
      },
      executorPM: 'user-001',
      contentMediaIds: ['user-002'],
      contractType: 'single',
      settlementRules: '按月结算',
      kpi: '目标曝光量1000万'
    });
    console.log('创建的项目:', newProject);

    // 获取统计数据
    const stats = await api.getProjectStats();
    console.log('统计数据:', stats);

    // 计算项目利润
    const profit = ProjectManagementAPI.calculateProjectProfit(
      newProject.budget,
      newProject.cost
    );
    console.log('项目利润:', profit);

  } catch (error) {
    console.error('API调用失败:', error.message);
  }
}

// 如果在Node.js环境中，导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProjectManagementAPI;
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
  window.ProjectManagementAPI = ProjectManagementAPI;
}
