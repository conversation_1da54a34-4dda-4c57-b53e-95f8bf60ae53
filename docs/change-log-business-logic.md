# 项目变更记录业务逻辑说明

## 概述

项目变更记录功能为项目管理系统提供完整的操作审计能力，自动记录项目的所有变更操作，确保数据的可追溯性和合规性。

## 核心业务逻辑

### 1. 智能字段对比机制

#### 问题背景
在项目更新时，用户可能提交某个字段的值，但该值与原值相同（如从1更新为1）。传统做法可能会错误地将此类操作记录为"变更"，导致变更记录不准确。

#### 解决方案
实现智能字段对比算法，使用深度比较来检测真正的字段变更：

```typescript
// 核心对比逻辑
if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
  changedFields.push(key);
}
```

#### 业务价值
- ✅ **精确记录**: 只记录真正发生变化的字段
- ✅ **避免噪音**: 排除"伪变更"，提高记录质量
- ✅ **支持各种数据类型**: 字符串、数字、布尔值、对象、数组

### 2. 变更类型分类

#### CREATE（创建）
- **触发时机**: 项目首次创建时
- **记录内容**: 所有设置的业务字段
- **changedFields**: 包含所有非空/非默认值的字段
- **业务意义**: 记录项目的初始状态

```typescript
// 示例：创建项目时记录的字段
changedFields: [
  'documentType', 'brandId', 'projectName', 'period', 
  'budget', 'cost', 'executorPM', 'contentMediaIds', 
  'contractType', 'settlementRules', 'kpi'
]
```

#### UPDATE（更新）
- **触发时机**: 项目信息修改时
- **记录内容**: 只记录实际发生变化的字段
- **changedFields**: 通过智能对比算法确定
- **业务意义**: 追踪项目演变过程

```typescript
// 示例：只更新项目名称和预算时
changedFields: ['projectName', 'planningBudget']
// 注意：未变更的字段不会被记录
```

#### DELETE（删除）
- **触发时机**: 项目删除时
- **记录内容**: 删除前的完整项目数据
- **changedFields**: 空数组（删除操作不涉及字段变更）
- **业务意义**: 保留删除记录，支持数据恢复

### 3. 系统字段排除机制

#### 排除字段列表
```typescript
const excludeFields = new Set([
  'id',         // 主键ID
  'createdAt',  // 创建时间
  'updatedAt',  // 更新时间
  'createdBy',  // 创建人
  'updatedBy'   // 更新人
]);
```

#### 排除原因
- **系统维护**: 这些字段由系统自动维护
- **非业务变更**: 不代表实际的业务逻辑变更
- **减少噪音**: 避免每次更新都记录时间戳变更

### 4. 操作信息记录

#### 记录内容
- **操作人员**: 从JWT token获取用户ID和姓名
- **操作环境**: IP地址、用户代理信息
- **操作时间**: 精确到毫秒的时间戳
- **操作原因**: 可选的变更原因说明

#### 数据结构
```typescript
{
  operatorId: 'user-001',           // 钉钉用户ID
  operatorName: '张三',             // 用户姓名
  operatorIP: '*************',     // IP地址
  userAgent: 'Mozilla/5.0...',     // 浏览器信息
  createdAt: '2024-01-15T10:30:00Z' // 操作时间
}
```

## 实际应用场景

### 场景1：项目预算调整
```
用户操作：将项目预算从100,000调整为150,000
系统记录：
- changeType: UPDATE
- changedFields: ['planningBudget']
- beforeData: { planningBudget: 100000 }
- afterData: { planningBudget: 150000 }
```

### 场景2：提交相同值
```
用户操作：将项目预算从100,000"更新"为100,000
系统记录：无记录（因为值未实际变更）
changedFields: [] （空数组，不会创建变更记录）
```

### 场景3：批量字段更新
```
用户操作：同时更新项目名称、预算、执行PM
实际变更：只有项目名称和预算发生变化，执行PM保持不变
系统记录：
- changedFields: ['projectName', 'planningBudget']
- 不包含executorPM（因为值未变更）
```

## 业务价值

### 1. 合规审计
- **操作追溯**: 完整记录谁在什么时候做了什么操作
- **责任明确**: 每次变更都有明确的操作人员
- **证据保全**: 保留变更前后的完整数据

### 2. 数据质量
- **精确记录**: 只记录真正的变更，避免噪音
- **完整性**: 涵盖项目生命周期的所有关键操作
- **一致性**: 统一的记录格式和标准

### 3. 业务洞察
- **变更频率**: 分析项目变更的频率和模式
- **热点字段**: 识别经常变更的字段
- **操作习惯**: 了解用户的操作行为

### 4. 风险控制
- **异常检测**: 识别异常的变更操作
- **权限审计**: 验证操作权限的合规性
- **数据恢复**: 支持基于历史记录的数据恢复

## 技术特性

### 1. 性能优化
- **智能对比**: 只在有实际变更时才创建记录
- **索引优化**: 针对常用查询场景创建复合索引
- **分页查询**: 避免大量数据的性能问题

### 2. 扩展性
- **灵活查询**: 支持多维度的过滤和排序
- **数据格式**: 使用JSON存储复杂的变更详情
- **类型安全**: 完整的TypeScript类型定义

### 3. 可维护性
- **清晰架构**: 分层设计，职责明确
- **完整测试**: 覆盖各种边界情况
- **详细文档**: 完整的API文档和使用指南

## 最佳实践

### 1. 查询优化
- 使用项目ID + 时间范围进行查询
- 避免不带条件的全表扫描
- 合理设置分页大小

### 2. 数据管理
- 定期归档历史变更记录
- 监控存储空间使用情况
- 设置合理的数据保留策略

### 3. 安全考虑
- 确保JWT认证的有效性
- 记录操作人员的完整信息
- 保护敏感数据的访问权限

这套业务逻辑确保了项目变更记录的准确性、完整性和实用性，为项目管理提供了强有力的审计支持。
