# 财务报表 API 文档

## 概述

财务报表功能提供品牌级别的财务数据汇总和详细分析，帮助管理层了解各品牌的财务执行情况。

## 数据模型

### 品牌财务汇总 (BrandFinancialSummary)

```typescript
interface BrandFinancialSummary {
  brandId: string;                  // 品牌ID
  brandName: string;                // 品牌名称
  orderAmount: number;              // 品牌下单金额 (项目规划预算总和)
  executedAmount: number;           // 已执行金额 (已完成项目的实际支出)
  executingAmount: number;          // 执行中项目金额 (进行中项目的预算)
  estimatedProfit: number;          // 预估毛利 (预计利润总和)
  estimatedProfitMargin: number;    // 预估毛利率 (预估毛利/品牌下单金额)
  receivedAmount: number;           // 已回款 (已收到的收入)
  unreceiviedAmount: number;        // 未回款 (计划收入-已回款)
  paidProjectAmount: number;        // 已支付项目金额 (周预算中已支付金额总和)
  unpaidProjectAmount: number;      // 未支付项目金额 (周预算中未支付金额总和)
  remarks?: string;                 // 备注字段
  projectCount: number;             // 项目数量
  activeProjectCount: number;       // 进行中项目数量
  completedProjectCount: number;    // 已完成项目数量
}
```

### 财务报表响应 (FinancialReportResponse)

```typescript
interface FinancialReportResponse {
  summary: {
    totalBrands: number;            // 总品牌数
    totalOrderAmount: number;       // 总下单金额
    totalExecutedAmount: number;    // 总已执行金额
    totalEstimatedProfit: number;   // 总预估毛利
    totalReceivedAmount: number;    // 总已回款
    totalPaidAmount: number;        // 总已支付金额
    overallProfitMargin: number;    // 整体毛利率
  };
  brands: BrandFinancialSummary[];  // 品牌财务汇总列表
  generatedAt: Date;                // 报表生成时间
  reportPeriod: {
    startDate?: Date;               // 报表开始日期
    endDate?: Date;                 // 报表结束日期
  };
}
```

## API 接口

### 1. 获取品牌财务汇总报表

**GET** `/api/financial/brands/summary`

获取所有品牌的财务汇总数据，支持时间范围和项目状态过滤。

#### 查询参数

- `brandId` (string, optional) - 品牌ID过滤，指定特定品牌
- `startDate` (string, optional) - 开始日期 (YYYY-MM-DD格式)
- `endDate` (string, optional) - 结束日期 (YYYY-MM-DD格式)
- `projectStatus` (array, optional) - 项目状态过滤，可选值：['draft', 'active', 'completed', 'cancelled']
- `includeCompleted` (string, optional) - 是否包含已完成项目 ('true'/'false')
- `includeCancelled` (string, optional) - 是否包含已取消项目 ('true'/'false')

#### 响应示例

```json
{
  "success": true,
  "data": {
    "summary": {
      "totalBrands": 3,
      "totalOrderAmount": 5800000,
      "totalExecutedAmount": 3200000,
      "totalEstimatedProfit": 1600000,
      "totalReceivedAmount": 2800000,
      "totalPaidAmount": 2900000,
      "overallProfitMargin": 27.6
    },
    "brands": [
      {
        "brandId": "brand-001",
        "brandName": "品牌A",
        "orderAmount": 1800000,
        "executedAmount": 1200000,
        "executingAmount": 600000,
        "estimatedProfit": 540000,
        "estimatedProfitMargin": 30.0,
        "receivedAmount": 1000000,
        "unreceiviedAmount": 800000,
        "paidProjectAmount": 900000,
        "unpaidProjectAmount": 900000,
        "projectCount": 6,
        "activeProjectCount": 4,
        "completedProjectCount": 2
      }
    ],
    "generatedAt": "2024-12-16T10:30:00.000Z",
    "reportPeriod": {
      "startDate": "2024-12-16T00:00:00.000Z",
      "endDate": "2025-06-16T23:59:59.999Z"
    }
  },
  "message": "获取品牌财务汇总报表成功"
}
```

### 2. 获取品牌财务详细报表

**GET** `/api/financial/brands/{brandId}/detail`

获取指定品牌的详细财务数据，包括项目明细、收入分析和成本分析。

#### 路径参数

- `brandId` (string, required) - 品牌ID

#### 查询参数

- `startDate` (string, optional) - 开始日期 (YYYY-MM-DD格式)
- `endDate` (string, optional) - 结束日期 (YYYY-MM-DD格式)
- `projectStatus` (array, optional) - 项目状态过滤
- `includeCompleted` (string, optional) - 是否包含已完成项目
- `includeCancelled` (string, optional) - 是否包含已取消项目

#### 响应示例

```json
{
  "success": true,
  "data": {
    "brandInfo": {
      "id": "brand-001",
      "name": "品牌A",
      "description": "重点客户，我们执行毛利",
      "logo": "https://example.com/logo.png"
    },
    "summary": {
      "brandId": "brand-001",
      "brandName": "品牌A",
      "orderAmount": 1800000,
      "executedAmount": 1200000,
      "executingAmount": 600000,
      "estimatedProfit": 540000,
      "estimatedProfitMargin": 30.0,
      "receivedAmount": 1000000,
      "unreceiviedAmount": 800000,
      "paidProjectAmount": 900000,
      "unpaidProjectAmount": 900000,
      "projectCount": 6,
      "activeProjectCount": 4,
      "completedProjectCount": 2
    },
    "projects": [
      {
        "id": "project-001",
        "projectName": "春节营销活动",
        "status": "active",
        "documentType": "PROJECT_EXECUTION",
        "contractType": "ANNUAL_FRAME",
        "period": {
          "startDate": "2024-02-01T00:00:00.000Z",
          "endDate": "2024-02-29T23:59:59.999Z"
        },
        "budget": {
          "planningBudget": 1000000,
          "totalBudget": 1800000
        },
        "cost": {
          "totalCost": 710000,
          "estimatedInfluencerRebate": 20000
        },
        "profit": {
          "profit": 310000,
          "grossMargin": 31.0
        },
        "revenue": {
          "plannedAmount": 1000000,
          "receivedAmount": 500000,
          "unreceiviedAmount": 500000
        },
        "weeklyBudgets": {
          "totalContractAmount": 800000,
          "paidAmount": 400000,
          "unpaidAmount": 400000
        },
        "executorPM": "user-001",
        "executorPMInfo": {
          "userid": "user-001",
          "name": "张三",
          "department": "营销部"
        }
      }
    ],
    "revenueAnalysis": {
      "totalPlannedRevenue": 1800000,
      "totalReceivedRevenue": 1000000,
      "revenueByStatus": [
        {
          "status": "received",
          "count": 2,
          "totalAmount": 1000000
        },
        {
          "status": "planned",
          "count": 4,
          "totalAmount": 800000
        }
      ],
      "monthlyTrend": [
        {
          "month": "2024-12",
          "plannedAmount": 500000,
          "receivedAmount": 300000
        }
      ]
    },
    "costAnalysis": {
      "totalWeeklyBudgets": 15,
      "totalPaidAmount": 900000,
      "totalUnpaidAmount": 900000,
      "budgetsByServiceType": [
        {
          "serviceType": "influencer",
          "count": 8,
          "totalAmount": 1200000,
          "paidAmount": 600000
        },
        {
          "serviceType": "advertising",
          "count": 5,
          "totalAmount": 500000,
          "paidAmount": 250000
        }
      ]
    }
  },
  "message": "获取品牌财务详细报表成功"
}
```

## 数据计算说明

### 财务指标计算方式

1. **品牌下单金额** = 该品牌所有项目的规划预算总和
2. **已执行金额** = 状态为"已完成"的项目实际支出总和
3. **执行中项目金额** = 状态为"进行中"的项目预算总和
4. **预估毛利** = 所有项目的预计利润总和
5. **预估毛利率** = 预估毛利 ÷ 品牌下单金额 × 100%
6. **已回款** = 项目收入表中状态为"已收款"的金额总和
7. **未回款** = 计划收入总和 - 已回款
8. **已支付项目金额** = 周预算表中已支付金额总和
9. **未支付项目金额** = 周预算表中未支付金额总和

### 项目利润计算

项目利润 = 项目规划预算 - (达人成本 + 投流成本 + 其他成本) + 预估达人返点

### 使用场景

1. **管理层决策** - 通过汇总报表了解各品牌整体财务表现
2. **品牌分析** - 通过详细报表深入分析特定品牌的执行情况
3. **财务监控** - 实时监控回款情况和支付进度
4. **绩效评估** - 基于毛利率等指标评估项目和品牌绩效

## 权限要求

- 需要有效的JWT认证token
- 建议限制为财务人员和管理层访问
