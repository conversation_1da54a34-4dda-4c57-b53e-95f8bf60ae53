# 钉钉用户字段完整映射

## 概述

本文档详细说明了钉钉用户详情API返回的所有字段，以及它们在本地数据库中的映射关系。

## 钉钉API字段 vs 数据库字段映射

| 钉钉API字段 | 数据库字段 | 类型 | 说明 | 是否必填 |
|------------|-----------|------|------|---------|
| userid | userid | string | 钉钉用户ID（主键） | ✅ |
| unionid | unionid | string | 员工在当前开发者企业账号范围内的唯一标识 | ❌ |
| name | name | string | 员工名称 | ✅ |
| avatar | avatar | string | 头像URL | ❌ |
| state_code | stateCode | string | 国际电话区号 | ❌ |
| manager_userid | managerUserid | string | 员工的直属主管userid | ❌ |
| mobile | mobile | string | 手机号码 | ❌ |
| hide_mobile | hideMobile | boolean | 是否隐藏手机号 | ❌ |
| telephone | telephone | string | 分机号 | ❌ |
| job_number | jobNumber | string | 员工工号 | ❌ |
| title | title | string | 职位 | ❌ |
| email | email | string | 员工邮箱 | ❌ |
| work_place | workPlace | string | 办公地点 | ❌ |
| remark | remark | string | 备注 | ❌ |
| login_id | loginId | string | 专属帐号登录名 | ❌ |
| exclusive_account_type | exclusiveAccountType | string | 专属帐号类型 | ❌ |
| exclusive_account | exclusiveAccount | boolean | 是否专属帐号 | ❌ |
| dept_id_list | deptIdList | number[] | 所属部门ID列表 | ❌ |
| extension | extension | string | 扩展属性 | ❌ |
| hired_date | hiredDate | Date | 入职时间（Unix时间戳转换） | ❌ |
| active | active | boolean | 是否激活了钉钉 | ❌ |
| real_authed | realAuthed | boolean | 是否完成了实名认证 | ❌ |
| org_email | orgEmail | string | 员工的企业邮箱 | ❌ |
| org_email_type | orgEmailType | string | 员工的企业邮箱类型 | ❌ |
| senior | senior | boolean | 是否为企业的高管 | ❌ |
| admin | admin | boolean | 是否为企业的管理员 | ❌ |
| boss | boss | boolean | 是否为企业的老板 | ❌ |

## 特殊字段说明

### 1. dept_order_list（部门排序）
钉钉API返回的 `dept_order_list` 字段包含员工在对应部门中的排序信息：
```json
{
  "dept_order_list": [
    {
      "dept_id": 1,
      "order": 100
    }
  ]
}
```
**注意：** 当前版本暂未存储此字段，如需要可以扩展数据库表结构。

### 2. leader_in_dept（部门领导）
钉钉API返回的 `leader_in_dept` 字段表示员工在对应部门中是否为领导：
```json
{
  "leader_in_dept": [
    {
      "dept_id": 1,
      "leader": true
    }
  ]
}
```
**注意：** 当前版本暂未存储此字段，如需要可以扩展数据库表结构。

### 3. role_list（角色列表）
钉钉API返回的 `role_list` 字段包含用户的角色信息：
```json
{
  "role_list": [
    {
      "id": 123,
      "name": "管理员",
      "group_name": "系统角色"
    }
  ]
}
```
**注意：** 当前版本暂未存储此字段，如需要可以扩展数据库表结构。

### 4. union_emp_ext（关联组织信息）
钉钉API返回的 `union_emp_ext` 字段包含关联组织的信息：
```json
{
  "union_emp_ext": {
    "userid": "user123",
    "corp_id": "corp456",
    "union_emp_map_list": [
      {
        "userid": "user789",
        "corp_id": "corp101"
      }
    ]
  }
}
```
**注意：** 当前版本暂未存储此字段，如需要可以扩展数据库表结构。

## 数据转换规则

### 1. 时间字段转换
- `hired_date`: 从Unix时间戳（毫秒）转换为JavaScript Date对象
- `lastSyncAt`: 系统自动生成的同步时间

### 2. 布尔字段默认值
- `hideMobile`: 默认 `false`
- `exclusiveAccount`: 默认 `false`
- `active`: 默认 `true`（如果API返回undefined）
- `realAuthed`: 默认 `false`
- `senior`: 默认 `false`
- `admin`: 默认 `false`
- `boss`: 默认 `false`

### 3. 数组字段处理
- `deptIdList`: 直接存储为PostgreSQL的整数数组类型

### 4. 字符串字段处理
- 空字符串转换为 `undefined`，在数据库中存储为 `NULL`
- 超长字符串会被数据库字段长度限制截断

## 第三方企业应用限制

根据钉钉官方文档，第三方企业应用无法获取以下字段：
- `state_code` (国际电话区号)
- `mobile` (手机号码)
- `telephone` (分机号)
- `email` (员工邮箱)
- `work_place` (办公地点)
- `remark` (备注)
- `extension` (扩展属性)
- `hired_date` (入职时间)
- `org_email` (企业邮箱)

## 使用示例

### 1. 获取完整用户信息
```typescript
const userDetail = await dingTalkService.getUserDetail('user123');
const userData = {
  userid: userDetail.userid,
  unionid: userDetail.unionid || undefined,
  name: userDetail.name,
  avatar: userDetail.avatar || undefined,
  // ... 其他字段映射
};
await databaseService.upsertUser(userData);
```

### 2. 查询用户信息
```typescript
const user = await databaseService.getUser('user123');
console.log('用户信息:', {
  姓名: user.name,
  职位: user.title,
  部门: user.deptIdList?.join(', '),
  手机: user.mobile,
  邮箱: user.email,
  入职时间: user.hiredDate,
  是否管理员: user.admin,
  是否高管: user.senior
});
```

### 3. 搜索用户
```typescript
// 按姓名搜索
const users = await prisma.user.findMany({
  where: {
    name: { contains: '张三', mode: 'insensitive' }
  }
});

// 按部门搜索
const deptUsers = await prisma.user.findMany({
  where: {
    deptIdList: { has: 123 }
  }
});

// 按角色搜索
const admins = await prisma.user.findMany({
  where: { admin: true }
});
```

## 扩展建议

如果需要存储更多钉钉用户信息，可以考虑以下扩展：

1. **部门排序信息表**
```sql
CREATE TABLE user_dept_orders (
  userid VARCHAR(50),
  dept_id INTEGER,
  order_value INTEGER,
  PRIMARY KEY (userid, dept_id)
);
```

2. **用户角色信息表**
```sql
CREATE TABLE user_roles (
  id SERIAL PRIMARY KEY,
  userid VARCHAR(50),
  role_id INTEGER,
  role_name VARCHAR(100),
  group_name VARCHAR(100)
);
```

3. **关联组织信息表**
```sql
CREATE TABLE user_union_orgs (
  id SERIAL PRIMARY KEY,
  userid VARCHAR(50),
  union_userid VARCHAR(50),
  corp_id VARCHAR(50)
);
```

这样可以保持主用户表的简洁性，同时支持更复杂的查询需求。
