# 数据库设置指南

## 🎯 推荐方案：PostgreSQL + Prisma

### 1. 安装依赖

```bash
# 安装Prisma和PostgreSQL驱动
npm install prisma @prisma/client
npm install pg @types/pg

# 初始化Prisma
npx prisma init
```

### 2. 配置数据库连接

创建 `.env` 文件：
```env
# 数据库连接
DATABASE_URL="postgresql://username:password@localhost:5432/project_management?schema=public"

# 钉钉配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_CORP_ID=your_corp_id
DINGTALK_AGENT_ID=your_agent_id
```

### 3. Prisma Schema 设计

创建 `prisma/schema.prisma`：

```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 品牌库表
model Brand {
  id          String      @id @default(cuid())
  name        String      @unique @db.VarChar(100)
  description String?     @db.Text
  logo        String?     @db.VarChar(500)
  status      BrandStatus @default(ACTIVE)

  // 关联项目
  projects    Project[]

  // 审计字段
  createdAt   DateTime    @default(now()) @db.Timestamptz
  updatedAt   DateTime    @updatedAt @db.Timestamptz
  createdBy   String      @db.VarChar(50)

  @@map("brands")
  @@index([status])
  @@index([name])
}

// 项目主表
model Project {
  id             String        @id @default(cuid())

  // 基本信息
  documentType   DocumentType  @default(PROJECT_INITIATION)
  brandId        String
  projectName    String        @db.VarChar(200)

  // 项目执行周期
  startDate      DateTime      @db.Date
  endDate        DateTime      @db.Date

  // 预算信息 (使用Decimal确保金额精度)
  planningBudget    Decimal     @db.Decimal(15, 2) // 项目规划预算
  influencerBudget  Decimal     @db.Decimal(15, 2) // 达人预算
  adBudget         Decimal     @db.Decimal(15, 2) // 投流预算
  otherBudget      Decimal     @db.Decimal(15, 2) // 其他预算

  // 成本信息
  influencerCost              Decimal  @db.Decimal(15, 2) // 达人成本
  adCost                     Decimal  @db.Decimal(15, 2) // 投流成本
  otherCost                  Decimal  @db.Decimal(15, 2) // 其他成本
  estimatedInfluencerRebate  Decimal  @db.Decimal(15, 2) // 预估达人返点

  // 计算字段 (通过数据库视图或应用层计算)
  // 项目利润 = 项目规划预算 - (达人成本 + 投流成本 + 其他成本) + 预估达人返点
  // 项目毛利 = 项目利润 / 项目规划预算

  // 人员信息
  executorPM       String      @db.VarChar(50)  // 执行PM (钉钉用户ID)
  contentMediaIds  String[]                     // 内容媒介 (钉钉用户ID数组)

  // 合同信息
  contractType     ContractType                 // 合同类型
  settlementRules  String      @db.Text         // 项目结算规则 (富文本)
  kpi             String      @db.Text         // KPI (富文本)

  // 项目状态
  status          ProjectStatus @default(DRAFT)

  // 关联关系
  brand           Brand        @relation(fields: [brandId], references: [id], onDelete: Restrict)
  attachments     Attachment[] // 项目附件

  // 审计字段
  createdAt       DateTime     @default(now()) @db.Timestamptz
  updatedAt       DateTime     @updatedAt @db.Timestamptz
  createdBy       String       @db.VarChar(50)
  updatedBy       String       @db.VarChar(50)

  @@map("projects")
  @@index([brandId, status])
  @@index([executorPM])
  @@index([contractType])
  @@index([startDate, endDate])
  @@index([createdAt])
  @@index([status])
}

// 项目附件表
model Attachment {
  id           String   @id @default(cuid())
  filename     String   @db.VarChar(255)     // 存储文件名
  originalName String   @db.VarChar(255)     // 原始文件名
  size         BigInt                        // 文件大小 (字节)
  mimeType     String   @db.VarChar(100)     // MIME类型
  url          String   @db.VarChar(500)     // 文件访问URL

  // 关联项目
  projectId    String
  project      Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 上传信息
  uploadedBy   String   @db.VarChar(50)      // 上传者 (钉钉用户ID)
  uploadedAt   DateTime @default(now()) @db.Timestamptz

  @@map("attachments")
  @@index([projectId])
  @@index([uploadedBy])
}

// 用户信息缓存表 (缓存钉钉用户信息)
model User {
  userid      String    @id @db.VarChar(50)  // 钉钉用户ID
  name        String    @db.VarChar(50)      // 用户姓名
  avatar      String?   @db.VarChar(500)     // 头像URL
  mobile      String?   @db.VarChar(20)      // 手机号
  email       String?   @db.VarChar(100)     // 邮箱
  department  String[]                       // 部门列表
  position    String?   @db.VarChar(50)      // 职位
  jobNumber   String?   @db.VarChar(20)      // 工号

  // 同步信息
  lastSyncAt  DateTime  @default(now()) @db.Timestamptz
  isActive    Boolean   @default(true)       // 是否激活

  @@map("users")
  @@index([name])
  @@index([isActive])
}

// 项目统计视图 (用于快速查询统计数据)
// 这个视图会在迁移时创建
// CREATE VIEW project_stats AS
// SELECT
//   p.id,
//   p.project_name,
//   p.brand_id,
//   b.name as brand_name,
//   p.planning_budget,
//   (p.planning_budget - (p.influencer_cost + p.ad_cost + p.other_cost) + p.estimated_influencer_rebate) as profit,
//   CASE
//     WHEN p.planning_budget > 0 THEN
//       ((p.planning_budget - (p.influencer_cost + p.ad_cost + p.other_cost) + p.estimated_influencer_rebate) / p.planning_budget * 100)
//     ELSE 0
//   END as gross_margin,
//   p.status,
//   p.contract_type,
//   p.start_date,
//   p.end_date,
//   p.created_at
// FROM projects p
// LEFT JOIN brands b ON p.brand_id = b.id;

// 枚举定义
enum BrandStatus {
  ACTIVE    // 启用
  INACTIVE  // 禁用

  @@map("brand_status")
}

enum DocumentType {
  PROJECT_INITIATION  // 项目立项表

  @@map("document_type")
}

enum ContractType {
  ANNUAL_FRAME     // 年框
  QUARTERLY_FRAME  // 季框
  SINGLE          // 单次
  PO_ORDER        // PO单
  JING_TASK       // 京任务

  @@map("contract_type")
}

enum ProjectStatus {
  DRAFT      // 草稿
  ACTIVE     // 进行中
  COMPLETED  // 已完成
  CANCELLED  // 已取消

  @@map("project_status")
}

### 4. 数据库迁移

```bash
# 生成迁移文件
npx prisma migrate dev --name init

# 生成Prisma客户端
npx prisma generate
```

### 5. 数据库服务类

创建 `src/services/database.ts`：

```typescript
import { PrismaClient, Prisma, Decimal } from '@prisma/client';
import {
  Project,
  Brand,
  ProjectQueryParams,
  BrandQueryParams,
  ProjectStats,
  CreateProjectRequest,
  UpdateProjectRequest,
  CreateBrandRequest,
  UpdateBrandRequest
} from '../types/project.js';

export class DatabaseService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  // 项目管理
  async createProject(data: CreateProjectRequest, createdBy: string) {
    const projectData: Prisma.ProjectCreateInput = {
      documentType: data.documentType,
      projectName: data.projectName,
      startDate: new Date(data.period.startDate),
      endDate: new Date(data.period.endDate),
      planningBudget: new Decimal(data.budget.planningBudget),
      influencerBudget: new Decimal(data.budget.influencerBudget),
      adBudget: new Decimal(data.budget.adBudget),
      otherBudget: new Decimal(data.budget.otherBudget),
      influencerCost: new Decimal(data.cost.influencerCost),
      adCost: new Decimal(data.cost.adCost),
      otherCost: new Decimal(data.cost.otherCost),
      estimatedInfluencerRebate: new Decimal(data.cost.estimatedInfluencerRebate),
      executorPM: data.executorPM,
      contentMediaIds: data.contentMediaIds,
      contractType: data.contractType,
      settlementRules: data.settlementRules,
      kpi: data.kpi,
      createdBy,
      updatedBy: createdBy,
      brand: {
        connect: { id: data.brandId }
      }
    };

    return this.prisma.project.create({
      data: projectData,
      include: {
        brand: true,
        attachments: true,
      },
    });
  }

  async getProjects(params: ProjectQueryParams = {}) {
    const {
      page = 1,
      pageSize = 20,
      documentType,
      brandId,
      contractType,
      executorPM,
      status,
      keyword,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    // 构建查询条件
    const where: Prisma.ProjectWhereInput = {};

    if (documentType) where.documentType = documentType;
    if (brandId) where.brandId = brandId;
    if (contractType) where.contractType = contractType;
    if (executorPM) where.executorPM = executorPM;
    if (status) where.status = status;
    if (keyword) {
      where.projectName = {
        contains: keyword,
        mode: 'insensitive'
      };
    }
    if (startDate) where.startDate = { gte: new Date(startDate) };
    if (endDate) where.endDate = { lte: new Date(endDate) };

    // 构建排序条件
    const orderBy: Prisma.ProjectOrderByWithRelationInput = {};
    if (sortBy === 'projectName') {
      orderBy.projectName = sortOrder;
    } else if (sortBy === 'profit') {
      // 按利润排序需要使用原始查询
      orderBy.planningBudget = sortOrder; // 临时使用预算排序
    } else {
      orderBy[sortBy as keyof Prisma.ProjectOrderByWithRelationInput] = sortOrder;
    }

    const [projects, total] = await Promise.all([
      this.prisma.project.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          brand: true,
          attachments: true,
        },
      }),
      this.prisma.project.count({ where }),
    ]);

    return {
      projects: projects.map(this.transformProject),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async getProject(id: string) {
    const project = await this.prisma.project.findUnique({
      where: { id },
      include: {
        brand: true,
        attachments: true,
      },
    });

    return project ? this.transformProject(project) : null;
  }

  async updateProject(data: UpdateProjectRequest, updatedBy: string) {
    const updateData: Prisma.ProjectUpdateInput = {
      updatedBy,
    };

    if (data.documentType) updateData.documentType = data.documentType;
    if (data.projectName) updateData.projectName = data.projectName;
    if (data.period) {
      updateData.startDate = new Date(data.period.startDate);
      updateData.endDate = new Date(data.period.endDate);
    }
    if (data.budget) {
      if (data.budget.planningBudget !== undefined) updateData.planningBudget = new Decimal(data.budget.planningBudget);
      if (data.budget.influencerBudget !== undefined) updateData.influencerBudget = new Decimal(data.budget.influencerBudget);
      if (data.budget.adBudget !== undefined) updateData.adBudget = new Decimal(data.budget.adBudget);
      if (data.budget.otherBudget !== undefined) updateData.otherBudget = new Decimal(data.budget.otherBudget);
    }
    if (data.cost) {
      if (data.cost.influencerCost !== undefined) updateData.influencerCost = new Decimal(data.cost.influencerCost);
      if (data.cost.adCost !== undefined) updateData.adCost = new Decimal(data.cost.adCost);
      if (data.cost.otherCost !== undefined) updateData.otherCost = new Decimal(data.cost.otherCost);
      if (data.cost.estimatedInfluencerRebate !== undefined) updateData.estimatedInfluencerRebate = new Decimal(data.cost.estimatedInfluencerRebate);
    }
    if (data.executorPM) updateData.executorPM = data.executorPM;
    if (data.contentMediaIds) updateData.contentMediaIds = data.contentMediaIds;
    if (data.contractType) updateData.contractType = data.contractType;
    if (data.settlementRules) updateData.settlementRules = data.settlementRules;
    if (data.kpi) updateData.kpi = data.kpi;
    if (data.brandId) {
      updateData.brand = {
        connect: { id: data.brandId }
      };
    }

    const project = await this.prisma.project.update({
      where: { id: data.id },
      data: updateData,
      include: {
        brand: true,
        attachments: true,
      },
    });

    return this.transformProject(project);
  }

  async deleteProject(id: string) {
    await this.prisma.project.delete({
      where: { id },
    });
    return true;
  }

  // 品牌管理
  async createBrand(data: CreateBrandRequest, createdBy: string) {
    return this.prisma.brand.create({
      data: {
        name: data.name,
        description: data.description,
        logo: data.logo,
        createdBy,
      },
    });
  }

  async getBrands(params: BrandQueryParams = {}) {
    const {
      page = 1,
      pageSize = 50,
      status,
      keyword,
      sortBy = 'name',
      sortOrder = 'asc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: Prisma.BrandWhereInput = {};
    if (status) where.status = status;
    if (keyword) {
      where.name = {
        contains: keyword,
        mode: 'insensitive'
      };
    }

    const orderBy: Prisma.BrandOrderByWithRelationInput = {};
    orderBy[sortBy as keyof Prisma.BrandOrderByWithRelationInput] = sortOrder;

    const [brands, total] = await Promise.all([
      this.prisma.brand.findMany({
        skip,
        take,
        where,
        orderBy,
      }),
      this.prisma.brand.count({ where }),
    ]);

    return {
      brands,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async getBrand(id: string) {
    return this.prisma.brand.findUnique({
      where: { id },
    });
  }

  async updateBrand(data: UpdateBrandRequest) {
    const updateData: Prisma.BrandUpdateInput = {};

    if (data.name) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.logo !== undefined) updateData.logo = data.logo;
    if (data.status) updateData.status = data.status;

    return this.prisma.brand.update({
      where: { id: data.id },
      data: updateData,
    });
  }

  async deleteBrand(id: string) {
    // 检查是否有关联项目
    const projectCount = await this.prisma.project.count({
      where: { brandId: id }
    });

    if (projectCount > 0) {
      throw new Error('无法删除品牌，存在关联的项目');
    }

    await this.prisma.brand.delete({
      where: { id },
    });
    return true;
  }

  // 统计查询
  async getProjectStats(): Promise<ProjectStats> {
    // 使用视图查询统计数据
    const summaryStats = await this.prisma.$queryRaw<Array<{
      total_projects: bigint;
      active_projects: bigint;
      completed_projects: bigint;
      draft_projects: bigint;
      cancelled_projects: bigint;
      total_planning_budget: Decimal;
      total_profit: Decimal;
      average_gross_margin_percentage: Decimal;
    }>>`SELECT * FROM project_summary_stats`;

    const brandStats = await this.prisma.$queryRaw<Array<{
      brand_id: string;
      brand_name: string;
      project_count: bigint;
      total_planning_budget: Decimal;
      total_profit: Decimal;
    }>>`SELECT brand_id, brand_name, project_count, total_planning_budget, total_profit FROM project_stats_by_brand WHERE project_count > 0`;

    const contractStats = await this.prisma.$queryRaw<Array<{
      contract_type: string;
      project_count: bigint;
      total_planning_budget: Decimal;
      total_profit: Decimal;
    }>>`SELECT contract_type, project_count, total_planning_budget, total_profit FROM project_stats_by_contract`;

    const summary = summaryStats[0];

    return {
      totalProjects: Number(summary.total_projects),
      activeProjects: Number(summary.active_projects),
      completedProjects: Number(summary.completed_projects),
      totalBudget: Number(summary.total_planning_budget),
      totalProfit: Number(summary.total_profit),
      averageGrossMargin: Number(summary.average_gross_margin_percentage),
      projectsByBrand: brandStats.map(stat => ({
        brandId: stat.brand_id,
        brandName: stat.brand_name,
        count: Number(stat.project_count),
        totalBudget: Number(stat.total_planning_budget),
      })),
      projectsByContractType: contractStats.map(stat => ({
        contractType: stat.contract_type as any,
        count: Number(stat.project_count),
        totalBudget: Number(stat.total_planning_budget),
      })),
    };
  }

  // 附件管理
  async createAttachment(data: {
    filename: string;
    originalName: string;
    size: number;
    mimeType: string;
    url: string;
    projectId: string;
    uploadedBy: string;
  }) {
    return this.prisma.attachment.create({
      data,
    });
  }

  async getAttachment(id: string) {
    return this.prisma.attachment.findUnique({
      where: { id },
    });
  }

  async deleteAttachment(id: string) {
    await this.prisma.attachment.delete({
      where: { id },
    });
    return true;
  }

  // 用户缓存
  async upsertUser(data: {
    userid: string;
    name: string;
    avatar?: string;
    mobile?: string;
    email?: string;
    department?: string[];
    position?: string;
    jobNumber?: string;
  }) {
    return this.prisma.user.upsert({
      where: { userid: data.userid },
      update: {
        ...data,
        lastSyncAt: new Date(),
      },
      create: {
        ...data,
        lastSyncAt: new Date(),
      },
    });
  }

  async getUser(userid: string) {
    return this.prisma.user.findUnique({
      where: { userid },
    });
  }

  async getUsers(userids: string[]) {
    return this.prisma.user.findMany({
      where: {
        userid: {
          in: userids,
        },
      },
    });
  }

  // 数据转换方法
  private transformProject(project: any): Project {
    // 计算利润
    const totalCost = Number(project.influencerCost) + Number(project.adCost) + Number(project.otherCost);
    const profit = Number(project.planningBudget) - totalCost + Number(project.estimatedInfluencerRebate);
    const grossMargin = Number(project.planningBudget) > 0 ? (profit / Number(project.planningBudget)) * 100 : 0;

    return {
      id: project.id,
      documentType: project.documentType,
      brandId: project.brandId,
      brand: project.brand,
      projectName: project.projectName,
      period: {
        startDate: project.startDate,
        endDate: project.endDate,
      },
      budget: {
        planningBudget: Number(project.planningBudget),
        influencerBudget: Number(project.influencerBudget),
        adBudget: Number(project.adBudget),
        otherBudget: Number(project.otherBudget),
      },
      cost: {
        influencerCost: Number(project.influencerCost),
        adCost: Number(project.adCost),
        otherCost: Number(project.otherCost),
        estimatedInfluencerRebate: Number(project.estimatedInfluencerRebate),
      },
      profit: {
        profit: Math.round(profit * 100) / 100,
        grossMargin: Math.round(grossMargin * 100) / 100,
      },
      executorPM: project.executorPM,
      contentMediaIds: project.contentMediaIds,
      contractType: project.contractType,
      settlementRules: project.settlementRules,
      kpi: project.kpi,
      attachments: project.attachments || [],
      status: project.status,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      createdBy: project.createdBy,
      updatedBy: project.updatedBy,
    };
  }

  // 健康检查
  async healthCheck() {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date() };
    } catch (error) {
      return { status: 'unhealthy', error: error.message, timestamp: new Date() };
    }
  }

  // 数据库指标
  async getMetrics() {
    const [
      connectionCount,
      tableStats,
    ] = await Promise.all([
      this.prisma.$queryRaw`SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'`,
      this.prisma.$queryRaw`
        SELECT
          schemaname,
          tablename,
          n_tup_ins as inserts,
          n_tup_upd as updates,
          n_tup_del as deletes,
          n_live_tup as live_tuples
        FROM pg_stat_user_tables
        WHERE schemaname = 'public'
      `,
    ]);

    return {
      connectionCount,
      tableStats,
    };
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }
}

export const db = new DatabaseService();
```

### 6. 环境配置

#### 开发环境
```bash
# 使用Docker启动PostgreSQL
docker run --name postgres-dev \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=project_management \
  -p 5432:5432 \
  -d postgres:15

# 运行迁移
npx prisma migrate dev
```

#### 生产环境
- 使用云数据库服务（如AWS RDS、阿里云RDS）
- 配置连接池和读写分离
- 设置备份和监控

### 7. 性能优化

#### 索引策略
```sql
-- 项目查询索引
CREATE INDEX idx_projects_brand_status ON projects(brand_id, status);
CREATE INDEX idx_projects_created_at ON projects(created_at);
CREATE INDEX idx_projects_executor_pm ON projects(executor_pm);

-- 品牌查询索引
CREATE INDEX idx_brands_status ON brands(status);
CREATE INDEX idx_brands_name ON brands(name);

-- 全文搜索索引
CREATE INDEX idx_projects_search ON projects USING gin(to_tsvector('chinese', project_name));
```

#### 查询优化
```typescript
// 使用选择性字段
const projects = await prisma.project.findMany({
  select: {
    id: true,
    projectName: true,
    status: true,
    brand: {
      select: {
        name: true,
      },
    },
  },
});

// 批量操作
await prisma.project.createMany({
  data: projectsData,
  skipDuplicates: true,
});
```

### 8. 数据迁移

从当前内存存储迁移到PostgreSQL：

```typescript
// 迁移脚本 scripts/migrate-to-postgres.ts
import { db } from '../src/services/database.js';
import { ProjectService } from '../src/services/project.js';

async function migrate() {
  const projectService = new ProjectService();
  
  // 迁移品牌数据
  const brands = await projectService.getBrands({ pageSize: 1000 });
  for (const brand of brands.data.brands) {
    await db.createBrand({
      id: brand.id,
      name: brand.name,
      description: brand.description,
      logo: brand.logo,
      status: brand.status === 'active' ? 'ACTIVE' : 'INACTIVE',
      createdBy: brand.createdBy,
    });
  }
  
  // 迁移项目数据
  const projects = await projectService.getProjects({ pageSize: 1000 });
  for (const project of projects.data.projects) {
    await db.createProject({
      id: project.id,
      documentType: 'PROJECT_INITIATION',
      brandId: project.brandId,
      projectName: project.projectName,
      startDate: project.period.startDate,
      endDate: project.period.endDate,
      planningBudget: project.budget.planningBudget,
      influencerBudget: project.budget.influencerBudget,
      adBudget: project.budget.adBudget,
      otherBudget: project.budget.otherBudget,
      influencerCost: project.cost.influencerCost,
      adCost: project.cost.adCost,
      otherCost: project.cost.otherCost,
      estimatedInfluencerRebate: project.cost.estimatedInfluencerRebate,
      executorPM: project.executorPM,
      contentMediaIds: project.contentMediaIds,
      contractType: project.contractType.toUpperCase(),
      settlementRules: project.settlementRules,
      kpi: project.kpi,
      attachments: project.attachments,
      status: project.status.toUpperCase(),
      createdBy: project.createdBy,
      updatedBy: project.updatedBy,
    });
  }
  
  console.log('数据迁移完成');
}

migrate().catch(console.error);
```

### 9. 备份策略

```bash
# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/backups/project_management"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_$DATE.sql"

# 创建备份
pg_dump $DATABASE_URL > $BACKUP_FILE

# 压缩备份
gzip $BACKUP_FILE

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

### 10. 监控和维护

```typescript
// 健康检查
export async function checkDatabaseHealth() {
  try {
    await db.prisma.$queryRaw`SELECT 1`;
    return { status: 'healthy', timestamp: new Date() };
  } catch (error) {
    return { status: 'unhealthy', error: error.message, timestamp: new Date() };
  }
}

// 性能监控
export async function getDatabaseMetrics() {
  const [
    connectionCount,
    slowQueries,
    tableStats,
  ] = await Promise.all([
    db.prisma.$queryRaw`SELECT count(*) FROM pg_stat_activity`,
    db.prisma.$queryRaw`SELECT query, mean_time FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10`,
    db.prisma.$queryRaw`SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del FROM pg_stat_user_tables`,
  ]);
  
  return {
    connectionCount,
    slowQueries,
    tableStats,
  };
}
```

这个方案提供了完整的数据库设计、迁移和维护策略，确保您的项目管理系统具有企业级的稳定性和性能。
