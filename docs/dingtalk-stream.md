# 钉钉Stream推送服务

本文档介绍如何使用钉钉Stream推送服务来实现实时的审批消息推送和回调处理。

## 概述

钉钉Stream推送是钉钉提供的一种高效的实时消息推送方式，相比传统的HTTP回调，Stream推送具有以下优势：

- **实时性更强**：基于WebSocket长连接，消息推送延迟更低
- **可靠性更高**：自动重连机制，确保连接稳定
- **配置简单**：无需配置公网回调地址
- **安全性好**：内置加密和验证机制

## 功能特性

### 1. 自动连接管理
- 自动获取Stream连接信息
- WebSocket连接建立和维护
- 自动重连机制（最多5次重试）
- 心跳保活机制

### 2. 事件处理
- 审批状态变更事件处理
- 用户相关事件处理
- 部门相关事件处理
- 自定义事件监听

### 3. 管理接口
- 服务状态监控
- 手动启动/停止/重启
- 连接测试
- 配置信息查看

## 配置说明

### 环境变量

在 `.env` 文件中添加以下配置：

```env
# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret

# 启用Stream推送（可选）
ENABLE_DINGTALK_STREAM=true
```

### 自动启动

Stream服务会在以下情况下自动启动：
- 生产环境 (`NODE_ENV=production`)
- 设置了 `ENABLE_DINGTALK_STREAM=true`

## API接口

### 1. 获取服务状态

```http
GET /api/dingtalk/stream/status
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "isRunning": true,
    "isConnected": true,
    "reconnectAttempts": 0,
    "endpoint": "wss://...",
    "message": "Stream服务运行正常"
  }
}
```

### 2. 启动服务

```http
POST /api/dingtalk/stream/start
```

**响应示例：**
```json
{
  "success": true,
  "message": "Stream服务启动成功",
  "data": {
    "isConnected": true,
    "reconnectAttempts": 0,
    "endpoint": "wss://..."
  }
}
```

### 3. 停止服务

```http
POST /api/dingtalk/stream/stop
```

### 4. 重启服务

```http
POST /api/dingtalk/stream/restart
```

### 5. 测试连接

```http
POST /api/dingtalk/stream/test
```

### 6. 获取配置信息

```http
GET /api/dingtalk/stream/config
```

## 事件处理

### 审批状态变更事件

当审批实例状态发生变更时，Stream服务会自动处理：

1. **接收事件**：从钉钉Stream接收 `bpms_instance_change` 事件
2. **状态同步**：更新本地数据库中的审批实例状态
3. **业务处理**：如果是付款审批通过，自动更新周预算已付金额
4. **发送确认**：向钉钉发送ACK确认消息

### 事件监听

可以通过事件监听器来处理自定义逻辑：

```typescript
streamService.on('callback', (data) => {
  console.log('收到回调事件:', data);
  // 自定义处理逻辑
});

streamService.on('error', (error) => {
  console.error('Stream服务错误:', error);
  // 错误处理逻辑
});
```

## 日志监控

Stream服务会输出详细的日志信息：

```
🚀 启动钉钉Stream推送服务...
📡 获取Stream连接信息成功
🔗 建立WebSocket连接
✅ WebSocket连接已建立
💓 发送心跳
📨 收到Stream消息: { type: 'CALLBACK', subject: 'bpms_instance_change' }
📤 发送ACK确认
📋 处理审批状态变更
✅ 审批状态变更处理完成
```

## 故障排除

### 1. 连接失败

**可能原因：**
- 钉钉应用配置错误
- 网络连接问题
- 访问令牌过期

**解决方法：**
- 检查 `DINGTALK_APP_KEY` 和 `DINGTALK_APP_SECRET` 配置
- 确认网络连接正常
- 重启服务重新获取访问令牌

### 2. 频繁重连

**可能原因：**
- 网络不稳定
- 钉钉服务端问题

**解决方法：**
- 检查网络稳定性
- 查看钉钉开发者控制台是否有异常
- 适当调整重连间隔

### 3. 事件处理失败

**可能原因：**
- 数据库连接问题
- 业务逻辑错误

**解决方法：**
- 检查数据库连接状态
- 查看详细错误日志
- 验证审批实例数据完整性

## 最佳实践

### 1. 生产环境部署

- 确保服务器网络稳定
- 配置适当的日志级别
- 设置监控和告警
- 定期检查服务状态

### 2. 开发环境调试

- 启用详细日志输出
- 使用管理接口进行测试
- 监控事件处理流程
- 验证业务逻辑正确性

### 3. 错误处理

- 实现优雅的错误处理
- 记录详细的错误信息
- 设置合理的重试机制
- 提供降级方案

## 参考资料

- [钉钉Stream推送官方文档](https://open.dingtalk.com/document/orgapp/configure-stream-push)
- [钉钉审批回调事件](https://open.dingtalk.com/document/orgapp/approval-callback-event)
- [WebSocket API文档](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
