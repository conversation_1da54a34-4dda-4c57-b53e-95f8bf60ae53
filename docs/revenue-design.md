# 项目收入管理系统设计文档

## 1. 系统概述

项目收入管理系统是项目管理平台的核心财务模块，提供完整的项目预计收入、实际收入追踪和统计分析功能。系统支持多种收入类型、状态管理和时间维度的收入预测，帮助企业更好地进行财务规划和现金流管理。

## 2. 业务需求

### 2.1 核心功能
- **预计收入管理**: 创建和管理项目的预计收入计划
- **收入状态跟踪**: 从计划到收款的全流程状态管理
- **多维度分类**: 支持里程碑、月度、季度等多种收入类型
- **财务统计**: 提供收入统计和趋势分析
- **发票管理**: 跟踪开票和收款情况

### 2.2 业务价值
- 提高财务预测准确性
- 优化现金流管理
- 增强项目盈利能力分析
- 支持业务决策制定

## 3. 数据模型设计

### 3.1 数据库表结构

#### 项目收入表 (project_revenues)
```sql
CREATE TABLE project_revenues (
  id VARCHAR(50) PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  revenue_type revenue_type NOT NULL DEFAULT 'MILESTONE',
  status revenue_status NOT NULL DEFAULT 'PLANNED',
  planned_amount DECIMAL(15, 2) NOT NULL,
  actual_amount DECIMAL(15, 2),
  invoice_amount DECIMAL(15, 2),
  planned_date DATE NOT NULL,
  confirmed_date DATE,
  invoice_date DATE,
  received_date DATE,
  milestone VARCHAR(200),
  invoice_number VARCHAR(100),
  payment_terms TEXT,
  notes TEXT,
  project_id VARCHAR(50) NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by VARCHAR(50) NOT NULL,
  updated_by VARCHAR(50) NOT NULL
);
```

### 3.2 枚举类型

#### 收入状态 (revenue_status)
- `RECEIVING` - 收款中
- `RECEIVED` - 已收款
- `CANCELLED` - 已取消

#### 收入类型 (revenue_type)
- `INFLUENCER_INCOME` - 达人收入
- `PROJECT_INCOME` - 项目收入
- `OTHER` - 其他收入

### 3.3 关系设计
- 项目与收入：一对多关系
- 支持级联删除：删除项目时自动删除相关收入记录
- 外键约束确保数据完整性

## 4. 系统架构

### 4.1 技术栈
- **后端框架**: Fastify + TypeScript
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: JWT Token
- **API文档**: Swagger/OpenAPI

### 4.2 分层架构
```
┌─────────────────┐
│   API Routes    │  路由层
├─────────────────┤
│  Controllers    │  控制器层
├─────────────────┤
│   Services      │  业务逻辑层
├─────────────────┤
│   Database      │  数据访问层
└─────────────────┘
```

### 4.3 核心组件

#### 控制器 (RevenueController)
- 处理HTTP请求和响应
- 参数验证和错误处理
- 调用业务服务层

#### 服务层 (ProjectService)
- 业务逻辑处理
- 数据转换和计算
- 支持内存模式和数据库模式

#### 数据访问层 (DatabaseService)
- 数据库操作封装
- Prisma ORM集成
- 数据转换和映射

## 5. API 设计

### 5.1 RESTful API 规范
- 遵循REST设计原则
- 统一的响应格式
- 完整的HTTP状态码
- 详细的错误信息

### 5.2 核心接口
- `POST /api/projects/{projectId}/revenues` - 创建收入
- `GET /api/revenues` - 获取收入列表
- `GET /api/revenues/{id}` - 获取单个收入
- `PUT /api/revenues/{id}` - 更新收入
- `DELETE /api/revenues/{id}` - 删除收入
- `GET /api/revenues/stats` - 获取统计信息

### 5.3 认证和授权
- 所有API需要JWT认证
- 基于角色的访问控制
- 操作审计日志

## 6. 业务流程

### 6.1 收入管理流程
```
项目启动 → 创建预计收入 → 达成里程碑 → 确认收入 → 开具发票 → 收到款项
    ↓           ↓            ↓         ↓        ↓        ↓
  规划阶段    PLANNED     CONFIRMED  INVOICED  RECEIVED  完成
```

### 6.2 状态流转规则
- 正向流转：PLANNED → CONFIRMED → INVOICED → RECEIVED
- 可取消：任何状态都可以变更为CANCELLED
- 逾期处理：INVOICED状态超期自动变为OVERDUE

## 7. 数据统计

### 7.1 统计维度
- **金额统计**: 预计收入、实际收入、开票金额、已收款金额
- **状态分布**: 各状态下的收入数量和金额
- **类型分析**: 不同收入类型的分布情况
- **时间趋势**: 月度收入趋势分析

### 7.2 统计视图
```sql
-- 收入统计视图
CREATE VIEW revenue_stats_view AS
SELECT 
  SUM(planned_amount) as total_planned_revenue,
  SUM(actual_amount) as total_actual_revenue,
  COUNT(*) as total_count
FROM project_revenues;

-- 月度趋势视图
CREATE VIEW monthly_revenue_trend_view AS
SELECT 
  TO_CHAR(planned_date, 'YYYY-MM') as month,
  SUM(planned_amount) as planned_amount,
  SUM(actual_amount) as actual_amount
FROM project_revenues
GROUP BY TO_CHAR(planned_date, 'YYYY-MM');
```

## 8. 安全考虑

### 8.1 数据安全
- 敏感金额数据加密存储
- 操作日志完整记录
- 定期数据备份

### 8.2 访问控制
- JWT Token认证
- 基于角色的权限控制
- API访问频率限制

### 8.3 数据验证
- 输入参数严格验证
- 金额字段范围检查
- 日期格式标准化

## 9. 性能优化

### 9.1 数据库优化
- 合理的索引设计
- 查询语句优化
- 分页查询支持

### 9.2 缓存策略
- 统计数据缓存
- 查询结果缓存
- 用户信息缓存

## 10. 监控和运维

### 10.1 监控指标
- API响应时间
- 数据库查询性能
- 错误率统计

### 10.2 日志管理
- 操作日志记录
- 错误日志追踪
- 性能日志分析

## 11. 扩展性设计

### 11.1 功能扩展
- 支持多币种收入
- 收入预测算法
- 自动化提醒功能

### 11.2 集成扩展
- 财务系统集成
- 发票系统对接
- 银行系统连接

## 12. 测试策略

### 12.1 单元测试
- 业务逻辑测试
- 数据验证测试
- 错误处理测试

### 12.2 集成测试
- API接口测试
- 数据库操作测试
- 端到端流程测试

### 12.3 性能测试
- 并发访问测试
- 大数据量测试
- 压力测试

## 13. 部署和维护

### 13.1 部署方案
- Docker容器化部署
- 数据库迁移脚本
- 环境配置管理

### 13.2 维护计划
- 定期数据备份
- 系统性能监控
- 功能更新发布

这个设计文档提供了项目收入管理系统的完整技术方案，涵盖了从需求分析到系统实现的各个方面，为开发团队提供了清晰的技术指导。
