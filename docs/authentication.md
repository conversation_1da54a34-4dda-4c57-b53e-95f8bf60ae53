# 钉钉免登认证文档

## 概述

本项目管理系统采用钉钉企业内部应用免登认证机制，确保只有经过认证的企业用户才能访问敏感的业务数据。所有涉及项目、品牌、用户等核心业务数据的API接口都需要通过钉钉免登认证。

## 安全考虑

### 为什么需要认证？

1. **数据安全**: 项目和品牌信息属于企业核心业务数据，不应被未授权用户访问
2. **权限控制**: 不同用户具有不同的操作权限（普通用户、管理员、老板）
3. **审计追踪**: 需要记录谁在什么时候进行了什么操作
4. **合规要求**: 企业数据访问需要符合安全合规要求

### 受保护的API接口

以下API接口需要钉钉免登认证：

#### 项目管理
- `POST /api/projects` - 创建项目
- `GET /api/projects` - 获取项目列表
- `GET /api/projects/:id` - 获取单个项目
- `PUT /api/projects` - 更新项目
- `DELETE /api/projects/:id` - 删除项目（需要管理员权限）
- `GET /api/projects/stats` - 获取项目统计

#### 品牌管理
- `POST /api/brands` - 创建品牌（需要管理员权限）
- `GET /api/brands` - 获取品牌列表
- `GET /api/brands/:id` - 获取单个品牌
- `PUT /api/brands` - 更新品牌（需要管理员权限）
- `DELETE /api/brands/:id` - 删除品牌（需要管理员权限）

#### 用户管理
- `GET /api/departments` - 获取部门列表
- `GET /api/departments/:deptId/users` - 获取部门用户列表
- `GET /api/users/search` - 搜索用户
- `GET /api/users/:userid` - 获取用户详情

#### 文件管理
- `POST /api/upload` - 文件上传

## 认证流程

### 1. 前端获取免登码

在钉钉客户端中，使用钉钉JSAPI获取免登码：

```javascript
dd.ready(function() {
    dd.runtime.permission.requestAuthCode({
        corpId: 'your_corp_id', // 企业corpId
        onSuccess: function(result) {
            const authCode = result.code;
            // 将免登码发送给后端验证
            authenticateWithServer(authCode);
        },
        onFail: function(err) {
            console.error('获取免登码失败:', err);
        }
    });
});
```

### 2. 后端验证免登码

后端接收到免登码后，调用钉钉API验证并获取用户信息：

```javascript
// 1. 通过免登码获取用户ID
const userIdResponse = await dingTalkAPI.getUserInfo(authCode);
const userid = userIdResponse.userid;

// 2. 通过用户ID获取详细信息
const userDetail = await dingTalkAPI.getUserDetail(userid);

// 3. 将用户信息存储到请求上下文
request.user = {
    userid: userDetail.userid,
    name: userDetail.name,
    mobile: userDetail.mobile,
    deptIds: userDetail.dept_id_list,
    isAdmin: userDetail.admin,
    isBoss: userDetail.boss
};
```

### 3. API请求认证

前端在调用受保护的API时，需要在请求头中包含免登码：

```javascript
const headers = {
    'Content-Type': 'application/json',
    'X-DingTalk-Auth-Code': authCode
};

const response = await fetch('/api/projects', {
    method: 'GET',
    headers: headers
});
```

## 权限级别

### 普通用户
- 可以查看项目和品牌列表
- 可以创建和更新自己参与的项目
- 可以查看部门用户信息
- 可以上传文件

### 管理员
- 拥有普通用户的所有权限
- 可以创建、更新、删除品牌
- 可以删除项目
- 可以查看所有部门的数据

### 老板
- 拥有管理员的所有权限
- 可以访问所有数据和功能

## 错误处理

### 认证失败响应

当认证失败时，API会返回以下错误响应：

```json
{
  "success": false,
  "message": "钉钉免登认证失败",
  "code": "AUTH_FAILED"
}
```

### 权限不足响应

当用户权限不足时，API会返回以下错误响应：

```json
{
  "success": false,
  "message": "权限不足，需要管理员权限",
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

### 常见错误码

- `MISSING_AUTH_CODE`: 缺少钉钉免登认证码
- `AUTH_FAILED`: 钉钉免登认证失败
- `AUTH_SERVICE_ERROR`: 认证服务异常
- `USER_NOT_AUTHENTICATED`: 用户未认证
- `INSUFFICIENT_PERMISSIONS`: 权限不足
- `DEPARTMENT_ACCESS_DENIED`: 部门访问权限不足

## 前端集成示例

### 完整的认证流程

```html
<!DOCTYPE html>
<html>
<head>
    <title>钉钉免登认证示例</title>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.15.0/dingtalk.open.js"></script>
</head>
<body>
    <script>
        let authCode = null;

        // 初始化钉钉认证
        dd.ready(function() {
            dd.runtime.permission.requestAuthCode({
                corpId: 'your_corp_id',
                onSuccess: function(result) {
                    authCode = result.code;
                    console.log('获取免登码成功:', authCode);
                    
                    // 现在可以调用受保护的API
                    callProtectedAPI();
                },
                onFail: function(err) {
                    console.error('获取免登码失败:', err);
                }
            });
        });

        // 调用受保护的API
        async function callProtectedAPI() {
            try {
                const response = await fetch('/api/projects', {
                    headers: {
                        'X-DingTalk-Auth-Code': authCode
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    console.log('API调用成功:', data);
                } else {
                    console.error('API调用失败:', data.message);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }
    </script>
</body>
</html>
```

## 开发和测试

### 开发环境

在开发环境中，如果无法使用钉钉客户端，可以：

1. 使用钉钉开发者工具进行调试
2. 配置测试用的免登码
3. 临时禁用认证中间件（仅限开发环境）

### 测试建议

1. 测试正常的认证流程
2. 测试认证失败的情况
3. 测试权限不足的情况
4. 测试免登码过期的情况
5. 测试网络异常的情况

## 注意事项

1. **免登码有效期**: 钉钉免登码有时效性，通常为几分钟，需要及时使用
2. **HTTPS要求**: 生产环境必须使用HTTPS协议
3. **企业配置**: 需要在钉钉开放平台配置企业应用
4. **IP白名单**: 建议配置服务器IP白名单
5. **日志记录**: 记录所有认证和权限相关的操作日志

## 相关链接

- [钉钉开放平台文档](https://open.dingtalk.com/)
- [企业内部应用免登文档](https://open.dingtalk.com/document/orgapp/enterprise-internal-application-logon-free)
- [钉钉JSAPI文档](https://open.dingtalk.com/document/orgapp/jsapi-overview)
