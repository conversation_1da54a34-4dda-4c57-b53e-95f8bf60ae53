# 钉钉审批回调集成说明

## 功能概述

您的钉钉Stream服务现在已经完整集成了审批回调处理功能。当钉钉审批流程状态发生变更时，系统会自动接收推送消息并处理相关业务逻辑。

## 已实现的功能

### 1. **自动审批事件处理**

当钉钉审批状态变更时，系统会自动：

```typescript
// 1. 接收钉钉推送的审批事件
private async onCallback(message: DWClientDownStream): Promise<void> {
  if (message.headers.eventType === 'bpms_instance_change') {
    // 2. 解析审批数据
    const approvalData = JSON.parse(message.data);
    
    // 3. 调用审批服务处理
    const result = await this.handleApprovalStatusChange(approvalData);
    
    // 4. 触发事件供外部监听
    this.emit('approval_change', {
      originalData: approvalData,
      processResult: result
    });
  }
}
```

### 2. **审批数据处理**

系统会自动处理以下审批数据字段：

```typescript
interface ApprovalEventData {
  processInstanceId: string;  // 审批实例ID
  result: string;            // 审批结果 (agree/refuse/etc.)
  type: string;              // 事件类型 (start/finish/etc.)
  staffId: string;           // 操作人员工ID
  createTime: number;        // 创建时间
  finishTime?: number;       // 完成时间
  corpId: string;            // 企业ID
}
```

### 3. **数据库自动更新**

通过集成的 `ApprovalService`，系统会自动：

- 更新审批状态
- 同步支付金额到周预算
- 防止重复更新
- 记录操作日志

### 4. **事件监听机制**

您可以监听以下事件：

```typescript
// 审批状态变更成功
streamService.on('approval_change', (data) => {
  console.log('审批处理成功:', data.processResult);
});

// 审批处理错误
streamService.on('approval_error', (data) => {
  console.error('审批处理失败:', data.error);
});

// 通用回调事件
streamService.on('callback', (message) => {
  console.log('收到回调:', message.headers.topic);
});
```

## 使用方法

### 1. **启动服务**

服务已集成到主应用中，启动时会自动初始化：

```bash
# 设置环境变量启用Stream服务
export ENABLE_DINGTALK_STREAM=true

# 启动应用
npm start
```

### 2. **测试审批回调**

```bash
# 测试审批回调功能
node scripts/test-approval-callback.js
```

### 3. **手动处理审批数据**

如果需要手动处理审批数据：

```typescript
const streamService = new DingTalkStreamSimple();

// 手动处理审批数据
const approvalData = {
  processInstanceId: 'process-123',
  result: 'agree',
  type: 'finish',
  staffId: 'staff-456',
  createTime: Date.now(),
  corpId: 'corp-789'
};

const result = await streamService.processApprovalData(approvalData);
```

## 审批流程处理逻辑

### 1. **接收审批事件**

```
钉钉审批状态变更 → Stream推送 → onCallback处理
```

### 2. **数据解析和验证**

```typescript
// 解析JSON数据
const approvalData = JSON.parse(message.data);

// 构建标准参数
const changeParams = {
  processInstanceId: approvalData.processInstanceId || '',
  result: approvalData.result || 'unknown',
  type: approvalData.type || 'unknown',
  staffId: approvalData.staffId || '',
  createTime: approvalData.createTime || Date.now(),
  finishTime: approvalData.finishTime || null,
  corpId: approvalData.corpId || ''
};
```

### 3. **调用业务逻辑**

```typescript
// 调用ApprovalService处理
const result = await this.approvalService.handleApprovalStatusChange(changeParams);
```

### 4. **结果处理**

```typescript
// 成功时触发事件
this.emit('approval_change', {
  originalData: approvalData,
  processResult: result
});

// 失败时触发错误事件
this.emit('approval_error', {
  error: error,
  originalData: message.data
});
```

## 配置要求

### 1. **钉钉应用配置**

确保您的钉钉应用具有以下权限：

- ✅ 审批事件订阅权限
- ✅ Stream推送权限
- ✅ 企业内部应用权限

### 2. **环境变量**

```env
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
ENABLE_DINGTALK_STREAM=true
```

### 3. **数据库连接**

确保数据库连接正常，因为审批处理需要更新相关表：

- `approvals` - 审批记录表
- `weekly_budgets` - 周预算表
- 其他相关业务表

## 监控和调试

### 1. **日志输出**

系统会输出详细的处理日志：

```
📞 处理回调消息: /v1.0/workflow/processInstances/events/changed
📋 收到审批状态变更事件
📋 审批数据: { processInstanceId: "...", result: "agree", ... }
🔄 开始处理审批状态变更...
📋 审批变更参数: { processInstanceId: "...", ... }
✅ 审批服务处理完成: { success: true, ... }
✅ 审批状态变更处理结果: { success: true, ... }
```

### 2. **错误处理**

如果处理失败，会输出详细错误信息：

```
❌ 处理审批状态变更失败: Error: ...
❌ 审批状态变更处理失败: Error: ...
```

### 3. **状态监控**

```bash
# 查看Stream服务状态
curl http://localhost:3000/api/dingtalk/stream/status

# 查看连接状态
const status = streamService.getConnectionStatus();
console.log('连接状态:', status);
```

## 测试场景

### 1. **正常审批流程**

1. 在钉钉中发起审批
2. 审批人进行审批操作
3. 系统自动接收状态变更
4. 更新数据库相关记录
5. 触发相应事件

### 2. **错误处理测试**

1. 模拟网络异常
2. 模拟数据库连接失败
3. 模拟无效的审批数据
4. 验证错误恢复机制

### 3. **重复处理防护**

1. 模拟重复的审批事件
2. 验证防重复机制
3. 确保数据一致性

## 注意事项

1. **幂等性**：系统已实现防重复处理机制
2. **错误恢复**：处理失败时会触发错误事件，不会影响其他功能
3. **性能考虑**：审批处理是异步的，不会阻塞消息接收
4. **数据安全**：所有审批数据都会进行验证和清理

您的钉钉审批回调功能现在已经完全就绪，可以自动处理审批状态变更并更新相关业务数据。
