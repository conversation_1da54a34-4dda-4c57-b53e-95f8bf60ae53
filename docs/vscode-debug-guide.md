# VSCode 调试配置指南

本项目已配置完整的 VSCode 调试环境，支持 TypeScript、Node.js 和钉钉 API 开发调试。

## 🚀 快速开始

### 1. 安装推荐扩展
打开项目后，VSCode 会自动提示安装推荐扩展。点击 "Install All" 安装所有推荐扩展。

### 2. 环境配置
确保项目根目录有 `.env` 文件，包含必要的环境变量：

```env
# 服务器配置
PORT=3000
HOST=localhost
NODE_ENV=development

# 钉钉配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_CORP_ID=your_corp_id

# JWT 配置
JWT_SECRET=your_jwt_secret

# 日志级别
LOG_LEVEL=debug
```

## 🔧 调试配置说明

### 主要调试配置

#### 1. 🚀 启动开发服务器
- **用途**: 直接调试 TypeScript 源码
- **特点**: 使用 tsx 运行器，支持热重载
- **端口**: 3000
- **环境**: development
- **注意**: 适用于 Node.js v20.6.0+

#### 2. ⚡ 使用 tsx 启动开发服务器
- **用途**: 备用的 TypeScript 调试方案
- **特点**: 使用 npx tsx 命令，兼容性更好
- **推荐**: 如果主配置有问题时使用

#### 3. 🔄 使用 nodemon 启动开发服务器
- **用途**: 文件变化自动重启的调试方案
- **特点**: 结合 nodemon 和 tsx，稳定性好
- **推荐**: 长时间开发调试使用

#### 4. 🔧 调试编译后的代码
- **用途**: 调试编译后的 JavaScript 代码
- **特点**: 先编译再调试，更接近生产环境
- **前置任务**: 自动执行 build 任务

#### 3. 🧪 调试测试脚本
- **用途**: 调试各种测试脚本
- **包含**: API测试、数据库测试、钉钉API测试等

#### 4. 🔗 附加到运行中的进程
- **用途**: 附加到已运行的 Node.js 进程
- **端口**: 9229 (默认调试端口)

## 📋 任务配置

### 构建任务
- `build`: 编译 TypeScript 到 JavaScript
- `build:check`: 类型检查（不生成文件）
- `clean`: 清理 dist 目录

### 开发任务
- `dev`: 启动开发服务器（热重载）
- `test`: 运行测试
- `test:all`: 运行所有测试

### 数据库任务
- `db:generate`: 生成 Prisma 客户端
- `db:migrate`: 运行数据库迁移
- `db:studio`: 启动 Prisma Studio

## 🎯 调试技巧

### 1. 断点调试
- 在代码行号左侧点击设置断点
- 支持条件断点和日志断点
- 可在变量上悬停查看值

### 2. 调试控制台
- 在调试时可在控制台执行表达式
- 支持查看调用栈和变量作用域

### 3. 监视表达式
- 在调试面板添加监视表达式
- 实时查看变量值变化

### 4. 异常断点
- 可设置在抛出异常时自动断点
- 帮助快速定位错误

## 🔍 常用调试场景

### 调试 API 接口
1. 在路由处理函数设置断点
2. 使用 REST Client 扩展发送请求
3. 逐步调试请求处理流程

### 调试钉钉 API 调用
1. 在钉钉 API 调用前后设置断点
2. 检查请求参数和响应数据
3. 验证 access_token 是否有效

### 调试数据库操作
1. 在 Prisma 查询前后设置断点
2. 检查查询条件和结果
3. 使用 Prisma Studio 查看数据

### 调试认证流程
1. 在 JWT 验证中间件设置断点
2. 检查 token 解析过程
3. 验证用户权限

## 📁 项目结构调试

```
src/
├── controllers/     # 控制器 - 业务逻辑调试
├── routes/         # 路由 - 请求处理调试
├── services/       # 服务 - 外部API调试
├── middleware/     # 中间件 - 认证授权调试
├── types/          # 类型定义
└── utils/          # 工具函数调试
```

## 🛠️ 代码片段

项目包含常用的代码片段，输入前缀即可快速生成：

- `fastify-route`: Fastify 路由处理器
- `dingtalk-api`: 钉钉 API 调用
- `prisma-query`: Prisma 数据库查询
- `error-handler`: 错误处理
- `jwt-validate`: JWT 验证
- `zod-schema`: Zod 数据验证

## 🚨 常见问题

### 1. 调试器无法启动
- 检查端口是否被占用
- 确认 .env 文件配置正确
- 重启 VSCode

### 2. 断点不生效
- 确认 sourceMaps 已启用
- 检查 TypeScript 编译配置
- 清理并重新编译

### 3. 环境变量未加载
- 检查 .env 文件路径
- 确认 envFile 配置正确
- 重启调试会话

### 4. 钉钉 API 调试失败
- 检查网络连接
- 验证 APP_KEY 和 APP_SECRET
- 确认 access_token 有效性

### 5. Node.js v20+ tsx 加载器问题
如果遇到 "tsx must be loaded with --import instead of --loader" 错误：
- 使用 "⚡ 使用 tsx 启动开发服务器" 配置
- 或者使用 "🔄 使用 nodemon 启动开发服务器" 配置
- 这些配置已经适配了 Node.js v20+ 的新要求

## 📚 相关文档

- [Fastify 文档](https://www.fastify.io/docs/)
- [Prisma 文档](https://www.prisma.io/docs/)
- [钉钉开放平台](https://open.dingtalk.com/)
- [VSCode 调试指南](https://code.visualstudio.com/docs/editor/debugging)

## 🎉 开始调试

1. 按 `F5` 或点击调试面板的 "启动调试"
2. 选择 "🚀 启动开发服务器" 配置
3. 在浏览器访问 `http://localhost:3000`
4. 开始愉快的调试之旅！
