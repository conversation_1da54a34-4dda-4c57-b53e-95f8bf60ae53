# 钉钉JSAPI签名问题修复指南

## 🚨 问题描述

在钉钉H5微应用中遇到签名校验失败的错误：

```json
{
  "errorCode": "9",
  "errorMessage": "签名校验失败,nonce:[290c2fe615501c11990afbdf18aedb22],timestamp:[1749524958026],url:[http://localhost:3000/dingtalk-design-demo.html?dd_debug_h5=true&dd_debug_v1=7.7.0-Release.250522002&dd_debug_os=pc&dd_debug_v2=Chrome-108.0&dd_debug_unifiedAppId=f2722d5d-ad85-4a3c-89db-616b1bc4b6a6&dd_debug_token=15bc24f534b84ddbbdb061415abc044a&dd_debug_uuid=1669e0fc-00c6-408f-966f-8f044eb7b51b&dd_debug_pid=pcHome],ticketList:[M4Rd3Nsdaz******************************************************************Ww1wlchsyy]"
}
```

## 🔍 问题分析

### 主要问题

1. **URL包含调试参数**：钉钉调试工具会在URL中添加大量调试参数，这些参数会影响签名计算
2. **URL编码问题**：前端传递给后端的URL编码处理不当
3. **签名算法细节**：签名生成时的参数处理和字符编码问题

### 调试参数列表

钉钉调试工具会添加以下参数：
- `dd_debug_h5=true`
- `dd_debug_v1=7.7.0-Release.250522002`
- `dd_debug_os=pc`
- `dd_debug_v2=Chrome-108.0`
- `dd_debug_unifiedAppId=f2722d5d-ad85-4a3c-89db-616b1bc4b6a6`
- `dd_debug_token=15bc24f534b84ddbbdb061415abc044a`
- `dd_debug_uuid=1669e0fc-00c6-408f-966f-8f044eb7b51b`
- `dd_debug_pid=pcHome`

## ✅ 解决方案

### 1. 后端修复 (src/services/dingtalk.ts)

#### 添加URL清理函数

```typescript
/**
 * 清理URL，移除钉钉调试参数
 */
private cleanUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    
    // 移除钉钉调试相关的参数
    const debugParams = [
      'dd_debug_h5',
      'dd_debug_v1', 
      'dd_debug_os',
      'dd_debug_v2',
      'dd_debug_unifiedAppId',
      'dd_debug_token',
      'dd_debug_uuid',
      'dd_debug_pid'
    ];
    
    debugParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });
    
    return urlObj.toString();
  } catch (error) {
    console.warn('URL清理失败，使用原始URL:', error);
    return url;
  }
}
```

#### 改进签名生成算法

```typescript
async generateCorrectJSAPISignature(url: string): Promise<JSAPISignature> {
  const timestamp = Date.now();
  const nonceStr = crypto.randomBytes(16).toString('hex');

  try {
    const ticket = await this.getJSAPITicket();
    
    // 1. URL解码
    let decodedUrl = decodeURIComponent(url);
    
    // 2. 清理调试参数
    decodedUrl = this.cleanUrl(decodedUrl);
    
    // 3. 按照钉钉官方文档的签名算法
    // 重要：参数必须严格按照字母顺序排列
    const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${decodedUrl}`;
    const signature = crypto.createHash('sha1').update(string1, 'utf8').digest('hex');

    return {
      agentId: this.config.agentId || '',
      corpId: this.config.corpId,
      timeStamp: timestamp,
      nonceStr,
      signature,
    };
  } catch (error) {
    console.error('生成JSAPI签名失败:', error);
    // 降级到简单签名
    return this.generateJSAPISignature(url);
  }
}
```

### 2. 前端修复

#### 添加URL清理函数 (JavaScript)

```javascript
// 清理URL，移除钉钉调试参数
function cleanUrl(url) {
    try {
        const urlObj = new URL(url);
        
        // 移除钉钉调试相关的参数
        const debugParams = [
            'dd_debug_h5',
            'dd_debug_v1', 
            'dd_debug_os',
            'dd_debug_v2',
            'dd_debug_unifiedAppId',
            'dd_debug_token',
            'dd_debug_uuid',
            'dd_debug_pid'
        ];
        
        debugParams.forEach(param => {
            urlObj.searchParams.delete(param);
        });
        
        return urlObj.toString();
    } catch (error) {
        console.warn('URL清理失败:', error);
        return url;
    }
}
```

#### 改进JSAPI签名获取

```javascript
// 获取JSAPI签名数据
async function getJSAPISignatureData() {
    try {
        // 清理当前URL，移除调试参数
        const cleanedUrl = cleanUrl(window.location.href);
        console.log('原始URL:', window.location.href);
        console.log('清理后URL:', cleanedUrl);
        
        const currentUrl = encodeURIComponent(cleanedUrl);
        const response = await fetch(`/api/auth/jsapi-signature?url=${currentUrl}`, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        
        if (data.success) {
            return data.data;
        } else {
            throw new Error(data.message || '获取JSAPI签名失败');
        }
    } catch (error) {
        console.error('获取JSAPI签名失败:', error);
        throw error;
    }
}
```

## 🧪 测试验证

### 1. 使用签名测试页面

访问 `http://localhost:3000/signature-test.html` 进行测试：

- 查看URL清理效果
- 测试JSAPI签名生成
- 对比标准签名和增强版签名

### 2. 使用免登录演示页面

访问 `http://localhost:3000/dingtalk-login-demo.html`：

- 查看详细的调试日志
- 验证免登录流程
- 检查签名是否正确

### 3. API测试

访问 `http://localhost:3000/test-api.html`：

- 单独测试各个API接口
- 验证签名生成接口

## 📋 修复检查清单

- ✅ 后端添加URL清理函数
- ✅ 后端改进签名生成算法
- ✅ 前端添加URL清理函数
- ✅ 前端改进签名获取逻辑
- ✅ 添加详细的调试日志
- ✅ 创建专门的测试页面
- ✅ 更新所有相关HTML页面

## 🔧 关键修复点

### 1. URL处理

**问题**：钉钉调试参数影响签名计算
**解决**：在签名计算前移除所有调试参数

### 2. 字符编码

**问题**：URL编码/解码处理不当
**解决**：确保正确的编码/解码顺序

### 3. 签名算法

**问题**：参数顺序和字符编码
**解决**：严格按照钉钉文档的算法实现

## 🚀 部署注意事项

1. **环境变量**：确保正确配置钉钉应用信息
2. **HTTPS**：生产环境必须使用HTTPS
3. **域名白名单**：在钉钉开放平台配置可信域名
4. **调试模式**：生产环境关闭调试模式

## 📞 故障排除

### 如果仍然出现签名错误

1. 检查jsapi_ticket是否有效
2. 验证access_token是否正确
3. 确认应用配置是否正确
4. 查看服务器日志中的详细错误信息

### 常见错误码

- `errorCode: 9` - 签名校验失败
- `errorCode: 40001` - access_token无效
- `errorCode: 40014` - 不合法的access_token

## 📚 参考资料

- [钉钉JSAPI签名算法](https://open.dingtalk.com/document/orgapp/jsapi-signature-algorithm)
- [钉钉H5微应用开发](https://open.dingtalk.com/document/orgapp/h5-micro-applications)
- [钉钉免登录流程](https://open.dingtalk.com/document/orgapp/logon-free-process)
