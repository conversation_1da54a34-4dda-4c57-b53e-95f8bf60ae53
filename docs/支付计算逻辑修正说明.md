# 支付计算逻辑修正说明

## 修正概述

根据用户需求，修正了财务导出服务中的供应商支付计算逻辑，确保本月应付、下月应付和转后续月份的计算符合业务规则。

## 业务规则

供应商支付计算逻辑：
1. **未付金额** = 供应商总金额 - 已付金额
2. **项目结束时间判断**：
   - 如果项目结束时间 ≤ 本月最后一天：本月应付 = 未付金额
   - 如果项目结束时间 > 本月最后一天：按天均分计算

## 计算逻辑

### 1. 本月应付计算 (`getAmountToPayThisMonth`)

```typescript
// 如果项目结束时间没有超过本月，本月应付就是未付金额
if (projectEndDate <= lastDayOfMonth) {
  return amountLeft;
}

// 如果项目结束时间超过本月，需要按天均分
const totalDaysLeft = TimezoneUtils.getDaysDifference(today, projectEndDate);
const daysLeftInMonth = lastDayOfMonth.getDate() - today.getDate() + 1;
const dailyAmount = amountLeft / totalDaysLeft;
return Math.round(dailyAmount * daysLeftInMonth);
```

### 2. 下月应付计算 (`getTransferToNextMonth`)

```typescript
// 如果项目结束时间没有超过本月，下月应付为0
if (projectEndDate <= lastDayOfMonth) {
  return 0;
}

// 如果项目结束时间超过本月但没有超过下月
if (projectEndDate <= lastDayOfNextMonth) {
  return amountLeft - this.getAmountToPayThisMonth(project, weeklyBudget);
}

// 如果项目结束时间超过下月，需要按天均分
const totalDaysLeft = TimezoneUtils.getDaysDifference(today, projectEndDate);
const dailyAmount = amountLeft / totalDaysLeft;
const daysInNextMonth = lastDayOfNextMonth.getDate();
return Math.round(dailyAmount * daysInNextMonth);
```

### 3. 转后续月份计算 (`getTransferToNext2Month`)

```typescript
// 只有项目结束时间超过下月最后一天，才有转后续月份的金额
if (projectEndDate > lastDayOfNextMonth) {
  const thisMonthAmount = this.getAmountToPayThisMonth(project, weeklyBudget);
  const nextMonthAmount = this.getTransferToNextMonth(project, weeklyBudget);
  return amountLeft - thisMonthAmount - nextMonthAmount;
}
return 0;
```

## 测试场景

### 场景1：项目在本月结束
- 项目结束时间：2024-07-25
- 当前时间：2024-07-20
- 未付金额：8000元
- **结果**：本月应付=8000，下月应付=0，转后续月份=0

### 场景2：项目在下月结束
- 项目结束时间：2024-08-15
- 当前时间：2024-07-20
- 未付金额：8000元
- **结果**：本月应付=3692，下月应付=4308，转后续月份=0

### 场景3：项目超过下月结束
- 项目结束时间：2024-09-15
- 当前时间：2024-07-20
- 未付金额：8000元
- **结果**：本月应付=1684，下月应付=4351，转后续月份=1965

## 修改文件

- `src/services/financialExport.ts`
  - 修正了 `getAmountToPayThisMonth` 方法
  - 修正了 `getTransferToNextMonth` 方法
  - 修正了 `getTransferToNext2Month` 方法
  - 启用了被注释的方法调用

## 验证

- ✅ TypeScript 编译通过
- ✅ 计算逻辑测试通过
- ✅ 三种场景验证正确

## 注意事项

1. 表头中的月份名称是动态生成的，会根据当前月份自动调整
2. 计算时会排除时分秒，只按日期进行比较
3. 金额计算使用 `Math.round()` 进行四舍五入
4. 所有日期计算都使用 `TimezoneUtils` 确保时区一致性
