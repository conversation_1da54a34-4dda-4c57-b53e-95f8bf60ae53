# 数据库快速开始指南

## 🎯 概述

本指南将帮助您快速设置和使用PostgreSQL数据库版本的项目管理系统。

## 🚀 快速开始

### 方式一：自动设置（推荐）

```bash
# 1. 一键设置数据库
npm run db:setup

# 2. 启动应用
npm run dev

# 3. 访问项目管理页面
# http://localhost:3000/project-management.html
```

### 方式二：手动设置

#### 1. 安装PostgreSQL

**选项A：使用Docker（推荐）**
```bash
# 启动PostgreSQL容器
docker run --name postgres-project-mgmt \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=project_management \
  -e POSTGRES_USER=postgres \
  -p 5432:5432 \
  -d postgres:15
```

**选项B：本地安装**
- 下载并安装PostgreSQL：https://www.postgresql.org/download/
- 创建数据库：`project_management`

#### 2. 配置环境变量

编辑 `.env` 文件：
```env
# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/project_management?schema=public"

# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key_here
DINGTALK_APP_SECRET=your_app_secret_here
DINGTALK_CORP_ID=your_corp_id_here
DINGTALK_AGENT_ID=your_agent_id_here
```

#### 3. 安装依赖和初始化

```bash
# 安装Prisma依赖
npm install prisma @prisma/client

# 生成Prisma客户端
npm run db:generate

# 运行数据库迁移
npm run db:migrate

# 启动应用
npm run dev
```

## 🧪 测试验证

### 测试数据库功能
```bash
npm run test:db
```

### 测试项目管理API
```bash
npm run test:project
```

### 测试所有API
```bash
npm run test:api
```

## 📊 功能特性

### 数据库优势
- ✅ **数据持久化**：数据永久保存，重启不丢失
- ✅ **ACID事务**：确保财务数据的一致性
- ✅ **高精度计算**：使用DECIMAL类型确保金额精确
- ✅ **复杂查询**：支持统计分析和报表
- ✅ **数据完整性**：外键约束保证数据关联正确
- ✅ **性能优化**：索引和查询优化

### 项目管理功能
- ✅ **项目立项**：完整的项目创建流程
- ✅ **品牌管理**：独立的品牌库维护
- ✅ **预算控制**：详细的预算和成本管理
- ✅ **利润计算**：自动计算项目利润和毛利率
- ✅ **人员管理**：执行PM和内容媒介分配
- ✅ **合同管理**：多种合同类型支持
- ✅ **附件管理**：项目文件上传和管理
- ✅ **统计分析**：多维度数据统计

## 🗄️ 数据库管理

### 常用命令

```bash
# 数据库迁移
npm run db:migrate      # 开发环境迁移
npm run db:deploy       # 生产环境迁移

# 客户端管理
npm run db:generate     # 生成Prisma客户端

# 数据库工具
npm run db:studio       # 打开Prisma Studio (可视化管理)
npm run db:reset        # 重置数据库

# 数据迁移
npm run db:seed         # 从内存存储迁移数据
```

### Prisma Studio

Prisma Studio是一个可视化的数据库管理工具：

```bash
npm run db:studio
```

打开 http://localhost:5555 即可：
- 查看和编辑数据
- 执行查询
- 管理数据库结构

## 📈 数据统计

### 实时统计卡片
- **总项目数**：系统中所有项目的数量
- **活跃项目**：状态为"进行中"的项目数量
- **总预算**：所有项目的规划预算总和
- **总利润**：所有项目的利润总和
- **平均毛利率**：所有项目的平均毛利率

### 详细统计查询
```sql
-- 按品牌统计
SELECT * FROM project_stats_by_brand;

-- 按合同类型统计
SELECT * FROM project_stats_by_contract;

-- 项目详细统计
SELECT * FROM project_stats;

-- 汇总统计
SELECT * FROM project_summary_stats;
```

## 🔧 数据库维护

### 备份数据库
```bash
# 创建备份
pg_dump $DATABASE_URL > backup.sql

# 恢复备份
psql $DATABASE_URL < backup.sql
```

### 监控性能
```bash
# 查看连接数
SELECT count(*) FROM pg_stat_activity;

# 查看慢查询
SELECT query, mean_time FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

# 查看表统计
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
FROM pg_stat_user_tables;
```

## 🚨 故障排除

### 常见问题

**1. 数据库连接失败**
```bash
# 检查数据库是否运行
docker ps | grep postgres

# 重启数据库容器
docker restart postgres-project-mgmt

# 检查连接字符串
echo $DATABASE_URL
```

**2. 迁移失败**
```bash
# 重置数据库
npm run db:reset

# 重新运行迁移
npm run db:migrate
```

**3. Prisma客户端错误**
```bash
# 重新生成客户端
npm run db:generate

# 清理node_modules
rm -rf node_modules
npm install
```

### 日志查看
```bash
# 查看应用日志
npm run dev

# 查看数据库日志
docker logs postgres-project-mgmt
```

## 🔄 数据迁移

### 从内存存储迁移

如果您之前使用的是内存存储模式，可以将数据迁移到数据库：

```bash
# 1. 确保数据库已设置
npm run db:setup

# 2. 运行数据迁移
npm run db:seed

# 3. 验证迁移结果
npm run test:db
```

### 迁移过程
1. **品牌数据迁移**：将所有品牌信息迁移到数据库
2. **项目数据迁移**：将所有项目信息迁移到数据库
3. **数据验证**：检查数据完整性和关联关系
4. **清理重复数据**：处理可能的重复记录

## 🎯 最佳实践

### 开发环境
1. **使用Docker**：简化数据库设置
2. **定期备份**：避免开发数据丢失
3. **使用Prisma Studio**：可视化数据管理
4. **运行测试**：确保功能正常

### 生产环境
1. **使用云数据库**：如AWS RDS、阿里云RDS
2. **配置连接池**：提高性能
3. **设置监控**：及时发现问题
4. **定期备份**：数据安全保障

## 📚 相关文档

- [完整数据库设置指南](database-setup-guide.md)
- [项目管理使用指南](project-management-guide.md)
- [API接口文档](../DINGTALK_IMPLEMENTATION.md)
- [Prisma官方文档](https://www.prisma.io/docs/)

## 💡 提示

1. **数据库URL格式**：
   ```
   postgresql://用户名:密码@主机:端口/数据库名?schema=public
   ```

2. **Docker管理命令**：
   ```bash
   docker start postgres-project-mgmt    # 启动
   docker stop postgres-project-mgmt     # 停止
   docker restart postgres-project-mgmt  # 重启
   ```

3. **环境切换**：
   - 有`DATABASE_URL`：自动使用数据库模式
   - 无`DATABASE_URL`：自动使用内存存储模式

4. **性能优化**：
   - 使用索引加速查询
   - 合理使用分页
   - 避免N+1查询问题

现在您已经成功设置了PostgreSQL数据库版本的项目管理系统！🎉
