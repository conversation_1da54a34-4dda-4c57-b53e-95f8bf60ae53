import pandas as pd


def parse_chinese_address(address):
    """
    Parse Chinese address into province-city, full administrative regions and detailed address

    Args:
        address (str): Full Chinese address string

    Returns:
        tuple: (province_city, administrative_region, detailed_address)
    """
    if pd.isna(address):  # Handle empty or NaN values
        return ("", "", "")

    # Initialize components
    province = ""
    city = ""
    district = ""

    # Find province
    province_end = address.find('省')
    if province_end != -1:
        province = address[:province_end + 1]
        address = address[province_end + 1:]
    elif address.startswith('上海市') or address.startswith('北京市') or address.startswith(
            '天津市') or address.startswith('重庆市'):
        province = address[:3]
        address = address[3:]

    # Find city
    city_end = address.find('市')
    if city_end != -1:
        city = address[:city_end + 1]
        address = address[city_end + 1:]

    # Find district
    for district_suffix in ['区', '县', '镇']:
        district_end = address.find(district_suffix)
        if district_end != -1:
            district = address[:district_end + 1]
            address = address[district_end + 1:]
            break

    # Create different levels of administrative regions
    province_city = province + city
    full_admin_region = province + city + district

    return (province_city, full_admin_region, address.strip())


def process_excel_addresses(file_path):
    """
    Process addresses in Excel file with first row as header

    Args:
        file_path (str): Path to Excel file
    """
    try:
        # Read Excel file
        df = pd.read_excel(file_path)

        # Add new columns if they don't exist
        if '省市' not in df.columns:
            df['省市'] = ""
        if '行政区划' not in df.columns:
            df['行政区划'] = ""
        if '详细地址' not in df.columns:
            df['详细地址'] = ""

        # Create lists for results
        province_cities = ["省市"]  # Header for first row
        admin_regions = ["行政区划"]  # Header for first row
        detailed_addresses = ["详细地址"]  # Header for first row

        # Process addresses from row 1 onwards (skipping header)
        for i in range(1, len(df)):
            address = df.iloc[i, 5]  # Column F (index 5)
            if pd.notna(address):  # Only process if address is not empty
                province_city, admin_region, detail = parse_chinese_address(str(address))
                province_cities.append(province_city)
                admin_regions.append(admin_region)
                detailed_addresses.append(detail)
            else:
                province_cities.append("")
                admin_regions.append("")
                detailed_addresses.append("")

        # Update the columns
        df['省市'] = province_cities
        df['行政区划'] = admin_regions
        df['详细地址'] = detailed_addresses

        # Save the results back to Excel
        df.to_excel(file_path, index=False)
        print("处理完成！")
        return True

    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        return False
# Usage example:
process_excel_addresses('utf-8用户信息.xlsx')