-- CreateEnum
CREATE TYPE "brand_status" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "document_type" AS ENUM ('PROJECT_INITIATION', 'PROJECT_PROPOSAL', 'PROJECT_PLAN', 'PROJECT_EXECUTION', 'PROJECT_SUMMARY');

-- <PERSON>reateEnum
CREATE TYPE "contract_type" AS ENUM ('ANNUAL_FRAME', 'QUARTERLY_FRAME', 'SINGLE', 'PO_ORDER', 'JING_TASK');

-- CreateEnum
CREATE TYPE "project_status" AS ENUM ('DRAFT', 'ACTIVE', 'COMPLETED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "revenue_status" AS ENUM ('RECEIVING', 'RECEIVED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "revenue_type" AS ENUM ('INFLUENCER_INCOME', 'PROJECT_INCOME', 'OTHER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "supplier_status" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'BLACKLISTED');

-- CreateEnum
CREATE TYPE "service_type" AS ENUM ('INFLUENCER', 'ADVERTISING', 'OTHER');

-- CreateEnum
CREATE TYPE "tax_rate" AS ENUM ('SPECIAL_1', 'SPECIAL_3', 'SPECIAL_6', 'GENERAL');

-- CreateEnum
CREATE TYPE "weekly_budget_status" AS ENUM ('DRAFT', 'APPROVED', 'EXECUTING', 'COMPLETED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "approval_status" AS ENUM ('NONE', 'PENDING', 'APPROVED', 'REJECTED', 'CANCELLED');

-- CreateTable
CREATE TABLE "brands" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "logo" VARCHAR(500),
    "status" "brand_status" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "createdBy" VARCHAR(50) NOT NULL,

    CONSTRAINT "brands_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "projects" (
    "id" TEXT NOT NULL,
    "documentType" "document_type" NOT NULL DEFAULT 'PROJECT_INITIATION',
    "brandId" TEXT NOT NULL,
    "projectName" VARCHAR(200) NOT NULL,
    "startDate" DATE NOT NULL,
    "endDate" DATE NOT NULL,
    "planningBudget" DECIMAL(15,2) NOT NULL,
    "influencerBudget" DECIMAL(15,2) NOT NULL,
    "adBudget" DECIMAL(15,2) NOT NULL,
    "otherBudget" DECIMAL(15,2) NOT NULL,
    "expectedPaymentMonth" VARCHAR(7),
    "paymentTermDays" SMALLINT,
    "influencerCost" DECIMAL(15,2) NOT NULL,
    "adCost" DECIMAL(15,2) NOT NULL,
    "otherCost" DECIMAL(15,2) NOT NULL,
    "estimatedInfluencerRebate" DECIMAL(15,2) NOT NULL,
    "executorPM" VARCHAR(50) NOT NULL,
    "contentMediaIds" TEXT[],
    "contractType" "contract_type" NOT NULL,
    "settlementRules" TEXT NOT NULL,
    "kpi" TEXT NOT NULL,
    "status" "project_status" NOT NULL DEFAULT 'DRAFT',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "createdBy" VARCHAR(50) NOT NULL,
    "updatedBy" VARCHAR(50) NOT NULL,

    CONSTRAINT "projects_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "attachments" (
    "id" TEXT NOT NULL,
    "filename" VARCHAR(255) NOT NULL,
    "originalName" VARCHAR(255) NOT NULL,
    "size" BIGINT NOT NULL,
    "mimeType" VARCHAR(100) NOT NULL,
    "url" VARCHAR(500) NOT NULL,
    "projectId" TEXT NOT NULL,
    "uploadedBy" VARCHAR(50) NOT NULL,
    "uploadedAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "attachments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_revenues" (
    "id" TEXT NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "revenueType" "revenue_type" NOT NULL DEFAULT 'PROJECT_INCOME',
    "status" "revenue_status" NOT NULL DEFAULT 'RECEIVING',
    "plannedAmount" DECIMAL(15,2) NOT NULL,
    "actualAmount" DECIMAL(15,2),
    "invoiceAmount" DECIMAL(15,2),
    "plannedDate" DATE,
    "confirmedDate" DATE,
    "invoiceDate" DATE,
    "receivedDate" DATE,
    "milestone" VARCHAR(200),
    "invoiceNumber" VARCHAR(100),
    "paymentTerms" TEXT,
    "notes" TEXT,
    "projectId" TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "createdBy" VARCHAR(50) NOT NULL,
    "updatedBy" VARCHAR(50) NOT NULL,

    CONSTRAINT "project_revenues_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "weekly_budgets" (
    "id" TEXT NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "weekStartDate" DATE NOT NULL,
    "weekEndDate" DATE NOT NULL,
    "weekNumber" SMALLINT NOT NULL,
    "year" SMALLINT NOT NULL,
    "serviceType" "service_type" NOT NULL,
    "serviceContent" TEXT NOT NULL,
    "remarks" TEXT,
    "contractAmount" DECIMAL(15,2) NOT NULL,
    "taxRate" "tax_rate" NOT NULL,
    "paidAmount" DECIMAL(15,2) NOT NULL DEFAULT 0,
    "remainingAmount" DECIMAL(15,2) NOT NULL,
    "status" "weekly_budget_status" NOT NULL DEFAULT 'DRAFT',
    "approvalStatus" "approval_status" NOT NULL DEFAULT 'NONE',
    "approvalAmount" DECIMAL(15,2),
    "approvalReason" TEXT,
    "projectId" TEXT NOT NULL,
    "supplierId" TEXT,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "createdBy" VARCHAR(50) NOT NULL,
    "updatedBy" VARCHAR(50) NOT NULL,

    CONSTRAINT "weekly_budgets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "suppliers" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "shortName" VARCHAR(100),
    "code" VARCHAR(50),
    "contactPerson" VARCHAR(100),
    "contactPhone" VARCHAR(20),
    "contactEmail" VARCHAR(100),
    "address" TEXT,
    "taxNumber" VARCHAR(50),
    "bankAccount" VARCHAR(50),
    "bankName" VARCHAR(200),
    "legalPerson" VARCHAR(100),
    "serviceTypes" "service_type"[],
    "preferredTaxRate" "tax_rate",
    "creditLimit" DECIMAL(15,2),
    "paymentTerms" TEXT,
    "status" "supplier_status" NOT NULL DEFAULT 'ACTIVE',
    "rating" SMALLINT,
    "notes" TEXT,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "createdBy" VARCHAR(50) NOT NULL,
    "updatedBy" VARCHAR(50) NOT NULL,

    CONSTRAINT "suppliers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "userid" VARCHAR(50) NOT NULL,
    "unionid" VARCHAR(100),
    "name" VARCHAR(100) NOT NULL,
    "avatar" VARCHAR(500),
    "stateCode" VARCHAR(10),
    "managerUserid" VARCHAR(50),
    "mobile" VARCHAR(20),
    "hideMobile" BOOLEAN DEFAULT false,
    "telephone" VARCHAR(20),
    "jobNumber" VARCHAR(50),
    "title" VARCHAR(100),
    "email" VARCHAR(100),
    "workPlace" VARCHAR(200),
    "remark" TEXT,
    "loginId" VARCHAR(100),
    "exclusiveAccountType" VARCHAR(20),
    "exclusiveAccount" BOOLEAN DEFAULT false,
    "deptIdList" INTEGER[],
    "extension" TEXT,
    "hiredDate" TIMESTAMPTZ,
    "active" BOOLEAN DEFAULT true,
    "realAuthed" BOOLEAN DEFAULT false,
    "orgEmail" VARCHAR(100),
    "orgEmailType" VARCHAR(50),
    "senior" BOOLEAN DEFAULT false,
    "admin" BOOLEAN DEFAULT false,
    "boss" BOOLEAN DEFAULT false,
    "lastSyncAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "users_pkey" PRIMARY KEY ("userid")
);

-- CreateTable
CREATE TABLE "approval_instances" (
    "id" TEXT NOT NULL,
    "processInstanceId" VARCHAR(100) NOT NULL,
    "processCode" VARCHAR(100) NOT NULL,
    "businessId" VARCHAR(100),
    "title" VARCHAR(200) NOT NULL,
    "originatorUserId" VARCHAR(50) NOT NULL,
    "status" "approval_status" NOT NULL DEFAULT 'PENDING',
    "result" VARCHAR(50),
    "createTime" TIMESTAMPTZ NOT NULL,
    "finishTime" TIMESTAMPTZ,
    "approvalAmount" DECIMAL(15,2) NOT NULL,
    "actualAmount" DECIMAL(15,2),
    "reason" TEXT,
    "remark" TEXT,
    "weeklyBudgetId" TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "approval_instances_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "brands_name_key" ON "brands"("name");

-- CreateIndex
CREATE INDEX "brands_status_idx" ON "brands"("status");

-- CreateIndex
CREATE INDEX "brands_name_idx" ON "brands"("name");

-- CreateIndex
CREATE INDEX "projects_brandId_status_idx" ON "projects"("brandId", "status");

-- CreateIndex
CREATE INDEX "projects_executorPM_idx" ON "projects"("executorPM");

-- CreateIndex
CREATE INDEX "projects_contractType_idx" ON "projects"("contractType");

-- CreateIndex
CREATE INDEX "projects_startDate_endDate_idx" ON "projects"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "projects_createdAt_idx" ON "projects"("createdAt");

-- CreateIndex
CREATE INDEX "projects_status_idx" ON "projects"("status");

-- CreateIndex
CREATE INDEX "projects_expectedPaymentMonth_idx" ON "projects"("expectedPaymentMonth");

-- CreateIndex
CREATE INDEX "projects_paymentTermDays_idx" ON "projects"("paymentTermDays");

-- CreateIndex
CREATE INDEX "attachments_projectId_idx" ON "attachments"("projectId");

-- CreateIndex
CREATE INDEX "attachments_uploadedBy_idx" ON "attachments"("uploadedBy");

-- CreateIndex
CREATE INDEX "project_revenues_projectId_status_idx" ON "project_revenues"("projectId", "status");

-- CreateIndex
CREATE INDEX "project_revenues_plannedDate_idx" ON "project_revenues"("plannedDate");

-- CreateIndex
CREATE INDEX "project_revenues_status_idx" ON "project_revenues"("status");

-- CreateIndex
CREATE INDEX "project_revenues_revenueType_idx" ON "project_revenues"("revenueType");

-- CreateIndex
CREATE INDEX "project_revenues_createdAt_idx" ON "project_revenues"("createdAt");

-- CreateIndex
CREATE INDEX "weekly_budgets_projectId_weekStartDate_idx" ON "weekly_budgets"("projectId", "weekStartDate");

-- CreateIndex
CREATE INDEX "weekly_budgets_supplierId_idx" ON "weekly_budgets"("supplierId");

-- CreateIndex
CREATE INDEX "weekly_budgets_serviceType_idx" ON "weekly_budgets"("serviceType");

-- CreateIndex
CREATE INDEX "weekly_budgets_status_idx" ON "weekly_budgets"("status");

-- CreateIndex
CREATE INDEX "weekly_budgets_approvalStatus_idx" ON "weekly_budgets"("approvalStatus");

-- CreateIndex
CREATE INDEX "weekly_budgets_year_weekNumber_idx" ON "weekly_budgets"("year", "weekNumber");

-- CreateIndex
CREATE INDEX "weekly_budgets_createdAt_idx" ON "weekly_budgets"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "suppliers_code_key" ON "suppliers"("code");

-- CreateIndex
CREATE INDEX "suppliers_name_idx" ON "suppliers"("name");

-- CreateIndex
CREATE INDEX "suppliers_status_idx" ON "suppliers"("status");

-- CreateIndex
CREATE INDEX "suppliers_serviceTypes_idx" ON "suppliers"("serviceTypes");

-- CreateIndex
CREATE INDEX "suppliers_createdAt_idx" ON "suppliers"("createdAt");

-- CreateIndex
CREATE INDEX "users_name_idx" ON "users"("name");

-- CreateIndex
CREATE INDEX "users_isActive_idx" ON "users"("isActive");

-- CreateIndex
CREATE INDEX "users_mobile_idx" ON "users"("mobile");

-- CreateIndex
CREATE INDEX "users_email_idx" ON "users"("email");

-- CreateIndex
CREATE INDEX "users_managerUserid_idx" ON "users"("managerUserid");

-- CreateIndex
CREATE UNIQUE INDEX "approval_instances_processInstanceId_key" ON "approval_instances"("processInstanceId");

-- CreateIndex
CREATE INDEX "approval_instances_processInstanceId_idx" ON "approval_instances"("processInstanceId");

-- CreateIndex
CREATE INDEX "approval_instances_weeklyBudgetId_idx" ON "approval_instances"("weeklyBudgetId");

-- CreateIndex
CREATE INDEX "approval_instances_status_idx" ON "approval_instances"("status");

-- CreateIndex
CREATE INDEX "approval_instances_originatorUserId_idx" ON "approval_instances"("originatorUserId");

-- CreateIndex
CREATE INDEX "approval_instances_createTime_idx" ON "approval_instances"("createTime");

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "brands"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "attachments" ADD CONSTRAINT "attachments_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_revenues" ADD CONSTRAINT "project_revenues_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "weekly_budgets" ADD CONSTRAINT "weekly_budgets_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "weekly_budgets" ADD CONSTRAINT "weekly_budgets_supplierId_fkey" FOREIGN KEY ("supplierId") REFERENCES "suppliers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "approval_instances" ADD CONSTRAINT "approval_instances_weeklyBudgetId_fkey" FOREIGN KEY ("weeklyBudgetId") REFERENCES "weekly_budgets"("id") ON DELETE CASCADE ON UPDATE CASCADE;
