-- AlterTable
ALTER TABLE "project_revenues" ALTER COLUMN "plannedDate" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "confirmedDate" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "invoiceDate" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "receivedDate" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "projects" ADD COLUMN     "intermediaryCost" DECIMAL(15,2) NOT NULL DEFAULT 0,
ALTER COLUMN "startDate" DROP NOT NULL,
ALTER COLUMN "startDate" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "endDate" DROP NOT NULL,
ALTER COLUMN "endDate" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "weekly_budgets" ALTER COLUMN "weekStartDate" DROP NOT NULL,
ALTER COLUMN "weekStartDate" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "weekEndDate" DROP NOT NULL,
ALTER COLUMN "weekEndDate" SET DATA TYPE TIMESTAMPTZ;
