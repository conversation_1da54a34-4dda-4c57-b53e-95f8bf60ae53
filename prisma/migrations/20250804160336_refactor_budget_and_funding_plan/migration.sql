-- CreateEnum
CREATE TYPE "budget_status" AS ENUM ('CREATED', 'APPROVED', 'EXECUTING', 'COMPLETED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "funding_plan_status" AS ENUM ('DRAFT', 'SUBMITTED', 'APPROVED', 'EXECUTING', 'COMPLETED', 'CANCELLED');

-- DropIndex
DROP INDEX "weekly_budgets_projectId_weekStartDate_idx";

-- DropIndex
DROP INDEX "weekly_budgets_year_weekNumber_idx";

-- AlterTable
ALTER TABLE "approval_instances" ADD COLUMN     "fundingPlanId" TEXT,
ALTER COLUMN "weeklyBudgetId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "weekly_budgets" ALTER COLUMN "weekNumber" DROP NOT NULL,
ALTER COLUMN "year" DROP NOT NULL;

-- CreateTable
CREATE TABLE "budgets" (
    "id" TEXT NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "serviceType" "service_type" NOT NULL,
    "serviceContent" TEXT NOT NULL,
    "remarks" TEXT,
    "contractAmount" DECIMAL(15,2) NOT NULL,
    "taxRate" "tax_rate" NOT NULL,
    "totalPaidAmount" DECIMAL(15,2) NOT NULL DEFAULT 0,
    "remainingAmount" DECIMAL(15,2) NOT NULL,
    "status" "budget_status" NOT NULL DEFAULT 'CREATED',
    "projectId" TEXT NOT NULL,
    "supplierId" TEXT,
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) NOT NULL,
    "createdBy" VARCHAR(50) NOT NULL,
    "updatedBy" VARCHAR(50) NOT NULL,

    CONSTRAINT "budgets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "funding_plans" (
    "id" TEXT NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "year" SMALLINT NOT NULL,
    "month" SMALLINT NOT NULL,
    "weekOfMonth" SMALLINT NOT NULL,
    "plannedAmount" DECIMAL(15,2) NOT NULL,
    "paidAmount" DECIMAL(15,2) NOT NULL DEFAULT 0,
    "remainingAmount" DECIMAL(15,2) NOT NULL,
    "status" "funding_plan_status" NOT NULL DEFAULT 'DRAFT',
    "approvalStatus" "approval_status" NOT NULL DEFAULT 'NONE',
    "approvalAmount" DECIMAL(15,2),
    "approvalReason" TEXT,
    "remarks" TEXT,
    "budgetId" TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) NOT NULL,
    "createdBy" VARCHAR(50) NOT NULL,
    "updatedBy" VARCHAR(50) NOT NULL,

    CONSTRAINT "funding_plans_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "budgets_projectId_idx" ON "budgets"("projectId");

-- CreateIndex
CREATE INDEX "budgets_supplierId_idx" ON "budgets"("supplierId");

-- CreateIndex
CREATE INDEX "budgets_serviceType_idx" ON "budgets"("serviceType");

-- CreateIndex
CREATE INDEX "budgets_status_idx" ON "budgets"("status");

-- CreateIndex
CREATE INDEX "budgets_createdAt_idx" ON "budgets"("createdAt");

-- CreateIndex
CREATE INDEX "funding_plans_budgetId_idx" ON "funding_plans"("budgetId");

-- CreateIndex
CREATE INDEX "funding_plans_year_month_weekOfMonth_idx" ON "funding_plans"("year", "month", "weekOfMonth");

-- CreateIndex
CREATE INDEX "funding_plans_status_idx" ON "funding_plans"("status");

-- CreateIndex
CREATE INDEX "funding_plans_approvalStatus_idx" ON "funding_plans"("approvalStatus");

-- CreateIndex
CREATE INDEX "funding_plans_createdAt_idx" ON "funding_plans"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "funding_plans_budgetId_year_month_weekOfMonth_key" ON "funding_plans"("budgetId", "year", "month", "weekOfMonth");

-- CreateIndex
CREATE INDEX "approval_instances_fundingPlanId_idx" ON "approval_instances"("fundingPlanId");

-- CreateIndex
CREATE INDEX "weekly_budgets_projectId_idx" ON "weekly_budgets"("projectId");

-- AddForeignKey
ALTER TABLE "budgets" ADD CONSTRAINT "budgets_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "budgets" ADD CONSTRAINT "budgets_supplierId_fkey" FOREIGN KEY ("supplierId") REFERENCES "suppliers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "funding_plans" ADD CONSTRAINT "funding_plans_budgetId_fkey" FOREIGN KEY ("budgetId") REFERENCES "budgets"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "approval_instances" ADD CONSTRAINT "approval_instances_fundingPlanId_fkey" FOREIGN KEY ("fundingPlanId") REFERENCES "funding_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;
