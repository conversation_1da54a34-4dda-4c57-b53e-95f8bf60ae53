import { FastifyInstance } from 'fastify';
import { PermissionController } from '../controllers/permissionController.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';
import { DatabaseService } from '../services/database.js';
import { PermissionService } from '../services/permissionService.js';

export async function permissionRoutes(fastify: FastifyInstance) {
  // 初始化服务和控制器
  const databaseService = new DatabaseService();
  const permissionService = new PermissionService(databaseService);
  const permissionController = new PermissionController(permissionService);

  // 创建权限
  fastify.post('/permissions', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_CREATE)],
    schema: {
      description: '创建权限',
      tags: ['Permission'],
      body: {
        type: 'object',
        required: ['name', 'displayName', 'module', 'action'],
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 100, description: '权限名称' },
          displayName: { type: 'string', minLength: 1, maxLength: 100, description: '权限显示名称' },
          description: { type: 'string', description: '权限描述' },
          module: { type: 'string', minLength: 1, maxLength: 50, description: '所属模块' },
          action: { type: 'string', minLength: 1, maxLength: 50, description: '操作类型' },
          resource: { type: 'string', maxLength: 50, description: '资源类型' },
          isSystem: { type: 'boolean', description: '是否为系统权限' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                displayName: { type: 'string' },
                description: { type: 'string' },
                module: { type: 'string' },
                action: { type: 'string' },
                resource: { type: 'string' },
                isSystem: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.createPermission.bind(permissionController));

  // 批量创建权限
  fastify.post('/permissions/batch', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_CREATE)],
    schema: {
      description: '批量创建权限',
      tags: ['Permission'],
      body: {
        type: 'object',
        required: ['permissions'],
        properties: {
          permissions: {
            type: 'array',
            items: {
              type: 'object',
              required: ['name', 'displayName', 'module', 'action'],
              properties: {
                name: { type: 'string', minLength: 1, maxLength: 100 },
                displayName: { type: 'string', minLength: 1, maxLength: 100 },
                description: { type: 'string' },
                module: { type: 'string', minLength: 1, maxLength: 50 },
                action: { type: 'string', minLength: 1, maxLength: 50 },
                resource: { type: 'string', maxLength: 50 },
                isSystem: { type: 'boolean' },
              },
            },
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                permissions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                      module: { type: 'string' },
                      action: { type: 'string' },
                    },
                  },
                },
                total: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.createPermissionsBatch.bind(permissionController));

  // 更新权限
  fastify.put('/permissions/:permissionId', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_UPDATE)],
    schema: {
      description: '更新权限',
      tags: ['Permission'],
      params: {
        type: 'object',
        required: ['permissionId'],
        properties: {
          permissionId: { type: 'string', description: '权限ID' },
        },
      },
      body: {
        type: 'object',
        properties: {
          displayName: { type: 'string', minLength: 1, maxLength: 100, description: '权限显示名称' },
          description: { type: 'string', description: '权限描述' },
          module: { type: 'string', minLength: 1, maxLength: 50, description: '所属模块' },
          action: { type: 'string', minLength: 1, maxLength: 50, description: '操作类型' },
          resource: { type: 'string', maxLength: 50, description: '资源类型' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                displayName: { type: 'string' },
                description: { type: 'string' },
                module: { type: 'string' },
                action: { type: 'string' },
                resource: { type: 'string' },
                isSystem: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.updatePermission.bind(permissionController));

  // 删除权限
  fastify.delete('/permissions/:permissionId', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_DELETE)],
    schema: {
      description: '删除权限',
      tags: ['Permission'],
      params: {
        type: 'object',
        required: ['permissionId'],
        properties: {
          permissionId: { type: 'string', description: '权限ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.deletePermission.bind(permissionController));

  // 获取权限详情
  fastify.get('/permissions/:permissionId', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_READ)],
    schema: {
      description: '获取权限详情',
      tags: ['Permission'],
      params: {
        type: 'object',
        required: ['permissionId'],
        properties: {
          permissionId: { type: 'string', description: '权限ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                displayName: { type: 'string' },
                description: { type: 'string' },
                module: { type: 'string' },
                action: { type: 'string' },
                resource: { type: 'string' },
                isSystem: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.getPermission.bind(permissionController));

  // 获取权限列表
  fastify.get('/permissions', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_READ)],
    schema: {
      description: '获取权限列表',
      tags: ['Permission'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          module: { type: 'string', description: '模块筛选' },
          action: { type: 'string', description: '操作类型筛选' },
          isSystem: { type: 'string', description: '是否为系统权限' },
          search: { type: 'string', description: '搜索关键词' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                permissions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                      description: { type: 'string' },
                      module: { type: 'string' },
                      action: { type: 'string' },
                      resource: { type: 'string' },
                      isSystem: { type: 'boolean' },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                    },
                  },
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.getPermissions.bind(permissionController));

  // 按模块分组获取权限
  fastify.get('/permissions/modules', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_READ)],
    schema: {
      description: '按模块分组获取权限',
      tags: ['Permission'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                modules: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      module: { type: 'string' },
                      permissions: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            displayName: { type: 'string' },
                            action: { type: 'string' },
                          },
                        },
                      },
                    },
                  },
                },
                total: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.getPermissionsByModule.bind(permissionController));

  // 获取所有模块列表
  fastify.get('/permissions/modules/list', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_READ)],
    schema: {
      description: '获取所有模块列表',
      tags: ['Permission'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                modules: {
                  type: 'array',
                  items: { type: 'string' },
                },
                total: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.getModules.bind(permissionController));

  // 获取所有操作类型列表
  fastify.get('/permissions/actions', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_READ)],
    schema: {
      description: '获取所有操作类型列表',
      tags: ['Permission'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                actions: {
                  type: 'array',
                  items: { type: 'string' },
                },
                total: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.getActions.bind(permissionController));

  // 检查权限名称是否可用
  fastify.get('/permissions/check-name', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_READ)],
    schema: {
      description: '检查权限名称是否可用',
      tags: ['Permission'],
      querystring: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { type: 'string', description: '权限名称' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                available: { type: 'boolean' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.checkPermissionName.bind(permissionController));

  // 检查指定权限名称是否可用（更新时使用）
  fastify.get('/permissions/:permissionId/check-name', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PERMISSION_READ)],
    schema: {
      description: '检查指定权限名称是否可用（更新时使用）',
      tags: ['Permission'],
      params: {
        type: 'object',
        required: ['permissionId'],
        properties: {
          permissionId: { type: 'string', description: '权限ID' },
        },
      },
      querystring: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { type: 'string', description: '权限名称' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                available: { type: 'boolean' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, permissionController.checkPermissionName.bind(permissionController));
}
