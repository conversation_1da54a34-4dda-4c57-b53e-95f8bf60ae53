import { FastifyInstance } from 'fastify';
import { FinancialExportController } from '../controllers/financialExport.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

export async function financialExportRoutes(fastify: FastifyInstance) {
  const financialExportController = new FinancialExportController();

  // 导出财务报表
  fastify.get('/financial/export', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.REPORT_EXPORT)],
    schema: {
      description: '导出财务报表Excel',
      tags: ['Financial Export'],
      querystring: {
        type: 'object',
        required: ['year'],
        properties: {
          year: { 
            type: 'string', 
            description: '导出年份',
            pattern: '^(202[0-9]|203[0-9])$'
          },
          brandIds: { 
            type: 'string', 
            description: '品牌ID列表，用逗号分隔' 
          },
          includeCompleted: { 
            type: 'string', 
            enum: ['true', 'false'],
            description: '是否包含已完成项目，默认true' 
          },
          includeCancelled: { 
            type: 'string', 
            enum: ['true', 'false'],
            description: '是否包含已取消项目，默认false' 
          }
        }
      },
      response: {
        200: {
          description: 'Excel文件',
          type: 'string',
          format: 'binary'
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: { type: 'string' },
                  message: { type: 'string' }
                }
              }
            }
          }
        }
      }
    }
  }, financialExportController.exportFinancialReport.bind(financialExportController));

  // 获取可导出的年份列表
  fastify.get('/financial/export/years', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取可导出的年份列表',
      tags: ['Financial Export'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                years: {
                  type: 'array',
                  items: { type: 'number' }
                },
                currentYear: { type: 'number' },
                defaultYear: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialExportController.getAvailableYears.bind(financialExportController));

  // 获取导出预览信息
  fastify.get('/financial/export/preview', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取导出预览信息',
      tags: ['Financial Export'],
      querystring: {
        type: 'object',
        required: ['year'],
        properties: {
          year: { 
            type: 'string', 
            description: '导出年份',
            pattern: '^(202[0-9]|203[0-9])$'
          },
          brandIds: { 
            type: 'string', 
            description: '品牌ID列表，用逗号分隔' 
          },
          includeCompleted: { 
            type: 'string', 
            enum: ['true', 'false'],
            description: '是否包含已完成项目' 
          },
          includeCancelled: { 
            type: 'string', 
            enum: ['true', 'false'],
            description: '是否包含已取消项目' 
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                year: { type: 'number' },
                estimatedSheets: { type: 'number' },
                filters: {
                  type: 'object',
                  properties: {
                    brandIds: {
                      type: 'array',
                      items: { type: 'string' }
                    },
                    includeCompleted: { type: 'boolean' },
                    includeCancelled: { type: 'boolean' }
                  }
                },
                estimatedSize: { type: 'string' },
                estimatedTime: { type: 'string' },
                sheets: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      name: { type: 'string' },
                      description: { type: 'string' },
                      columns: { type: 'number' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialExportController.getExportPreview.bind(financialExportController));

  // 获取导出历史记录
  fastify.get('/financial/export/history', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取导出历史记录',
      tags: ['Financial Export'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                history: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      filename: { type: 'string' },
                      year: { type: 'number' },
                      exportedAt: { type: 'string' },
                      exportedBy: { type: 'string' },
                      fileSize: { type: 'string' },
                      status: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialExportController.getExportHistory.bind(financialExportController));

  // 导出一个文件的链接
  fastify.get('/financial/export/download', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '导出一个文件的链接',
      tags: ['Financial Export'],
      params: {
        type: 'object',
        properties: {
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                url: { type: 'string' },
                filename: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialExportController.getExportDownloadUrl.bind(financialExportController));
}
