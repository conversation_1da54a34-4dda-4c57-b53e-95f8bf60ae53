import { FastifyInstance } from "fastify";
import { AppController } from "../controllers/app.js";
import { jwtAuthMiddleware } from '../middleware/auth.js';

export async function appRoutes(fastify: FastifyInstance) {
  const appController = new AppController();

  // 获取应用配置信息
  fastify.get(
    "/app/config",
    {
      schema: {
        description: "获取应用配置信息",
        tags: ["App"],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  appKey: { type: "string" },
                  corpId: { type: "string" },
                  agentId: { type: "string" },
                  jsApiList: {
                    type: "array",
                    items: { type: "string" },
                  },
                  debug: { type: "boolean" },
                },
              },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.getAppConfig.bind(appController)
  );

  // 获取部门用户列表
  fastify.get(
    "/app/department/users",
    {
      schema: {
        description: "获取部门用户列表",
        tags: ["App"],
        querystring: {
          type: "object",
          // required: ['deptId'],
          properties: {
            deptId: {
              type: "number",
              description: "部门ID",
            },
            cursor: {
              type: "number",
              description: "分页游标",
              default: 0,
            },
            size: {
              type: "number",
              description: "每页数量",
              default: 100,
              maximum: 100,
            },
          },
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  list: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        userid: { type: "string" },
                        name: { type: "string" },
                        avatar: { type: "string" },
                        mobile: { type: "string" },
                        email: { type: "string" },
                      },
                    },
                  },
                  has_more: { type: "boolean" },
                  next_cursor: { type: "number" },
                },
              },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.getDepartmentUsers.bind(appController)
  );

  // 发送工作通知
  fastify.post(
    "/app/notification/send",
    {
      schema: {
        description: "发送工作通知",
        tags: ["App"],
        body: {
          type: "object",
          required: ["userIds", "title", "content"],
          properties: {
            userIds: {
              type: "array",
              items: { type: "string" },
              description: "接收用户ID列表",
            },
            title: {
              type: "string",
              description: "通知标题",
            },
            content: {
              type: "string",
              description: "通知内容",
            },
            messageType: {
              type: "string",
              enum: ["text", "markdown", "actionCard"],
              description: "消息类型",
              default: "text",
            },
          },
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  sent: { type: "boolean" },
                },
              },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.sendWorkNotification.bind(appController)
  );

  // 获取增强版JSAPI签名
  fastify.get(
    "/app/jsapi-signature/enhanced",
    {
      schema: {
        description: "获取增强版JSAPI签名（使用真实ticket）",
        tags: ["App"],
        querystring: {
          type: "object",
          required: ["url"],
          properties: {
            url: {
              type: "string",
              format: "uri",
              description: "当前页面URL",
            },
          },
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  agentId: { type: "string" },
                  corpId: { type: "string" },
                  timeStamp: { type: "number" },
                  nonceStr: { type: "string" },
                  signature: { type: "string" },
                },
              },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.getEnhancedJSAPISignature.bind(appController)
  );

  // 文件上传
  fastify.post(
    "/app/upload",
    {
      schema: {
        description: "上传单个文件",
        tags: ["App"],
        consumes: ["multipart/form-data"],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              filename: { type: "string" },
            },
          },
          500: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.uploadFile.bind(appController)
  );

  // 获取用户权限
  fastify.get(
    "/app/user/permissions",
    {
      schema: {
        description: "获取当前用户权限",
        tags: ["App"],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  userid: { type: "string" },
                  permissions: {
                    type: "array",
                    items: { type: "string" },
                  },
                  roles: {
                    type: "array",
                    items: { type: "string" },
                  },
                },
              },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.getUserPermissions.bind(appController)
  );

  // 获取应用统计信息
  fastify.get(
    "/app/stats",
    {
      schema: {
        description: "获取应用统计信息",
        tags: ["App"],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  totalUsers: { type: "number" },
                  totalDepartments: { type: "number" },
                  onlineUsers: { type: "number" },
                  messagesSent: { type: "number" },
                  lastUpdate: { type: "string" },
                },
              },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.getAppStats.bind(appController)
  );

  // 获取审批钉盘空间信息
  fastify.get(
    "/app/dingtalk/processinstance/cspace/space",
    {
      preHandler: [jwtAuthMiddleware],
      
      schema: {
        description: "获取审批钉盘空间信息",
        tags: ["App"],
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              data: {
                type: "object",
                properties: {
                  spaceId: { type: "string" },
                },
              },
              message: { type: "string" },
            },
          },
        },
      },
    },
    appController.getDingTalkSpaceId.bind(appController)
  );
}
