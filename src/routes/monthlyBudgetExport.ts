import { FastifyInstance } from 'fastify';
import { MonthlyBudgetExportController } from '../controllers/monthlyBudgetExport.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

export async function monthlyBudgetExportRoutes(fastify: FastifyInstance) {
  const monthlyBudgetExportController = new MonthlyBudgetExportController();

  // 导出当月周预算报表
  fastify.get('/monthly-budget/export', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.REPORT_EXPORT)],
    schema: {
      description: '导出当月周预算报表Excel',
      tags: ['Monthly Budget Export'],
      querystring: {
        type: 'object',
        required: ['year', 'month'],
        properties: {
          year: { 
            type: 'string', 
            description: '年份',
            pattern: '^(202[0-9]|203[0-9])$'
          },
          month: { 
            type: 'string', 
            description: '月份(1-12)',
            pattern: '^(1[0-2]|[1-9])$'
          }
        }
      },
      response: {
        200: {
          description: 'Excel文件',
          type: 'string',
          format: 'binary'
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: { type: 'string' },
                  message: { type: 'string' }
                }
              }
            }
          }
        }
      }
    }
  }, monthlyBudgetExportController.exportMonthlyBudgetReport.bind(monthlyBudgetExportController));

  // 获取当月周预算导出预览信息
  fastify.get('/monthly-budget/export/preview', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取当月周预算导出预览信息',
      tags: ['Monthly Budget Export'],
      querystring: {
        type: 'object',
        required: ['year', 'month'],
        properties: {
          year: { 
            type: 'string', 
            description: '年份',
            pattern: '^(202[0-9]|203[0-9])$'
          },
          month: { 
            type: 'string', 
            description: '月份(1-12)',
            pattern: '^(1[0-2]|[1-9])$'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                year: { type: 'number' },
                month: { type: 'number' },
                monthName: { type: 'string' },
                estimatedSheets: { type: 'number' },
                estimatedSize: { type: 'string' },
                estimatedTime: { type: 'string' },
                columns: {
                  type: 'array',
                  items: { type: 'string' }
                },
                description: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, monthlyBudgetExportController.getMonthlyBudgetExportPreview.bind(monthlyBudgetExportController));
}