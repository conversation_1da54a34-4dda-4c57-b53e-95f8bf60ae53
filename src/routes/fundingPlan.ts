import { FastifyInstance } from 'fastify';
import { FundingPlanController } from '../controllers/fundingPlan.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';

export async function fundingPlanRoutes(fastify: FastifyInstance) {
  const fundingPlanController = new FundingPlanController();

  // 资金计划管理路由

  // 创建资金计划
  fastify.post('/funding-plans', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_CREATE)],
    schema: {
      description: '创建资金计划',
      tags: ['FundingPlan'],
      body: {
        type: 'object',
        required: ['title', 'year', 'month', 'weekOfMonth', 'plannedAmount', 'budgetId'],
        properties: {
          title: { type: 'string', description: '计划标题' },
          year: { type: 'integer', minimum: 2020, maximum: 2050, description: '年份' },
          month: { type: 'integer', minimum: 1, maximum: 12, description: '月份' },
          weekOfMonth: { type: 'integer', minimum: 1, maximum: 5, description: '当月第几周' },
          plannedAmount: { type: 'number', minimum: 0, description: '计划金额' },
          remarks: { type: 'string', description: '备注' },
          budgetId: { type: 'string', description: '关联预算ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, fundingPlanController.createFundingPlan.bind(fundingPlanController));

  // 获取资金计划列表
  fastify.get('/funding-plans', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取资金计划列表',
      tags: ['FundingPlan'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1, description: '页码' },
          pageSize: { type: 'integer', minimum: 1, maximum: 100, default: 20, description: '每页数量' },
          budgetId: { type: 'string', description: '预算ID' },
          projectId: { type: 'string', description: '项目ID' },
          year: { type: 'integer', description: '年份' },
          month: { type: 'integer', minimum: 1, maximum: 12, description: '月份' },
          weekOfMonth: { type: 'integer', minimum: 1, maximum: 5, description: '当月第几周' },
          status: {
            type: 'string',
            enum: ['draft', 'submitted', 'approved', 'executing', 'completed', 'cancelled'],
            description: '计划状态'
          },
          sortBy: {
            type: 'string',
            enum: ['year', 'month', 'weekOfMonth', 'plannedAmount', 'createdAt'],
            default: 'year',
            description: '排序字段'
          },
          sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            default: 'desc',
            description: '排序方向'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                fundingPlans: { type: 'array' },
                total: { type: 'integer' },
                page: { type: 'integer' },
                pageSize: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, fundingPlanController.getFundingPlans.bind(fundingPlanController));

  // 获取预算的资金计划列表
  fastify.get('/budgets/:budgetId/funding-plans', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取预算的资金计划列表',
      tags: ['FundingPlan'],
      params: {
        type: 'object',
        required: ['budgetId'],
        properties: {
          budgetId: { type: 'string', description: '预算ID' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1, description: '页码' },
          pageSize: { type: 'integer', minimum: 1, maximum: 100, default: 20, description: '每页数量' },
          year: { type: 'integer', description: '年份' },
          month: { type: 'integer', minimum: 1, maximum: 12, description: '月份' },
          weekOfMonth: { type: 'integer', minimum: 1, maximum: 5, description: '当月第几周' },
          status: {
            type: 'string',
            enum: ['draft', 'submitted', 'approved', 'executing', 'completed', 'cancelled'],
            description: '计划状态'
          },
          sortBy: {
            type: 'string',
            enum: ['year', 'month', 'weekOfMonth', 'plannedAmount', 'createdAt'],
            default: 'year',
            description: '排序字段'
          },
          sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            default: 'desc',
            description: '排序方向'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                fundingPlans: { type: 'array' },
                total: { type: 'integer' },
                page: { type: 'integer' },
                pageSize: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { budgetId } = request.params as { budgetId: string };
    const query = { ...request.query, budgetId };
    request.query = query;
    return fundingPlanController.getFundingPlans(request, reply);
  });

  // 获取资金计划详情
  fastify.get('/funding-plans/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取资金计划详情',
      tags: ['FundingPlan'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '资金计划ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, fundingPlanController.getFundingPlan.bind(fundingPlanController));

  // 更新资金计划
  fastify.put('/funding-plans/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_UPDATE)],
    schema: {
      description: '更新资金计划',
      tags: ['FundingPlan'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '资金计划ID' }
        }
      },
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', description: '计划标题' },
          year: { type: 'integer', minimum: 2020, maximum: 2050, description: '年份' },
          month: { type: 'integer', minimum: 1, maximum: 12, description: '月份' },
          weekOfMonth: { type: 'integer', minimum: 1, maximum: 5, description: '当月第几周' },
          plannedAmount: { type: 'number', minimum: 0, description: '计划金额' },
          paidAmount: { type: 'number', minimum: 0, description: '已付金额' },
          status: {
            type: 'string',
            enum: ['draft', 'submitted', 'approved', 'executing', 'completed', 'cancelled'],
            description: '计划状态'
          },
          approvalAmount: { type: 'number', minimum: 0, description: '审批金额' },
          approvalReason: { type: 'string', description: '审批原因' },
          remarks: { type: 'string', description: '备注' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, fundingPlanController.updateFundingPlan.bind(fundingPlanController));

  // 删除资金计划
  fastify.delete('/funding-plans/:id', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_DELETE)],
    schema: {
      description: '删除资金计划',
      tags: ['FundingPlan'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '资金计划ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, fundingPlanController.deleteFundingPlan.bind(fundingPlanController));

  // 提交资金计划审批
  fastify.post('/funding-plans/:id/approval', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.APPROVAL_CREATE)],
    schema: {
      description: '提交资金计划审批',
      tags: ['FundingPlan'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '资金计划ID' }
        }
      },
      body: {
        type: 'object',
        required: ['totalAmount', 'paymentReason', 'contractEntity', 'expectedPaymentDate', 'department', 'paymentMethod', 'receivingAccount'],
        properties: {
          totalAmount: { type: 'number', minimum: 0, description: '付款总额' },
          paymentReason: { type: 'string', description: '付款事由' },
          contractEntity: {
            type: 'string',
            enum: ['CANTV', 'CANTV_TECH'],
            description: '合同签署主体'
          },
          expectedPaymentDate: { type: 'string', format: 'date', description: '期望付款时间' },
          department: { type: 'integer', description: '申请部门' },
          paymentMethod: {
            type: 'string',
            enum: ['BANK_TRANSFER', 'ALIPAY', 'WECHAT_PAY', 'CASH', 'CHECK'],
            description: '付款方式'
          },
          receivingAccount: {
            type: 'object',
            required: ['accountName', 'accountNumber', 'bankName'],
            properties: {
              accountName: { type: 'string', description: '账户名称' },
              accountNumber: { type: 'string', description: '账号' },
              bankName: { type: 'string', description: '开户银行' },
              bankCode: { type: 'string', description: '银行代码' }
            }
          },
          relatedApprovalId: { type: 'string', description: '关联审批单' },
          invoiceFiles: { type: 'array', description: '发票文件' },
          attachments: { type: 'array', description: '附件' },
          remark: { type: 'string', description: '备注' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, fundingPlanController.submitApproval.bind(fundingPlanController));

  // 获取资金计划统计
  fastify.get('/funding-plan-stats', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.BUDGET_VIEW)],
    schema: {
      description: '获取资金计划统计',
      tags: ['FundingPlan'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, fundingPlanController.getFundingPlanStats.bind(fundingPlanController));
}
