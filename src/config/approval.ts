import { env } from './env.js';

// 审批流程配置
export const APPROVAL_CONFIG = {
  // 审批流程代码
  PROCESS_CODES: {
    PAYMENT: env.APPROVAL_PROCESS_CODE_PAYMENT,     // 对公付款审批
    EXPENSE: env.APPROVAL_PROCESS_CODE_EXPENSE,     // 费用报销审批
    CONTRACT: env.APPROVAL_PROCESS_CODE_CONTRACT,   // 合同审批
    PURCHASE: 'PROC-PURCHASE-001',                  // 采购审批
    LEAVE: 'PROC-LEAVE-001',                        // 请假审批
  },

  // 审批表单字段配置
  FORM_FIELDS: {
    PAYMENT: {
      // 申请人信息
      APPLICANT: 'applicant',
      APPLICANT_NAME: 'applicantName',
      DEPARTMENT: 'department',

      // 审批信息
      RELATED_APPROVAL_ID: 'relatedApprovalId',
      PROJECT_ID: 'projectId',
      PROJECT_NAME: 'projectName',

      // 付款信息
      PAYMENT_REASON: 'paymentReason',
      CONTRACT_ENTITY: 'contractEntity',
      TOTAL_AMOUNT: 'totalAmount',
      EXPECTED_PAYMENT_DATE: 'expectedPaymentDate',
      PAYMENT_METHOD: 'paymentMethod',

      // 收款信息
      RECEIVING_ACCOUNT: 'receivingAccount',

      // 附件信息
      INVOICE_FILES: 'invoiceFiles',
      ATTACHMENTS: 'attachments',
      REMARK: 'remark'
    }
  },

  // 审批状态映射
  STATUS_MAPPING: {
    DINGTALK_TO_SYSTEM: {
      'NEW': 'PENDING',
      'RUNNING': 'PENDING',
      'TERMINATED': 'CANCELLED',
      'COMPLETED': 'APPROVED',
      'CANCELED': 'CANCELLED'
    },
    SYSTEM_TO_DINGTALK: {
      'NONE': 'NEW',
      'PENDING': 'RUNNING',
      'APPROVED': 'COMPLETED',
      'REJECTED': 'TERMINATED',
      'CANCELLED': 'CANCELED'
    }
  },

  // 审批结果映射
  RESULT_MAPPING: {
    DINGTALK_TO_SYSTEM: {
      'agree': 'APPROVED',
      'refuse': 'REJECTED',
      'redirect': 'PENDING'
    },
    SYSTEM_TO_DINGTALK: {
      'APPROVED': 'agree',
      'REJECTED': 'refuse',
      'PENDING': 'redirect'
    }
  },

  // 合同签署主体配置
  CONTRACT_ENTITIES: {
    COMPANY_A: 'company_a',
    COMPANY_B: 'company_b',
    SUBSIDIARY: 'subsidiary',
    OTHER: 'other'
  },

  // 付款方式配置
  PAYMENT_METHODS: {
    BANK_TRANSFER: 'bank_transfer',
    ONLINE_PAYMENT: 'online_payment',
    CHECK: 'check',
    CASH: 'cash',
    OTHER: 'other'
  },

  // 文件类型配置
  FILE_TYPES: {
    INVOICE: 'invoice',
    ATTACHMENT: 'attachment',
    CONTRACT: 'contract',
    OTHER: 'other'
  },

  // 审批超时配置（小时）
  TIMEOUT_CONFIG: {
    DEFAULT: 72,        // 默认72小时
    URGENT: 24,         // 紧急24小时
    NORMAL: 48,         // 普通48小时
    LOW_PRIORITY: 168   // 低优先级168小时（7天）
  },

  // 审批金额阈值配置
  AMOUNT_THRESHOLDS: {
    SMALL: 10000,       // 小额：1万以下
    MEDIUM: 100000,     // 中额：1万-10万
    LARGE: 1000000,     // 大额：10万-100万
    HUGE: ********      // 巨额：100万以上
  },

  // 钉钉API配置
  DINGTALK_CONFIG: {
    // API端点
    ENDPOINTS: {
      CREATE_PROCESS: '/topapi/processinstance/create',
      GET_PROCESS: '/topapi/processinstance/get',
      LIST_PROCESS: '/topapi/processinstance/listids',
      UPLOAD_MEDIA: '/media/upload'
    },

    // 回调事件类型
    CALLBACK_EVENTS: {
      INSTANCE_CHANGE: 'bpms_instance_change',
      TASK_CHANGE: 'bpms_task_change'
    },

    // 回调操作类型
    CALLBACK_TYPES: {
      START: 'start',
      FINISH: 'finish',
      TERMINATE: 'terminate'
    }
  },

  // 错误代码配置
  ERROR_CODES: {
    INVALID_PROCESS_CODE: 'APPROVAL_001',
    INVALID_FORM_DATA: 'APPROVAL_002',
    DINGTALK_API_ERROR: 'APPROVAL_003',
    APPROVAL_NOT_FOUND: 'APPROVAL_004',
    PERMISSION_DENIED: 'APPROVAL_005',
    TIMEOUT_ERROR: 'APPROVAL_006'
  },

  // 默认配置
  DEFAULTS: {
    PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    RETRY_TIMES: 3,
    RETRY_DELAY: 1000, // 毫秒
    CACHE_TTL: 300     // 缓存时间（秒）
  }
} as const;

// 类型定义
export type ProcessCode = typeof APPROVAL_CONFIG.PROCESS_CODES[keyof typeof APPROVAL_CONFIG.PROCESS_CODES];
export type ApprovalStatus = 'NONE' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
export type ApprovalResult = 'agree' | 'refuse' | 'redirect';
export type ContractEntity = typeof APPROVAL_CONFIG.CONTRACT_ENTITIES[keyof typeof APPROVAL_CONFIG.CONTRACT_ENTITIES];
export type PaymentMethod = typeof APPROVAL_CONFIG.PAYMENT_METHODS[keyof typeof APPROVAL_CONFIG.PAYMENT_METHODS];
export type FileType = typeof APPROVAL_CONFIG.FILE_TYPES[keyof typeof APPROVAL_CONFIG.FILE_TYPES];

// 辅助函数
export class ApprovalConfigHelper {
  /**
   * 获取审批流程代码
   */
  static getProcessCode(type: keyof typeof APPROVAL_CONFIG.PROCESS_CODES): string {
    return APPROVAL_CONFIG.PROCESS_CODES[type];
  }

  /**
   * 钉钉状态转系统状态
   */
  static mapDingTalkStatusToSystem(dingTalkStatus: string): ApprovalStatus {
    return APPROVAL_CONFIG.STATUS_MAPPING.DINGTALK_TO_SYSTEM[dingTalkStatus as keyof typeof APPROVAL_CONFIG.STATUS_MAPPING.DINGTALK_TO_SYSTEM] || 'PENDING';
  }

  /**
   * 系统状态转钉钉状态
   */
  static mapSystemStatusToDingTalk(systemStatus: ApprovalStatus): string {
    return APPROVAL_CONFIG.STATUS_MAPPING.SYSTEM_TO_DINGTALK[systemStatus] || 'RUNNING';
  }

  /**
   * 钉钉结果转系统结果
   */
  static mapDingTalkResultToSystem(dingTalkResult: string): ApprovalStatus {
    return APPROVAL_CONFIG.RESULT_MAPPING.DINGTALK_TO_SYSTEM[dingTalkResult as keyof typeof APPROVAL_CONFIG.RESULT_MAPPING.DINGTALK_TO_SYSTEM] || 'PENDING';
  }

  /**
   * 根据金额获取审批优先级
   */
  static getApprovalPriority(amount: number): 'LOW_PRIORITY' | 'NORMAL' | 'URGENT' {
    if (amount >= APPROVAL_CONFIG.AMOUNT_THRESHOLDS.HUGE) {
      return 'URGENT';
    } else if (amount >= APPROVAL_CONFIG.AMOUNT_THRESHOLDS.LARGE) {
      return 'NORMAL';
    } else {
      return 'LOW_PRIORITY';
    }
  }

  /**
   * 获取审批超时时间
   */
  static getApprovalTimeout(priority: keyof typeof APPROVAL_CONFIG.TIMEOUT_CONFIG): number {
    return APPROVAL_CONFIG.TIMEOUT_CONFIG[priority];
  }

  /**
   * 验证流程代码
   */
  static isValidProcessCode(code: string): boolean {
    return Object.values(APPROVAL_CONFIG.PROCESS_CODES).includes(code as ProcessCode);
  }

  /**
   * 验证合同签署主体
   */
  static isValidContractEntity(entity: string): boolean {
    return Object.values(APPROVAL_CONFIG.CONTRACT_ENTITIES).includes(entity as ContractEntity);
  }

  /**
   * 验证付款方式
   */
  static isValidPaymentMethod(method: string): boolean {
    return Object.values(APPROVAL_CONFIG.PAYMENT_METHODS).includes(method as PaymentMethod);
  }
}

export default APPROVAL_CONFIG;
