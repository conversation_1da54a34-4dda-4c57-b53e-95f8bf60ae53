import { DatabaseService } from './database.js';
import { DingTalkService } from './dingtalk.js';
import { UserSyncService } from './userSync.js';

export interface SchedulerOptions {
  syncIntervalMinutes?: number;  // 同步间隔（分钟）
  enableAutoSync?: boolean;      // 是否启用自动同步
  maxConcurrentSyncs?: number;   // 最大并发同步数
}

export class UserSyncScheduler {
  private userSyncService: UserSyncService;
  private options: Required<SchedulerOptions>;
  private syncTimer: NodeJS.Timeout | null = null;
  private isRunning = false;
  private lastSyncTime: Date | null = null;
  private syncStats = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    lastSyncDuration: 0
  };

  constructor(options: SchedulerOptions = {}) {
    const databaseService = new DatabaseService();
    const dingTalkService = new DingTalkService();
    this.userSyncService = new UserSyncService(databaseService, dingTalkService);
    
    this.options = {
      syncIntervalMinutes: options.syncIntervalMinutes || 60, // 默认每小时同步一次
      enableAutoSync: options.enableAutoSync !== false,       // 默认启用
      maxConcurrentSyncs: options.maxConcurrentSyncs || 1,    // 默认不并发
    };
  }

  /**
   * 启动定时同步
   */
  start(): void {
    if (this.isRunning) {
      console.log('用户同步调度器已在运行中');
      return;
    }

    if (!this.options.enableAutoSync) {
      console.log('自动用户同步已禁用');
      return;
    }

    this.isRunning = true;
    console.log(`启动用户同步调度器，同步间隔: ${this.options.syncIntervalMinutes} 分钟`);

    // 立即执行一次同步
    this.performSync();

    // 设置定时器
    this.syncTimer = setInterval(() => {
      this.performSync();
    }, this.options.syncIntervalMinutes * 60 * 1000);
  }

  /**
   * 停止定时同步
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('用户同步调度器未在运行');
      return;
    }

    this.isRunning = false;
    
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }

    console.log('用户同步调度器已停止');
  }

  /**
   * 执行同步
   */
  private async performSync(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    const startTime = Date.now();
    this.syncStats.totalSyncs++;

    try {
      console.log('开始定时用户同步...');
      
      const results = await this.userSyncService.syncAllUsers();
      
      this.syncStats.successfulSyncs++;
      this.lastSyncTime = new Date();
      this.syncStats.lastSyncDuration = Date.now() - startTime;

      console.log(`定时用户同步完成: 成功 ${results.success}, 失败 ${results.failed}, 耗时 ${this.syncStats.lastSyncDuration}ms`);

      if (results.errors.length > 0) {
        console.warn('同步过程中出现错误:', results.errors.slice(0, 5)); // 只显示前5个错误
      }

    } catch (error) {
      this.syncStats.failedSyncs++;
      this.syncStats.lastSyncDuration = Date.now() - startTime;
      
      console.error('定时用户同步失败:', error);
    }
  }

  /**
   * 手动触发同步
   */
  async triggerSync(): Promise<{
    success: number;
    failed: number;
    errors: Array<{ userid: string; error: string }>;
  }> {
    console.log('手动触发用户同步...');
    
    const startTime = Date.now();
    
    try {
      const results = await this.userSyncService.syncAllUsers();
      
      this.lastSyncTime = new Date();
      this.syncStats.lastSyncDuration = Date.now() - startTime;
      
      console.log(`手动用户同步完成: 成功 ${results.success}, 失败 ${results.failed}, 耗时 ${this.syncStats.lastSyncDuration}ms`);
      
      return results;
      
    } catch (error) {
      this.syncStats.lastSyncDuration = Date.now() - startTime;
      console.error('手动用户同步失败:', error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   */
  getStatus(): {
    isRunning: boolean;
    lastSyncTime: Date | null;
    nextSyncTime: Date | null;
    syncIntervalMinutes: number;
    stats: {
      totalSyncs: number;
      successfulSyncs: number;
      failedSyncs: number;
      lastSyncDuration: number;
    };
  } {
    const nextSyncTime = this.isRunning && this.lastSyncTime 
      ? new Date(this.lastSyncTime.getTime() + this.options.syncIntervalMinutes * 60 * 1000)
      : null;

    return {
      isRunning: this.isRunning,
      lastSyncTime: this.lastSyncTime,
      nextSyncTime,
      syncIntervalMinutes: this.options.syncIntervalMinutes,
      stats: { ...this.syncStats }
    };
  }

  /**
   * 更新配置
   */
  updateOptions(newOptions: Partial<SchedulerOptions>): void {
    const wasRunning = this.isRunning;
    
    // 停止当前调度器
    if (wasRunning) {
      this.stop();
    }

    // 更新配置
    Object.assign(this.options, newOptions);

    // 如果之前在运行且新配置启用自动同步，则重新启动
    if (wasRunning && this.options.enableAutoSync) {
      this.start();
    }

    console.log('用户同步调度器配置已更新:', this.options);
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.syncStats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncDuration: 0
    };
    console.log('用户同步统计信息已重置');
  }
}

// 创建全局实例
export const userSyncScheduler = new UserSyncScheduler({
  syncIntervalMinutes: 60,  // 每小时同步一次
  enableAutoSync: true,     // 启用自动同步
  maxConcurrentSyncs: 1     // 不并发执行
});

// 在应用启动时自动启动调度器
if (process.env.NODE_ENV === 'production') {
  // 延迟启动，确保应用完全初始化
  setTimeout(() => {
    userSyncScheduler.start();
  }, 5000); // 5秒后启动
}

