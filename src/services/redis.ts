import { createClient, RedisClientType } from 'redis';
import { env } from '../config/env.js';

export class RedisService {
  private client: RedisClientType;
  private isConnected: boolean = false;

  constructor() {
    // 创建 Redis 客户端
    this.client = createClient({
      url: env.REDIS_URL || 'redis://localhost:6379',
      password: env.REDIS_PASSWORD,
      database: env.REDIS_DB || 0,
      socket: {
        connectTimeout: 5000,
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            console.error('Redis 重连次数超过限制，停止重连');
            return false;
          }
          return Math.min(retries * 100, 3000);
        }
      }
    });

    // 监听连接事件
    this.client.on('connect', () => {
      console.log('🔗 Redis 连接已建立');
    });

    this.client.on('ready', () => {
      console.log('✅ Redis 客户端已就绪');
      this.isConnected = true;
    });

    this.client.on('error', (error) => {
      console.error('❌ Redis 连接错误:', error);
      this.isConnected = false;
    });

    this.client.on('end', () => {
      console.log('🔌 Redis 连接已断开');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      console.log('🔄 Redis 正在重连...');
    });
  }

  /**
   * 连接到 Redis
   */
  async connect(): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.client.connect();
      }
    } catch (error) {
      console.error('Redis 连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开 Redis 连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.client.disconnect();
      }
    } catch (error) {
      console.error('Redis 断开连接失败:', error);
    }
  }

  /**
   * 检查连接状态
   */
  isReady(): boolean {
    return this.isConnected && this.client.isReady;
  }

  /**
   * 设置键值对
   */
  async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.client.setEx(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
    } catch (error) {
      console.error(`Redis SET 操作失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 获取值
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key);
    } catch (error) {
      console.error(`Redis GET 操作失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 删除键
   */
  async del(key: string): Promise<number> {
    try {
      return await this.client.del(key);
    } catch (error) {
      console.error(`Redis DEL 操作失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`Redis EXISTS 操作失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, seconds);
      return result === 1;
    } catch (error) {
      console.error(`Redis EXPIRE 操作失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      console.error(`Redis TTL 操作失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 批量获取
   */
  async mget(keys: string[]): Promise<(string | null)[]> {
    try {
      return await this.client.mGet(keys);
    } catch (error) {
      console.error('Redis MGET 操作失败:', error);
      throw error;
    }
  }

  /**
   * 批量设置
   */
  async mset(keyValues: Record<string, string>): Promise<void> {
    try {
      await this.client.mSet(keyValues);
    } catch (error) {
      console.error('Redis MSET 操作失败:', error);
      throw error;
    }
  }

  /**
   * 获取匹配模式的所有键
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      console.error(`Redis KEYS 操作失败 [${pattern}]:`, error);
      throw error;
    }
  }

  /**
   * 批量删除匹配模式的键
   */
  async delPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.keys(pattern);
      if (keys.length === 0) {
        return 0;
      }
      return await this.client.del(keys);
    } catch (error) {
      console.error(`Redis 批量删除操作失败 [${pattern}]:`, error);
      throw error;
    }
  }

  /**
   * 哈希表操作 - 设置字段
   */
  async hset(key: string, field: string, value: string): Promise<number> {
    try {
      return await this.client.hSet(key, field, value);
    } catch (error) {
      console.error(`Redis HSET 操作失败 [${key}.${field}]:`, error);
      throw error;
    }
  }

  /**
   * 哈希表操作 - 获取字段
   */
  async hget(key: string, field: string): Promise<string | undefined> {
    try {
      const result = await this.client.hGet(key, field);
      return result || undefined;
    } catch (error) {
      console.error(`Redis HGET 操作失败 [${key}.${field}]:`, error);
      throw error;
    }
  }

  /**
   * 哈希表操作 - 获取所有字段
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    try {
      return await this.client.hGetAll(key);
    } catch (error) {
      console.error(`Redis HGETALL 操作失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 哈希表操作 - 删除字段
   */
  async hdel(key: string, field: string): Promise<number> {
    try {
      return await this.client.hDel(key, field);
    } catch (error) {
      console.error(`Redis HDEL 操作失败 [${key}.${field}]:`, error);
      throw error;
    }
  }

  /**
   * 获取 Redis 信息
   */
  async info(): Promise<string> {
    try {
      return await this.client.info();
    } catch (error) {
      console.error('Redis INFO 操作失败:', error);
      throw error;
    }
  }

  /**
   * Ping Redis 服务器
   */
  async ping(): Promise<string> {
    try {
      return await this.client.ping();
    } catch (error) {
      console.error('Redis PING 操作失败:', error);
      throw error;
    }
  }

  /**
   * 获取原始客户端（用于高级操作）
   */
  getClient(): RedisClientType {
    return this.client;
  }
}

// 单例实例
export const redisService = new RedisService();

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('正在关闭 Redis 连接...');
  await redisService.disconnect();
});

process.on('SIGTERM', async () => {
  console.log('正在关闭 Redis 连接...');
  await redisService.disconnect();
});
