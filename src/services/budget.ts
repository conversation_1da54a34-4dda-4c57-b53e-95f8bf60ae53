import { db } from './database.js';

export class BudgetService {
  private useDatabase = true;

  /**
   * 根据预算id获取剩余可用的金额
   * @param budgetId 预算ID
   */
  getBudgetAvailableAmount(budgetId: string) {
    // 获取预算的总金额和已支付的金额
    return db.getBudgetAvailableAmount(budgetId);
  }

  /**
   * 批量验证预算可用金额
   * @param plans 资金计划数组
   * @returns 验证结果，包含每个预算的可用金额和验证状态
   */
  async validateBatchBudgetAvailability(plans: Array<{ budgetId: string; plannedAmount: number }>) {
    return db.validateBatchBudgetAvailability(plans);
  }
}