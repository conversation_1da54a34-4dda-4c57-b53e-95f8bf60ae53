import { DatabaseService } from './database.js';
import { DingTalkService } from './dingtalk.js';

export interface UserSyncOptions {
  syncIntervalHours?: number;  // 同步间隔（小时）
  batchSize?: number;          // 批量处理大小
  maxRetries?: number;         // 最大重试次数
}

export class UserSyncService {
  private databaseService: DatabaseService;
  private dingTalkService: DingTalkService;
  private options: Required<UserSyncOptions>;

  constructor(
    databaseService: DatabaseService,
    dingTalkService: DingTalkService,
    options: UserSyncOptions = {}
  ) {
    this.databaseService = databaseService;
    this.dingTalkService = dingTalkService;
    this.options = {
      syncIntervalHours: options.syncIntervalHours || 24,
      batchSize: options.batchSize || 50,
      maxRetries: options.maxRetries || 3,
    };
  }

  /**
   * 同步指定用户ID列表的用户信息
   */
  async syncUsersByIds(userIds: string[]): Promise<{
    success: number;
    failed: number;
    errors: Array<{ userid: string; error: string }>;
  }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ userid: string; error: string }>
    };

    // 检查哪些用户需要同步
    const usersNeedSync = await this.databaseService.getUsersNeedSync(
      userIds,
      this.options.syncIntervalHours
    );

    if (usersNeedSync.length === 0) {
      console.log('所有用户信息都是最新的，无需同步');
      return results;
    }

    console.log(`需要同步 ${usersNeedSync.length} 个用户信息`);

    // 分批处理用户同步
    for (let i = 0; i < usersNeedSync.length; i += this.options.batchSize) {
      const batch = usersNeedSync.slice(i, i + this.options.batchSize);
      
      const batchResults = await Promise.allSettled(
        batch.map(userid => this.syncSingleUser(userid))
      );

      batchResults.forEach((result, index) => {
        const userid = batch[index];
        if (!userid) {
          console.error(`批次中第 ${index} 个用户ID为空，跳过处理`);
          return;
        }

        if (result.status === 'fulfilled') {
          results.success++;
          console.log(`用户 ${userid} 同步成功`);
        } else {
          results.failed++;
          results.errors.push({
            userid,
            error: result.reason?.message || '未知错误'
          });
          console.error(`用户 ${userid} 同步失败:`, result.reason);
        }
      });
    }

    console.log(`用户同步完成: 成功 ${results.success}, 失败 ${results.failed}`);
    return results;
  }

  /**
   * 同步单个用户信息
   */
  private async syncSingleUser(userid: string): Promise<void> {
    let retries = 0;
    
    while (retries < this.options.maxRetries) {
      try {
        // 从钉钉API获取用户详细信息
        const userDetail = await this.dingTalkService.getUserDetail(userid);
        
        if (!userDetail) {
          throw new Error('用户不存在或已被删除');
        }

        // 转换为数据库格式
        const userData = {
          userid: userDetail.userid,
          unionid: userDetail.unionid || undefined,
          name: userDetail.name,
          avatar: userDetail.avatar || undefined,
          stateCode: userDetail.state_code || undefined,
          managerUserid: userDetail.manager_userid || undefined,
          mobile: userDetail.mobile || undefined,
          hideMobile: userDetail.hide_mobile || false,
          telephone: userDetail.telephone || undefined,
          jobNumber: userDetail.job_number || undefined,
          title: userDetail.title || undefined,
          email: userDetail.email || undefined,
          workPlace: userDetail.work_place || undefined,
          remark: userDetail.remark || undefined,
          loginId: userDetail.login_id || undefined,
          exclusiveAccountType: userDetail.exclusive_account_type || undefined,
          exclusiveAccount: userDetail.exclusive_account || false,
          deptIdList: userDetail.dept_id_list || [],
          extension: userDetail.extension || undefined,
          hiredDate: userDetail.hired_date ? new Date(userDetail.hired_date) : undefined,
          active: userDetail.active !== false, // 默认为true
          realAuthed: userDetail.real_authed || false,
          orgEmail: userDetail.org_email || undefined,
          orgEmailType: userDetail.org_email_type || undefined,
          senior: userDetail.senior || false,
          admin: userDetail.admin || false,
          boss: userDetail.boss || false,
        };

        // 保存到数据库
        await this.databaseService.upsertUser(userData);
        return; // 成功，退出重试循环
        
      } catch (error) {
        retries++;
        console.warn(`同步用户 ${userid} 失败 (第${retries}次重试):`, error);
        
        if (retries >= this.options.maxRetries) {
          throw error; // 达到最大重试次数，抛出错误
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }
  }

  /**
   * 同步所有用户信息
   */
  async syncAllUsers(): Promise<{
    success: number;
    failed: number;
    errors: Array<{ userid: string; error: string }>;
  }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ userid: string; error: string }>
    };

    try {
      console.log('开始同步所有用户信息...');
      
      // 获取所有用户列表
      let cursor = 0;
      const size = 100;
      let hasMore = true;
      const allUserIds: string[] = [];

      while (hasMore) {
        try {
          const userListResult = await this.dingTalkService.getAllUsers(cursor, size);
          
          if (userListResult?.list) {
            const userIds = userListResult.list.map(user => user.userid);
            allUserIds.push(...userIds);
          }

          hasMore = userListResult?.has_more || false;
          cursor = userListResult?.next_cursor || 0;
          
        } catch (error) {
          console.error('获取用户列表失败:', error);
          break;
        }
      }

      console.log(`获取到 ${allUserIds.length} 个用户，开始同步...`);

      // 同步所有用户
      const syncResults = await this.syncUsersByIds(allUserIds);
      
      return syncResults;
      
    } catch (error) {
      console.error('同步所有用户失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息（优先从本地数据库）
   */
  async getUserInfo(userid: string): Promise<{
    userid: string;
    name: string;
    avatar?: string;
    department?: string;
  } | null> {
    try {
      // 首先尝试从本地数据库获取
      const localUser = await this.databaseService.getUser(userid);
      
      if (localUser && localUser.isActive) {
        // 检查是否需要同步
        const cutoffTime = new Date(Date.now() - this.options.syncIntervalHours * 60 * 60 * 1000);
        
        if (localUser.lastSyncAt > cutoffTime) {
          // 本地数据是最新的
          return {
            userid: localUser.userid,
            name: localUser.name,
            avatar: localUser.avatar || undefined,
            department: localUser.deptIdList?.map(String).join(', ') || undefined
          };
        }
      }

      // 本地没有数据或数据过期，从钉钉API同步
      await this.syncSingleUser(userid);
      
      // 重新从数据库获取
      const updatedUser = await this.databaseService.getUser(userid);
      
      if (updatedUser) {
        return {
          userid: updatedUser.userid,
          name: updatedUser.name,
          avatar: updatedUser.avatar || undefined,
          department: updatedUser.deptIdList?.map(String).join(', ') || undefined
        };
      }

      return null;
      
    } catch (error) {
      console.error(`获取用户信息失败: ${userid}`, error);
      return null;
    }
  }

  /**
   * 批量获取用户信息
   */
  async getUsersInfo(userIds: string[]): Promise<Array<{
    userid: string;
    name: string;
    avatar?: string;
    department?: string;
  }>> {
    // 先尝试从本地数据库批量获取
    const localUsers = await this.databaseService.getUsers(userIds);
    const localUserMap = new Map(localUsers.map(user => [user.userid, user]));
    
    const results: Array<{
      userid: string;
      name: string;
      avatar?: string;
      department?: string;
    }> = [];
    
    const usersNeedSync: string[] = [];
    const cutoffTime = new Date(Date.now() - this.options.syncIntervalHours * 60 * 60 * 1000);

    // 检查哪些用户需要同步
    for (const userid of userIds) {
      const localUser = localUserMap.get(userid);
      
      if (localUser && localUser.isActive && localUser.lastSyncAt > cutoffTime) {
        // 本地数据是最新的
        results.push({
          userid: localUser.userid,
          name: localUser.name,
          avatar: localUser.avatar || undefined,
          department: localUser.deptIdList?.map(String).join(', ') || undefined
        });
      } else {
        // 需要同步
        usersNeedSync.push(userid);
      }
    }

    // 同步需要更新的用户
    if (usersNeedSync.length > 0) {
      await this.syncUsersByIds(usersNeedSync);
      
      // 重新获取同步后的用户信息
      const syncedUsers = await this.databaseService.getUsers(usersNeedSync);
      
      for (const user of syncedUsers) {
        results.push({
          userid: user.userid,
          name: user.name,
          avatar: user.avatar || undefined,
          department: user.deptIdList?.map(String).join(', ') || undefined
        });
      }
    }

    // 按原始顺序排序
    const orderedResults = userIds.map(userid => 
      results.find(user => user.userid === userid)
    ).filter(Boolean) as Array<{
      userid: string;
      name: string;
      avatar?: string;
      department?: string;
    }>;

    return orderedResults;
  }
}
