/**
 * 当月周预算Excel导出服务
 * 导出字段：项目名称,供应商名称,服务内容,合同成本,专票税率,不含税金额,已付金额,未付金额,七月总应付,第一周,第二周,第三周,第四周,第五周
 */

import ExcelJS from 'exceljs';
import { WeeklyBudget, Supplier, Project } from '../types/project.js';
import { TimezoneUtils } from '../utils/timezone.js';
import { ProjectService } from './project.js';

export interface MonthlyBudgetExportParams {
  year: number;      // 年份
  month: number;     // 月份 (1-12)
}

export interface MonthlyBudgetRow {
  projectName: string;          // 项目名称
  supplierName: string;         // 供应商名称
  serviceContent: string;       // 服务内容
  contractCost: number;         // 合同成本
  taxRate: string;              // 专票税率
  taxExcludedAmount: number;    // 不含税金额
  paidAmount: number;           // 已付金额
  unpaidAmount: number;         // 未付金额
  julyTotalPayable: number;     // 七月总应付
  week1: number;                // 第一周
  week2: number;                // 第二周
  week3: number;                // 第三周
  week4: number;                // 第四周
  week5: number;                // 第五周
}

export class MonthlyBudgetExportService {
  private projectService: ProjectService;

  constructor() {
    this.projectService = new ProjectService();
  }

  /**
   * 导出当月周预算Excel
   */
  async exportMonthlyBudgetReport(params: MonthlyBudgetExportParams): Promise<Buffer> {
    console.log('🚀 开始生成当月周预算报表...', params);

    // 获取基础数据
    const { weeklyBudgets, projects, suppliers } = await this.getMonthlyData(params);

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'CanTV财务系统';
    workbook.created = TimezoneUtils.now();
    workbook.company = 'CanTV';
    workbook.title = `${params.year}年${params.month}月周预算报表`;
    workbook.subject = `${params.year}年${params.month}月周预算数据`;
    workbook.description = `包含${params.year}年${params.month}月份所有有周预算的项目财务数据。`;

    // 创建当月周预算表
    await this.createMonthlyBudgetSheet(workbook, weeklyBudgets, projects, suppliers, params);

    // 生成Buffer
    const buffer = await workbook.xlsx.writeBuffer();
    console.log('✅ 当月周预算报表生成完成');

    return buffer as Buffer;
  }

  /**
   * 获取当月数据
   */
  private async getMonthlyData(params: MonthlyBudgetExportParams) {
    const startDate = new Date(params.year, params.month - 1, 1); // 月初
    const endDate = new Date(params.year, params.month, 0, 23, 59, 59); // 月末

    console.log(`📊 获取${params.year}年${params.month}月数据...`);
    console.log(`时间范围: ${startDate.toISOString()} ~ ${endDate.toISOString()}`);

    // 获取当月所有周预算数据
    const weeklyBudgetsResult = await this.projectService.getWeeklyBudgets({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      pageSize: 10000 // 获取所有数据
    });
    const weeklyBudgets = weeklyBudgetsResult.weeklyBudgets;

    // 获取相关项目数据
    const projectIds = [...new Set(weeklyBudgets.map(wb => wb.projectId))];
    const projects: Project[] = [];
    for (const projectId of projectIds) {
      try {
        const project = await this.projectService.getProject(projectId);
        if (project) {
          projects.push(project);
        }
      } catch (error) {
        console.warn(`获取项目 ${projectId} 数据失败:`, error);
      }
    }

    // 获取品牌数据（暂时不用，但保留接口）
    // const brandsResult = await this.projectService.getBrands({ pageSize: 1000 });
    // const brands = brandsResult.brands;

    // 获取供应商数据
    const suppliersResult = await this.projectService.getSuppliers({ pageSize: 1000 });
    const suppliers = suppliersResult.suppliers;

    console.log(`📈 数据统计: 周预算${weeklyBudgets.length}条, 项目${projects.length}个, 供应商${suppliers.length}个`);

    return { weeklyBudgets, projects, suppliers };
  }

  /**
   * 创建当月周预算表
   */
  private async createMonthlyBudgetSheet(
    workbook: ExcelJS.Workbook,
    weeklyBudgets: WeeklyBudget[],
    projects: Project[],
    suppliers: Supplier[],
    params: MonthlyBudgetExportParams
  ) {
    const worksheet = workbook.addWorksheet(`${params.month}月周预算`);

    // 添加标题行
    worksheet.mergeCells('A1:O1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = `CanTV财务系统 - ${params.year}年${params.month}月周预算报表`;
    titleCell.font = {
      name: '微软雅黑',
      size: 16,
      bold: true,
      color: { argb: 'FF2E4BC6' }
    };
    titleCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFF8F9FA' }
    };
    titleCell.border = {
      top: { style: 'thin', color: { argb: 'FFD1D5DB' } },
      left: { style: 'thin', color: { argb: 'FFD1D5DB' } },
      bottom: { style: 'thin', color: { argb: 'FFD1D5DB' } },
      right: { style: 'thin', color: { argb: 'FFD1D5DB' } }
    };
    worksheet.getRow(1).height = 35;

    // 添加生成时间信息
    worksheet.mergeCells('A2:O2');
    const infoCell = worksheet.getCell('A2');
    infoCell.value = `生成时间: ${TimezoneUtils.formatToLocal(TimezoneUtils.now())} | 数据范围: ${params.year}年${params.month}月`;
    infoCell.font = {
      name: '微软雅黑',
      size: 10,
      italic: true,
      color: { argb: 'FF6B7280' }
    };
    infoCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    worksheet.getRow(2).height = 20;

    // 空行
    worksheet.addRow([]);

    // 设置列标题
    const headers = [
      '项目名称', '供应商名称', '服务内容', '合同成本', '专票税率', '不含税金额',
      '已付金额', '未付金额', `${params.month}月总应付`,
      ...Array.from({ length: 5 }, (_, i) => `${params.month}月第${i + 1}周`)
    ];

    const headerRowIndex = 4;
    worksheet.addRow(headers);

    // 设置表头样式
    const headerRow = worksheet.getRow(headerRowIndex);
    headerRow.height = 40;
    headerRow.font = {
      name: '微软雅黑',
      size: 12,
      bold: true,
      color: { argb: 'FFFFFFFF' }
    };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF4F46E5' }
    };
    headerRow.alignment = {
      horizontal: 'center',
      vertical: 'middle',
      wrapText: true
    };

    // 为表头添加边框
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.border = {
        top: { style: 'medium', color: { argb: 'FF4F46E5' } },
        left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
        bottom: { style: 'medium', color: { argb: 'FF4F46E5' } },
        right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
      };
    });

    // 按项目分组并添加数据行
    const projectBudgetMap = new Map<string, WeeklyBudget[]>();
    weeklyBudgets.forEach(budget => {
      if (!projectBudgetMap.has(budget.projectId)) {
        projectBudgetMap.set(budget.projectId, []);
      }
      projectBudgetMap.get(budget.projectId)!.push(budget);
    });

    let currentRowIndex = headerRowIndex + 1;
    for (const [projectId, budgets] of projectBudgetMap.entries()) {
      const project = projects.find(p => p.id === projectId);
      if (!project) continue;

      // 按供应商分组预算
      const supplierBudgetMap = new Map<string, WeeklyBudget[]>();
      budgets.forEach(budget => {
        const supplierId = budget.supplierId || 'no-supplier';
        if (!supplierBudgetMap.has(supplierId)) {
          supplierBudgetMap.set(supplierId, []);
        }
        supplierBudgetMap.get(supplierId)!.push(budget);
      });

      // 为每个供应商创建一行数据
      for (const [supplierId, supplierBudgets] of supplierBudgetMap.entries()) {
        const supplier = suppliers.find(s => s.id === supplierId);

        // 计算合计数据
        const totalContractCost = supplierBudgets.reduce((sum, b) => sum + b.contractAmount, 0);
        const totalPaidAmount = supplierBudgets.reduce((sum, b) => sum + b.paidAmount, 0);
        const totalUnpaidAmount = supplierBudgets.reduce((sum, b) => sum + b.remainingAmount, 0);

        // 获取主要税率
        const mainTaxRate = this.getMainTaxRate(supplierBudgets);

        // 计算不含税金额
        const taxExcludedAmount = totalContractCost / (this.getTaxExclusionRate(mainTaxRate));

        // 计算当月总应付（这里简化为未付金额）
        const monthlyTotalPayable = totalUnpaidAmount;

        // 计算每周的预算
        const weeklyAmounts = this.calculateWeeklyAmounts(supplierBudgets, params);

        const row: MonthlyBudgetRow = {
          projectName: project.projectName,
          supplierName: supplier?.name || '未知供应商',
          serviceContent: this.getServiceContent(supplierBudgets),
          contractCost: totalContractCost,
          taxRate: this.getTaxRateText(mainTaxRate),
          taxExcludedAmount: taxExcludedAmount,
          paidAmount: totalPaidAmount,
          unpaidAmount: totalUnpaidAmount,
          julyTotalPayable: monthlyTotalPayable,
          week1: weeklyAmounts[0] || 0,
          week2: weeklyAmounts[1] || 0,
          week3: weeklyAmounts[2] || 0,
          week4: weeklyAmounts[3] || 0,
          week5: weeklyAmounts[4] || 0
        };

        const dataRow = worksheet.addRow(Object.values(row));
        this.styleDataRow(dataRow, currentRowIndex, headers.length);
        currentRowIndex++;
      }
    }

    // 如果没有数据，添加提示行
    if (weeklyBudgets.length === 0) {
      const noDataRow = worksheet.addRow(Array(15).fill(''));
      noDataRow.getCell(1).value = `${params.year}年${params.month}月暂无周预算数据`;
      noDataRow.font = {
        name: '微软雅黑',
        size: 12,
        italic: true,
        color: { argb: 'FF6B7280' }
      };
      noDataRow.alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.mergeCells(`A${currentRowIndex}:O${currentRowIndex}`);
      noDataRow.height = 40;
    }

    // 设置列宽和样式
    this.setupWorksheetColumns(worksheet, headers);

    console.log('✅ 当月周预算表创建完成');
  }

  /**
   * 计算每周的金额分配
   */
  private calculateWeeklyAmounts(budgets: WeeklyBudget[], params: MonthlyBudgetExportParams): number[] {
    const weeklyAmounts: number[] = [0, 0, 0, 0, 0]; // 5周

    // 根据周开始日期来分配金额
    budgets.forEach(budget => {
      if (!budget.weekStartDate) return;

      const weekStartDate = new Date(budget.weekStartDate);
      const weekNumber = this.getWeekNumberInMonth(weekStartDate);

      if (weekNumber >= 1 && weekNumber <= 5) {
        weeklyAmounts[weekNumber - 1] = (weeklyAmounts[weekNumber - 1] || 0) + (budget.remainingAmount ?? 0); // 使用剩余金额作为该周应付
      }
    });

    return weeklyAmounts;
  }

  /**
   * 获取日期在当月的第几周 (1-5)
   */
  private getWeekNumberInMonth(date: Date): number {
    const dayOfMonth = date.getDate();

    // 简单计算：每7天为一周，从1号开始算第一周
    return Math.ceil(dayOfMonth / 7);
  }

  /**
   * 获取主要税率
   */
  private getMainTaxRate(budgets: WeeklyBudget[]): string {
    if (budgets.length === 0) return 'special_6';

    // 按合同金额排序，取最大的税率
    const taxRateAmounts = new Map<string, number>();

    budgets.forEach(budget => {
      const current = taxRateAmounts.get(budget.taxRate) || 0;
      taxRateAmounts.set(budget.taxRate, current + budget.contractAmount);
    });

    if (taxRateAmounts.size === 0) return 'special_6';

    const sortedTaxRates = Array.from(taxRateAmounts.entries())
      .sort((a, b) => b[1] - a[1]);

    return sortedTaxRates[0]?.[0] || 'special_6';
  }

  /**
   * 获取服务内容
   */
  private getServiceContent(budgets: WeeklyBudget[]): string {
    const serviceTypes = [...new Set(budgets.map(b => b.serviceContent))];
    return serviceTypes.map(type => type).join(', ');
  }

  /**
   * 获取服务类型文本
   */
  private getServiceTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      'INFLUENCER': '达人服务',
      'influencer': '达人服务',
      'ADVERTISING': '投流服务',
      'advertising': '投流服务',
      'OTHER': '其他服务',
      'other': '其他服务'
    };
    return typeMap[type] || type;
  }

  /**
   * 获取税率文本
   */
  private getTaxRateText(taxRate: string): string {
    const taxRateMap: Record<string, string> = {
      'SPECIAL_1': '专票1%',
      'special_1': '专票1%',
      'SPECIAL_3': '专票3%',
      'special_3': '专票3%',
      'SPECIAL_6': '专票6%',
      'special_6': '专票6%',
      'GENERAL': '普票',
      'general': '普票',
      'NO_TAX': '不计税',
      'no_tax': '不计税',
      'NONE': '-',
      'none': '-'
    };
    return taxRateMap[taxRate] || taxRate;
  }

  /**
   * 获取税率对应的不含税比率
   */
  private getTaxExclusionRate(taxRate: string): number {
    const taxRateMap: Record<string, number> = {
      'SPECIAL_1': 1.01,
      'special_1': 1.01,
      'SPECIAL_3': 1.03,
      'special_3': 1.03,
      'SPECIAL_6': 1.06,
      'special_6': 1.06,
      'GENERAL': 1.0,
      'general': 1.0,
      'NONE': 1.0,
      'none': 1.0,
      'NO_TAX': 1.0,
      'no_tax': 1.0
    };
    return taxRateMap[taxRate] || 1.06; // 默认6%税率
  }

  /**
   * 设置数据行样式
   */
  private styleDataRow(row: ExcelJS.Row, rowIndex: number, columnCount: number): void {
    row.height = 30;
    row.font = {
      name: '微软雅黑',
      size: 11
    };

    // 交替行颜色
    const isEvenRow = rowIndex % 2 === 0;
    const backgroundColor = isEvenRow ? 'FFFFFFFF' : 'FFF8FAFC';

    for (let i = 1; i <= columnCount; i++) {
      const cell = row.getCell(i);

      // 设置背景色
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: backgroundColor }
      };

      // 设置边框
      cell.border = {
        top: { style: 'thin', color: { argb: 'FFE5E7EB' } },
        left: { style: 'thin', color: { argb: 'FFE5E7EB' } },
        bottom: { style: 'thin', color: { argb: 'FFE5E7EB' } },
        right: { style: 'thin', color: { argb: 'FFE5E7EB' } }
      };

      // 设置对齐方式
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
        indent: 1
      };

      // 根据列类型设置特殊样式
      const cellValue = cell.value;
      if (typeof cellValue === 'number') {
        // 数字列 - 右对齐
        cell.alignment = {
          horizontal: 'right',
          vertical: 'middle',
          indent: 1
        };

        // 金额列添加千分位分隔符
        if (i >= 4 && i <= 14) { // 合同成本到第五周的列
          cell.numFmt = '#,##0.00';
          if (cellValue > 0) {
            cell.font = { ...cell.font, color: { argb: 'FF059669' } };
          }
        }
      } else if (typeof cellValue === 'string') {
        // 长文本左对齐
        if (i === 1 || i === 2 || i === 3) { // 项目名称、供应商名称、服务内容
          cell.alignment = {
            horizontal: 'left',
            vertical: 'middle',
            wrapText: true,
            indent: 1
          };
        }
      }
    }
  }

  /**
   * 设置工作表列样式
   */
  private setupWorksheetColumns(worksheet: ExcelJS.Worksheet, headers: string[]): void {
    // 列宽配置
    const columnWidths = [
      40, // 项目名称
      25, // 供应商名称
      20, // 服务内容
      18, // 合同成本
      14, // 专票税率
      18, // 不含税金额
      18, // 已付金额
      18, // 未付金额
      18, // 月总应付
      15, // 第一周
      15, // 第二周
      15, // 第三周
      15, // 第四周
      15  // 第五周
    ];

    // 设置列宽
    headers.forEach((_, index) => {
      const column = worksheet.getColumn(index + 1);
      column.width = columnWidths[index] || 15;
    });

    // 冻结窗格
    worksheet.views = [
      {
        state: 'frozen',
        xSplit: 3, // 冻结前3列
        ySplit: 4, // 冻结标题行
        topLeftCell: 'D5',
        activeCell: 'A1',
        showGridLines: true,
        showRowColHeaders: true
      }
    ];

    // 设置打印选项
    worksheet.pageSetup = {
      paperSize: 9, // A4
      orientation: 'landscape',
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
      }
    };
  }
}