/**
 * 钉钉Stream推送服务 V2
 * 基于官方SDK标准重新实现
 * 
 * 参考: https://github.com/open-dingtalk/dingtalk-stream-sdk-nodejs
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { ApprovalService } from './approval.js';
import { DingTalkService } from './dingtalk.js';

// 基于官方SDK的消息格式
export interface DingTalkStreamMessage {
  headers?: {
    messageId?: string;
    eventType?: string;
    eventId?: string;
    eventBornTime?: number;
    eventCorpId?: string;
    eventUnifiedAppId?: string;
    [key: string]: any;
  };
  data?: any;
}

// 连接配置
export interface StreamConfig {
  clientId: string;
  clientSecret: string;
  subscriptions?: Array<{
    type: string;
    topic: string;
  }>;
}

export class DingTalkStreamServiceV2 extends EventEmitter {
  private dingTalkService: DingTalkService;
  private approvalService: ApprovalService;
  private ws: WebSocket | null = null;
  private config: StreamConfig;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.dingTalkService = new DingTalkService();
    this.approvalService = new ApprovalService();
    
    this.config = {
      clientId: process.env.DINGTALK_APP_KEY || '',
      clientSecret: process.env.DINGTALK_APP_SECRET || '',
      subscriptions: [
        {
          type: 'CALLBACK',
          topic: '/v1.0/im/bot/messages/get'
        },
        {
          type: 'CALLBACK', 
          topic: '/v1.0/workflow/processInstances/events/changed'
        }
      ]
    };
  }

  /**
   * 启动Stream服务
   */
  async start(): Promise<void> {
    try {
      console.log('🚀 启动钉钉Stream服务...');

      if (!this.config.clientId || !this.config.clientSecret) {
        throw new Error('缺少钉钉应用配置信息');
      }

      console.log('🔑 配置信息:', {
        clientId: this.config.clientId.substring(0, 8) + '...',
        clientSecret: '***',
        subscriptions: this.config.subscriptions
      });

      // 获取访问令牌
      console.log('🔑 正在获取访问令牌...');
      const accessToken = await this.dingTalkService.getAccessToken();
      console.log('✅ 访问令牌获取成功');

      // 建立Stream连接
      await this.connect(accessToken);

      console.log('✅ 钉钉Stream服务启动成功');
    } catch (error) {
      console.error('❌ 启动钉钉Stream服务失败:', error);
      throw error;
    }
  }

  /**
   * 停止Stream服务
   */
  async stop(): Promise<void> {
    console.log('🛑 停止钉钉Stream服务...');
    
    this.isConnected = false;
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    console.log('✅ 钉钉Stream服务已停止');
  }

  /**
   * 建立WebSocket连接
   */
  private async connect(accessToken: string): Promise<void> {
    try {
      // 获取Stream连接端点
      const endpoint = await this.getStreamEndpoint(accessToken);
      
      console.log('🔗 建立WebSocket连接到:', endpoint);

      // 钉钉Stream的WebSocket连接不需要额外的认证头
      // 认证信息已经包含在endpoint URL中
      this.ws = new WebSocket(endpoint);

      this.setupWebSocketHandlers();
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket连接超时'));
        }, 10000);

        this.ws!.once('open', () => {
          clearTimeout(timeout);
          this.isConnected = true;
          this.reconnectAttempts = 0;
          console.log('✅ WebSocket连接已建立');
          this.startHeartbeat();
          resolve();
        });

        this.ws!.once('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });
    } catch (error) {
      console.error('❌ 建立WebSocket连接失败:', error);
      throw error;
    }
  }

  /**
   * 获取Stream连接端点
   */
  private async getStreamEndpoint(accessToken: string): Promise<string> {
    try {
      console.log('📡 正在获取Stream连接端点...');

      const requestBody = {
        clientId: this.config.clientId,
        clientSecret: this.config.clientSecret,
        ua: 'DingTalkStream/2.0.0 Node.js',
        subscriptions: [
          {
            type: 'CALLBACK',
            topic: '*'  // 订阅所有回调事件
          }
        ]
      };

      console.log('📤 请求参数:', {
        clientId: this.config.clientId.substring(0, 8) + '...',
        clientSecret: '***',
        subscriptions: requestBody.subscriptions
      });

      const response = await fetch('https://api.dingtalk.com/v1.0/gateway/connections/open', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-acs-dingtalk-access-token': accessToken
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📥 响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ 响应错误内容:', errorText);
        throw new Error(`获取Stream端点失败: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('📥 响应数据:', data);

      if (!data.endpoint) {
        throw new Error('Stream端点响应格式错误: 缺少endpoint字段');
      }

      console.log('✅ 获取Stream端点成功:', data.endpoint);
      return data.endpoint;
    } catch (error) {
      console.error('❌ 获取Stream端点失败:', error);
      throw error;
    }
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        const rawMessage = data.toString();
        console.log('📥 收到消息:', rawMessage.substring(0, 200));
        
        const message: DingTalkStreamMessage = JSON.parse(rawMessage);
        this.handleMessage(message);
      } catch (error) {
        console.error('❌ 处理消息失败:', error);
      }
    });

    this.ws.on('close', (code: number, reason: Buffer) => {
      console.log(`🔌 WebSocket连接已关闭: ${code} ${reason.toString()}`);
      this.isConnected = false;
      
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }

      // 自动重连
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      } else {
        console.error('❌ 达到最大重连次数，停止重连');
        this.emit('error', new Error('WebSocket连接失败，达到最大重连次数'));
      }
    });

    this.ws.on('error', (error: Error) => {
      console.error('❌ WebSocket错误:', error);
      this.emit('error', error);
    });

    this.ws.on('ping', () => {
      console.log('💓 收到ping');
      if (this.ws) {
        this.ws.pong();
      }
    });
  }

  /**
   * 处理收到的消息
   */
  private async handleMessage(message: DingTalkStreamMessage): Promise<void> {
    try {
      const headers = message.headers || {};
      const eventType = headers.eventType;
      const messageId = headers.messageId;

      console.log('📨 处理消息:', {
        eventType,
        messageId,
        eventId: headers.eventId
      });

      // 发送ACK确认
      if (messageId) {
        this.sendAck(messageId);
      }

      // 根据事件类型处理
      switch (eventType) {
        case 'bpms_instance_change':
          await this.handleApprovalChange(message.data);
          break;
        case 'chat_bot_message':
          await this.handleBotMessage(message.data);
          break;
        default:
          console.log(`❓ 未处理的事件类型: ${eventType}`);
      }

      // 触发事件供外部监听
      this.emit('message', {
        eventType,
        data: message.data,
        headers
      });
    } catch (error) {
      console.error('❌ 处理消息失败:', error);
    }
  }

  /**
   * 处理审批状态变更
   */
  private async handleApprovalChange(data: any): Promise<void> {
    try {
      console.log('📋 处理审批状态变更:', data);
      
      if (data && data.processInstanceId) {
        const result = await this.approvalService.handleApprovalStatusChange({
          processInstanceId: data.processInstanceId,
          result: data.result || 'unknown',
          type: data.type || 'unknown',
          staffId: data.staffId || '',
          createTime: data.createTime || Date.now(),
          finishTime: data.finishTime,
          corpId: data.corpId || ''
        });

        console.log('✅ 审批状态变更处理结果:', result);
      }
    } catch (error) {
      console.error('❌ 处理审批状态变更失败:', error);
    }
  }

  /**
   * 处理机器人消息
   */
  private async handleBotMessage(data: any): Promise<void> {
    console.log('🤖 处理机器人消息:', data);
    // TODO: 实现机器人消息处理逻辑
  }

  /**
   * 发送ACK确认
   */
  private sendAck(messageId: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        const ackMessage = JSON.stringify({
          code: 200,
          headers: {
            messageId
          },
          message: 'OK'
        });

        this.ws.send(ackMessage);
        console.log('📤 发送ACK确认:', messageId);
      } catch (error) {
        console.error('❌ 发送ACK确认失败:', error);
      }
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.ping();
        console.log('💓 发送心跳');
      }
    }, 30000); // 30秒心跳间隔
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;
    
    console.log(`🔄 ${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);
    
    setTimeout(async () => {
      try {
        const accessToken = await this.dingTalkService.getAccessToken();
        await this.connect(accessToken);
      } catch (error) {
        console.error('❌ 重连失败:', error);
      }
    }, delay);
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    reconnectAttempts: number;
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}
