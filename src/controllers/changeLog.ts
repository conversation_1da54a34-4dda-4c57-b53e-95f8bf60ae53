import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ProjectService } from '../services/project.js';

// 查询参数验证schema
const changeLogQuerySchema = z.object({
  projectId: z.string().optional(),
  operatorId: z.string().optional(),
  changeType: z.enum(['CREATE', 'UPDATE', 'DELETE', 'STATUS_CHANGE', 'APPROVAL', 'ATTACHMENT']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.enum(['createdAt', 'changeType']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

const projectChangeLogQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(50),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

export class ChangeLogController {
  private projectService: ProjectService;

  constructor() {
    this.projectService = new ProjectService();
  }

  /**
   * 获取项目变更记录列表
   * 支持多维度过滤：项目ID、操作人员、变更类型、时间范围等
   * 变更记录包含完整的操作审计信息，确保数据可追溯性
   *
   * 业务逻辑说明：
   * - CREATE: 记录项目创建时所有设置的业务字段
   * - UPDATE: 智能对比字段变更，只记录实际发生变化的字段
   * - DELETE: 记录删除操作，保留删除前的项目数据
   */
  async getChangeLogs(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = changeLogQuerySchema.parse(request.query);
      // Ensure changeType is of type ChangeType if present
      const params = {
        ...queryParams,
        changeType: queryParams.changeType as any // Cast to ChangeType if necessary
      };
      const result = await this.projectService.getProjectChangeLogs(params);

      // 记录查询操作
      const user = (request as any).user;
      console.log(`变更记录查询: 操作人=${user?.userid}, 查询条件=${JSON.stringify(queryParams)}, 结果数量=${result.changeLogs?.length || 0}`);

      return reply.send({
        success: true,
        data: result,
        message: '获取变更记录列表成功'
      });
    } catch (error) {
      console.error('获取变更记录列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取变更记录列表失败'
      });
    }
  }

  /**
   * 获取单个项目的变更记录
   * 按时间倒序显示项目的完整操作历史
   *
   * 业务价值：
   * - 项目操作审计：追踪项目从创建到删除的完整生命周期
   * - 责任追溯：明确每次变更的操作人员和时间
   * - 数据恢复：通过beforeData可以了解变更前的状态
   * - 合规要求：满足企业内控和审计要求
   */
  async getProjectChangeLogs(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { projectId } = request.params as { projectId: string };
      const queryParams = projectChangeLogQuerySchema.parse(request.query);

      const result = await this.projectService.getProjectChangeLogsByProjectId(projectId, queryParams);

      // 记录查询操作
      const user = (request as any).user;
      console.log(`项目变更记录查询: 项目=${projectId}, 操作人=${user?.userid}, 结果数量=${result.changeLogs?.length || 0}`);

      return reply.send({
        success: true,
        data: result,
        message: '获取项目变更记录成功'
      });
    } catch (error) {
      console.error('获取项目变更记录失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取项目变更记录失败'
      });
    }
  }
}
