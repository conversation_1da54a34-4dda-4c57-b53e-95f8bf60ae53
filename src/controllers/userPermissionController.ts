import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { AssignDepartmentRoleData, AssignUserRoleData, UserPermissionService } from '../services/userPermissionService.js';

// 请求验证schemas
const assignUserRolesSchema = z.object({
  roleIds: z.array(z.string()),
  expiresAt: z.string().transform(val => val ? new Date(val) : undefined).optional(),
});

const assignDepartmentRolesSchema = z.object({
  roleIds: z.array(z.string()),
  expiresAt: z.string().transform(val => val ? new Date(val) : undefined).optional(),
});

const checkPermissionsSchema = z.object({
  permissions: z.array(z.string()),
});

export class UserPermissionController {
  constructor(private userPermissionService: UserPermissionService) {}

  /**
   * 获取用户权限信息
   */
  async getUserPermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };

      const permissionInfo = await this.userPermissionService.getUserPermissionInfo(userid);

      return reply.send({
        success: true,
        data: permissionInfo,
        message: '获取用户权限信息成功',
      });
    } catch (error: any) {
      console.error('获取用户权限信息失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取用户权限信息失败',
      });
    }
  }

  /**
   * 检查用户权限
   */
  async checkUserPermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };
      const body = checkPermissionsSchema.parse(request.body);

      const permissionResults = await this.userPermissionService.hasPermissions(userid, body.permissions);

      return reply.send({
        success: true,
        data: {
          userid,
          permissions: permissionResults,
        },
        message: '权限检查完成',
      });
    } catch (error: any) {
      console.error('检查用户权限失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '检查用户权限失败',
      });
    }
  }

  /**
   * 获取用户角色
   */
  async getUserRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };

      const roles = await this.userPermissionService.getUserRoles(userid);

      return reply.send({
        success: true,
        data: roles,
        message: '获取用户角色成功',
      });
    } catch (error: any) {
      console.error('获取用户角色失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取用户角色失败',
      });
    }
  }

  /**
   * 为用户分配角色
   */
  async assignUserRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };
      const body = assignUserRolesSchema.parse(request.body);
      const user = request.user!;

      const assignData: AssignUserRoleData = {
        userid,
        roleIds: body.roleIds,
        assignedBy: user.userid,
        expiresAt: body.expiresAt,
      };

      await this.userPermissionService.assignUserRoles(assignData);

      return reply.send({
        success: true,
        message: '用户角色分配成功',
      });
    } catch (error: any) {
      console.error('分配用户角色失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '分配用户角色失败',
      });
    }
  }

  /**
   * 获取部门角色
   */
  async getDepartmentRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { deptId } = request.params as { deptId: string };
      const departmentId = parseInt(deptId);

      if (isNaN(departmentId)) {
        return reply.status(400).send({
          success: false,
          message: '无效的部门ID',
        });
      }

      const roles = await this.userPermissionService.getDepartmentRoles(departmentId);

      return reply.send({
        success: true,
        data: {
          deptId: departmentId,
          roles,
          total: roles.length,
        },
        message: '获取部门角色成功',
      });
    } catch (error: any) {
      console.error('获取部门角色失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取部门角色失败',
      });
    }
  }

  /**
   * 为部门分配角色
   */
  async assignDepartmentRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { deptId } = request.params as { deptId: string };
      const departmentId = parseInt(deptId);
      const body = assignDepartmentRolesSchema.parse(request.body);
      const user = request.user!;

      if (isNaN(departmentId)) {
        return reply.status(400).send({
          success: false,
          message: '无效的部门ID',
        });
      }

      const assignData: AssignDepartmentRoleData = {
        deptId: departmentId,
        roleIds: body.roleIds,
        assignedBy: user.userid,
        expiresAt: body.expiresAt,
      };

      await this.userPermissionService.assignDepartmentRoles(assignData);

      return reply.send({
        success: true,
        message: '部门角色分配成功',
      });
    } catch (error: any) {
      console.error('分配部门角色失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '分配部门角色失败',
      });
    }
  }

  /**
   * 移除部门角色
   */
  async removeDepartmentRole(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { deptId, roleId } = request.params as { deptId: string; roleId: string };
      const departmentId = parseInt(deptId);

      if (isNaN(departmentId)) {
        return reply.status(400).send({
          success: false,
          message: '无效的部门ID',
        });
      }

      await this.userPermissionService.removeDepartmentRole(departmentId, roleId);

      return reply.send({
        success: true,
        message: '部门角色移除成功',
      });
    } catch (error: any) {
      console.error('移除部门角色失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '移除部门角色失败',
      });
    }
  }

  /**
   * 清空部门所有角色
   */
  async clearDepartmentRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { deptId } = request.params as { deptId: string };
      const departmentId = parseInt(deptId);

      if (isNaN(departmentId)) {
        return reply.status(400).send({
          success: false,
          message: '无效的部门ID',
        });
      }

      await this.userPermissionService.clearDepartmentRoles(departmentId);

      return reply.send({
        success: true,
        message: '部门角色清空成功',
      });
    } catch (error: any) {
      console.error('清空部门角色失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '清空部门角色失败',
      });
    }
  }

  /**
   * 获取当前用户权限信息
   */
  async getCurrentUserPermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = request.user!;

      const permissionInfo = await this.userPermissionService.getUserPermissionInfo(user.userid);

      return reply.send({
        success: true,
        data: permissionInfo,
        message: '获取当前用户权限信息成功',
      });
    } catch (error: any) {
      console.error('获取当前用户权限信息失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取当前用户权限信息失败',
      });
    }
  }

  /**
   * 检查当前用户权限
   */
  async checkCurrentUserPermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = request.user!;
      const body = checkPermissionsSchema.parse(request.body);

      const permissionResults = await this.userPermissionService.hasPermissions(user.userid, body.permissions);

      return reply.send({
        success: true,
        data: {
          userid: user.userid,
          permissions: permissionResults,
        },
        message: '当前用户权限检查完成',
      });
    } catch (error: any) {
      console.error('检查当前用户权限失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '检查当前用户权限失败',
      });
    }
  }

  /**
   * 获取当前用户角色
   */
  async getCurrentUserRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = request.user!;

      const roles = await this.userPermissionService.getUserRoles(user.userid);

      return reply.send({
        success: true,
        data: roles,
        message: '获取当前用户角色成功',
      });
    } catch (error: any) {
      console.error('获取当前用户角色失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取当前用户角色失败',
      });
    }
  }

  /**
   * 查询当前用户信息
   */
  async getCurrentUserDetail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = request.user!;
  
      return reply.send({
        success: true,
        data: user,
        message: '获取当前用户信息成功',
      });
    } catch (error: any) {
      console.error('获取当前用户信息失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取当前用户信息失败',
      });
    }
  }
}
