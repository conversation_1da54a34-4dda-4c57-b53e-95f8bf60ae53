import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { DatabaseService } from '../services/database.js';
import { ProjectService } from '../services/project.js';
import { ProjectStatus } from '../types/project.js';

// 验证模式
const financialReportQuerySchema = z.object({
  brandId: z.string().optional(),
  startDate: z.string().optional().transform(str => str ? new Date(str) : undefined),
  endDate: z.string().optional().transform(str => str ? new Date(str) : undefined),
  projectStatus: z.array(z.nativeEnum(ProjectStatus)).optional(),
  includeCompleted: z.string().optional().transform(str => str === 'true').default('true'),
  includeCancelled: z.string().optional().transform(str => str === 'true').default('false')
});

export class FinancialController {
  private projectService: ProjectService;
  private databaseService: DatabaseService;

  constructor() {
    this.projectService = new ProjectService();
    this.databaseService = new DatabaseService();
  }

  /**
   * 获取品牌财务汇总报表
   */
  async getBrandFinancialSummary(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = financialReportQuerySchema.parse(request.query);
      console.log('获取品牌财务汇总报表:', queryParams);

      const result = await this.projectService.getBrandFinancialSummary(queryParams);
      console.log('获取品牌财务汇总报表成功:', result);

      return reply.send({
        success: true,
        data: result,
        message: '获取品牌财务汇总报表成功'
      });
    } catch (error) {
      console.error('获取品牌财务汇总报表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取品牌财务汇总报表失败'
      });
    }
  }

  /**
   * 获取品牌财务详细报表
   */
  async getBrandFinancialDetail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { brandId } = request.params as { brandId: string };
      const queryParams = financialReportQuerySchema.parse(request.query);
      console.log('获取品牌财务详细报表:', brandId, queryParams);

      const result = await this.projectService.getBrandFinancialDetail(brandId, queryParams);
      console.log('获取品牌财务详细报表成功:', result);

      return reply.send({
        success: true,
        data: result,
        message: '获取品牌财务详细报表成功'
      });
    } catch (error) {
      console.error('获取品牌财务详细报表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取品牌财务详细报表失败'
      });
    }
  }

  /**
   * 获取统计数据
   */
  async getStatistics(request: FastifyRequest, reply: FastifyReply) {
    try {
      console.log('获取统计数据');

      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;
      const lastYear = currentYear - 1;
      const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;
      const lastMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear;

      // 当月开始和结束日期
      const currentMonthStart = new Date(currentYear, currentMonth - 1, 1);
      const currentMonthEnd = new Date(currentYear, currentMonth, 0, 23, 59, 59, 999);

      // 上个月开始和结束日期
      const lastMonthStart = new Date(lastMonthYear, lastMonth - 1, 1);
      const lastMonthEnd = new Date(lastMonthYear, lastMonth, 0, 23, 59, 59, 999);

      // 今年开始和结束日期
      const currentYearStart = new Date(currentYear, 0, 1);
      const currentYearEnd = new Date(currentYear, 11, 31, 23, 59, 59, 999);

      // 去年开始和结束日期
      const lastYearStart = new Date(lastYear, 0, 1);
      const lastYearEnd = new Date(lastYear, 11, 31, 23, 59, 59, 999);

      // 1. 项目总数
      const totalProjects = await this.databaseService.client.project.count({
        where: {
          status: {
            not: 'CANCELLED'
          }
        }
      });

      // 2. 进行中项目数
      const activeProjects = await this.databaseService.client.project.count({
        where: {
          status: 'ACTIVE'
        }
      });

      // 2. 当月总收入（基于项目收入表的实际收入金额）
      const currentMonthRevenue = await this.databaseService.client.projectRevenue.aggregate({
        where: {
          status: 'RECEIVED',
          receivedDate: {
            gte: currentMonthStart,
            lte: currentMonthEnd
          }
        },
        _sum: {
          actualAmount: true
        }
      });

      // 上个月总收入
      const lastMonthRevenue = await this.databaseService.client.projectRevenue.aggregate({
        where: {
          status: 'RECEIVED',
          receivedDate: {
            gte: lastMonthStart,
            lte: lastMonthEnd
          }
        },
        _sum: {
          actualAmount: true
        }
      });

      // 3. 今年总收入
      const currentYearRevenue = await this.databaseService.client.projectRevenue.aggregate({
        where: {
          status: 'RECEIVED',
          receivedDate: {
            gte: currentYearStart,
            lte: currentYearEnd
          }
        },
        _sum: {
          actualAmount: true
        }
      });

      // 去年总收入
      const lastYearRevenue = await this.databaseService.client.projectRevenue.aggregate({
        where: {
          status: 'RECEIVED',
          receivedDate: {
            gte: lastYearStart,
            lte: lastYearEnd
          }
        },
        _sum: {
          actualAmount: true
        }
      });

      // 计算同比数据
      const currentMonthAmount = Number(currentMonthRevenue._sum.actualAmount || 0);
      const lastMonthAmount = Number(lastMonthRevenue._sum.actualAmount || 0);
      const currentYearAmount = Number(currentYearRevenue._sum.actualAmount || 0);
      const lastYearAmount = Number(lastYearRevenue._sum.actualAmount || 0);

      // 计算环比和同比增长率
      const monthlyGrowthRate = lastMonthAmount === 0 ?
        (currentMonthAmount > 0 ? 100 : 0) :
        ((currentMonthAmount - lastMonthAmount) / lastMonthAmount) * 100;

      const yearlyGrowthRate = lastYearAmount === 0 ?
        (currentYearAmount > 0 ? 100 : 0) :
        ((currentYearAmount - lastYearAmount) / lastYearAmount) * 100;

      const result = {
        totalProjects,
        activeProjects,
        currentMonthRevenue: {
          amount: currentMonthAmount,
          previousAmount: lastMonthAmount,
          growthRate: Math.round(monthlyGrowthRate * 100) / 100, // 保留两位小数
          period: `${currentYear}-${currentMonth.toString().padStart(2, '0')}`
        },
        currentYearRevenue: {
          amount: currentYearAmount,
          previousAmount: lastYearAmount,
          growthRate: Math.round(yearlyGrowthRate * 100) / 100, // 保留两位小数
          period: currentYear.toString()
        },
        generatedAt: new Date().toISOString()
      };

      console.log('获取统计数据成功:', result);

      return reply.send({
        success: true,
        data: result,
        message: '获取统计数据成功'
      });
    } catch (error) {
      console.error('获取统计数据失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取统计数据失败'
      });
    }
  }
}
