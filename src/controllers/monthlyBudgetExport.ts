import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { MonthlyBudgetExportParams, MonthlyBudgetExportService } from '../services/monthlyBudgetExport.js';
import dayjs from 'dayjs';

// 验证模式
const monthlyBudgetExportQuerySchema = z.object({
  year: z.string().transform(str => parseInt(str, 10)).refine(year => year >= 2020 && year <= 2030, '年份必须在2020-2030之间'),
  month: z.string().transform(str => parseInt(str, 10)).refine(month => month >= 1 && month <= 12, '月份必须在1-12之间')
});

export class MonthlyBudgetExportController {
  private monthlyBudgetExportService: MonthlyBudgetExportService;

  constructor() {
    this.monthlyBudgetExportService = new MonthlyBudgetExportService();
  }

  /**
   * 导出当月周预算报表
   */
  async exportMonthlyBudgetReport(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 验证查询参数
      const validatedQuery = monthlyBudgetExportQuerySchema.parse(request.query);

      const params: MonthlyBudgetExportParams = {
        year: validatedQuery.year,
        month: validatedQuery.month
      };

      console.log('📊 开始导出当月周预算报表:', params);

      // 生成Excel文件
      const buffer = await this.monthlyBudgetExportService.exportMonthlyBudgetReport(params);

      // 设置响应头
      const filename = `当月周预算报表_${params.year}年${params.month}月_${dayjs().format('YYYY-MM-DD')}.xlsx`;

      reply.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      reply.header('Content-Disposition', `attachment; filename=${encodeURIComponent(filename)}`);
      reply.header('Content-Length', buffer.length);

      console.log(`✅ 当月周预算报表导出成功: ${filename} (${buffer.length} bytes)`);

      return reply.send(buffer);

    } catch (error) {
      console.error('❌ 当月周预算报表导出失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        });
      }

      return reply.status(500).send({
        success: false,
        message: '导出当月周预算报表失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取当月周预算导出预览信息
   */
  async getMonthlyBudgetExportPreview(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 验证查询参数
      const validatedQuery = monthlyBudgetExportQuerySchema.parse(request.query);

      const params: MonthlyBudgetExportParams = {
        year: validatedQuery.year,
        month: validatedQuery.month
      };

      console.log('👀 获取当月周预算导出预览信息:', params);

      // 这里可以添加预览逻辑，比如统计数据量
      const preview = {
        year: params.year,
        month: params.month,
        monthName: `${params.month}月`,
        estimatedSheets: 1, // 只有一个工作表
        estimatedSize: '预计 100KB-2MB',
        estimatedTime: '预计 5-15秒',
        columns: [
          '项目名称', '供应商名称', '服务内容', '合同成本', '专票税率', '不含税金额',
          '已付金额', '未付金额', `${params.month}月总应付`, '第一周', '第二周', '第三周', '第四周', '第五周'
        ],
        description: `导出${params.year}年${params.month}月所有有周预算的项目数据，按项目和供应商分组，包含周预算分配信息。`
      };

      return reply.send({
        success: true,
        data: preview,
        message: '获取当月周预算导出预览成功'
      });

    } catch (error) {
      console.error('❌ 获取当月周预算导出预览失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取当月周预算导出预览失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}
