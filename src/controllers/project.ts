import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { DatabaseService } from '../services/database.js';
import { DingTalkService } from '../services/dingtalk.js';
import { ProjectService } from '../services/project.js';
import { UserPermissionService } from '../services/userPermissionService.js';
import {
  ContractSigningStatus,
  ContractType,
  DocumentType
} from '../types/project.js';

// 请求参数验证模式
const createProjectSchema = z.object({
  documentType: z.nativeEnum(DocumentType),
  brandId: z.string().min(1, '品牌ID不能为空'),
  projectName: z.string().min(1, '项目名称不能为空'),
  period: z.object({
    startDate: z.string().transform(str => new Date(str)),
    endDate: z.string().transform(str => new Date(str))
  }),
  budget: z.object({
    planningBudget: z.number().min(0, '规划预算不能为负数'),
    influencerBudget: z.number().min(0, '达人预算不能为负数'),
    adBudget: z.number().min(0, '投流预算不能为负数'),
    otherBudget: z.number().min(0, '其他预算不能为负数')
  }),
  cost: z.object({
    influencerCost: z.number().min(0, '达人成本不能为负数'),
    adCost: z.number().min(0, '投流成本不能为负数'),
    otherCost: z.number().min(0, '其他成本不能为负数'),
    intermediaryCost: z.number().min(0, '居间费不能为负数').default(0),
    estimatedInfluencerRebate: z.number().min(0, '预估达人返点不能为负数')
  }),
  executorPM: z.string().min(1, '执行PM不能为空'),
  contentMediaIds: z.array(z.string()).min(1, '内容媒介不能为空'),
  contractType: z.nativeEnum(ContractType),
  settlementRules: z.string().min(1, '项目结算规则不能为空'),
  kpi: z.string().min(1, 'KPI不能为空'),

  // 财务回款信息
  expectedPaymentMonth: z.string().regex(/^\d{4}-\d{2}$/, '预计回款月份格式应为YYYY-MM').optional(),
  paymentTermDays: z.number().int().min(0, '账期天数不能为负数').max(3650, '账期天数不能超过10年').optional(),
  contractSigningStatus: z.nativeEnum(ContractSigningStatus).optional(),
  attachmentIds: z.array(z.string()).optional()
});

const updateProjectSchema = z.object({
  id: z.string().min(1, '项目ID不能为空'),
  executorPM: z.string().min(1, '执行PM不能为空'),
  contentMediaIds: z.array(z.string()).min(1, '内容媒介不能为空'),
  contractSigningStatus: z.nativeEnum(ContractSigningStatus).optional(),
  budget: z.object({
    planningBudget: z.number().min(0, '规划预算不能为负数'),
    influencerBudget: z.number().min(0, '达人预算不能为负数'),
    adBudget: z.number().min(0, '投流预算不能为负数'),
    otherBudget: z.number().min(0, '其他预算不能为负数')
  }),
  cost: z.object({
    influencerCost: z.number().min(0, '达人成本不能为负数'),
    adCost: z.number().min(0, '投流成本不能为负数'),
    otherCost: z.number().min(0, '其他成本不能为负数'),
    intermediaryCost: z.number().min(0, '居间费不能为负数').default(0),
    // estimatedInfluencerRebate: z.number().min(0, '预估达人返点不能为负数')
  }),
  period: z.object({
    startDate: z.string().transform(str => new Date(str)),
    endDate: z.string().transform(str => new Date(str))
  }).optional()
});

const projectQuerySchema = z.object({
  page: z.string().optional().transform((str, ctx) => {
    if (!str) return undefined;
    const num = parseInt(str);
    if (isNaN(num) || num < 1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '页码必须是大于0的整数'
      });
      return z.NEVER;
    }
    return num;
  }),
  pageSize: z.string().optional().transform((str, ctx) => {
    if (!str) return undefined;
    const num = parseInt(str);
    if (isNaN(num) || num < 1 || num > 100) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '每页数量必须是1-100之间的整数'
      });
      return z.NEVER;
    }
    return num;
  }),
  documentType: z.nativeEnum(DocumentType).optional(),
  brandId: z.string().optional(),
  contractType: z.nativeEnum(ContractType).optional(),
  executorPM: z.string().optional(),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']).optional(),
  keyword: z.string().optional(),
  startDate: z.string().optional().transform(str => str ? new Date(str) : undefined),
  endDate: z.string().optional().transform(str => str ? new Date(str) : undefined),
  sortBy: z.enum(['createdAt', 'updatedAt', 'projectName', 'profit']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});


export class ProjectController {
  private projectService: ProjectService;
  private dingTalkService: DingTalkService;
  private userPermissionService: UserPermissionService;

  constructor() {
    this.projectService = new ProjectService();
    this.dingTalkService = new DingTalkService();
    this.userPermissionService = new UserPermissionService(new DatabaseService());
  }

  /**
   * 创建项目
   * 自动记录项目创建的变更日志，包含所有设置的业务字段
   */
  async createProject(request: FastifyRequest, reply: FastifyReply) {
    try {
      const projectData = createProjectSchema.parse(request.body);

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user';

      // 获取操作信息用于变更记录
      const operatorInfo = {
        ip: request.ip,
        userAgent: request.headers['user-agent']
      };

      // 创建项目（会自动记录CREATE类型的变更日志）
      const project = await this.projectService.createProject(projectData, createdBy, operatorInfo);

      // 记录操作日志
      console.log(`项目创建成功: ${project.id}, 操作人: ${createdBy}, IP: ${operatorInfo.ip}`);

      return reply.send({
        success: true,
        data: project,
        message: '项目创建成功，已自动记录变更日志'
      });
    } catch (error) {
      console.error('创建项目失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '创建项目失败'
      });
    }
  }

  /**
   * 获取项目列表
   * PM只能看到自己作为执行PM的项目，PMO可以看到所有项目
   */
  async getProjects(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = projectQuerySchema.parse(request.query);

      // 获取当前用户信息
      const user = (request as any).user;
      if (!user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证',
          code: 'UNAUTHORIZED'
        });
      }

      const { userid, isAdmin, isBoss } = user;

      // 管理员和老板可以查看所有项目
      if (!isAdmin && !isBoss) {
        // 检查用户角色
        const isPM = await this.userPermissionService.isPM(userid);
        const isPMO = await this.userPermissionService.isPMO(userid);

        // PM用户只能查看自己作为执行PM的项目
        if (!isPMO) {
          queryParams.executorPM = userid;
        }
        // PMO用户可以查看所有项目，无需额外过滤
        // 普通用户如果既不是PM也不是PMO，也可以查看所有项目（根据现有权限系统）
      }

      const result = await this.projectService.getProjects(queryParams);
      console.log(result.projects.map(item => item.projectName).join(','))
      return reply.send({
        success: true,
        data: result,
        message: '获取项目列表成功'
      });
    } catch (error) {
      console.error('获取项目列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取项目列表失败'
      });
    }
  }

  /**
   * 获取单个项目
   */
  async getProject(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      if (!id) {
        return reply.status(400).send({
          success: false,
          message: '项目ID不能为空'
        });
      }

      const project = await this.projectService.getProject(id);

      if (!project) {
        return reply.status(404).send({
          success: false,
          message: '项目不存在'
        });
      }

      return reply.send({
        success: true,
        data: project,
        message: '获取项目成功'
      });
    } catch (error) {
      console.error('获取项目失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取项目失败'
      });
    }
  }

  /**
   * 更新项目
   * 智能对比字段变更，只记录实际发生变化的字段到变更日志
   */
  async updateProject(request: FastifyRequest, reply: FastifyReply) {
    try {
      const projectData = updateProjectSchema.parse(request.body);

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const updatedBy = user?.userid || 'current-user';

      // 获取操作信息用于变更记录
      const operatorInfo = {
        ip: request.ip,
        userAgent: request.headers['user-agent']
      };

      // 更新项目（会自动对比字段变更并记录UPDATE类型的变更日志）
      const project = await this.projectService.updateProject(projectData, updatedBy, operatorInfo);

      // 记录操作日志
      console.log(`项目更新成功: ${project.id}, 操作人: ${updatedBy}, IP: ${operatorInfo.ip}`);

      return reply.send({
        success: true,
        data: project,
        message: '项目更新成功，已自动记录变更日志'
      });
    } catch (error) {
      console.error('更新项目失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '更新项目失败'
      });
    }
  }

  /**
   * 删除项目
   * 记录项目删除操作到变更日志，保留删除前的项目数据
   */
  async deleteProject(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      if (!id) {
        return reply.status(400).send({
          success: false,
          message: '项目ID不能为空'
        });
      }

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const deletedBy = user?.userid || 'current-user';

      // 获取操作信息用于变更记录
      const operatorInfo = {
        ip: request.ip,
        userAgent: request.headers['user-agent']
      };

      // 删除项目（会自动记录DELETE类型的变更日志）
      const success = await this.projectService.deleteProject(id, deletedBy, operatorInfo);

      if (!success) {
        return reply.status(404).send({
          success: false,
          message: '项目不存在'
        });
      }

      // 记录操作日志
      console.log(`项目删除成功: ${id}, 操作人: ${deletedBy}, IP: ${operatorInfo.ip}`);

      return reply.send({
        success: true,
        message: '项目删除成功，已记录删除日志'
      });
    } catch (error) {
      console.error('删除项目失败:', error);

      return reply.status(500).send({
        success: false,
        message: '删除项目失败'
      });
    }
  }

  /**
   * 获取项目统计信息
   */
  async getProjectStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.projectService.getProjectStats();

      return reply.send({
        success: true,
        data: stats,
        message: '获取项目统计成功'
      });
    } catch (error) {
      console.error('获取项目统计失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取项目统计失败'
      });
    }
  }

  /**
   * 项目文件上传 - 上传文件并写入数据库
   */
  async uploadProjectFile(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 获取上传的文件
      const data = await request.file();

      if (!data) {
        return reply.status(400).send({
          success: false,
          message: '没有上传文件'
        });
      }

      // 获取当前用户ID
      const user = (request as any).user;
      const uploadedBy = user?.userid || 'current-user';

      // 获取查询参数中的项目ID（可选）
      const query = request.query as any;
      const projectId = query.projectId;

      // 上传文件并写入数据库
      const result = await this.projectService.uploadProjectFile(data, uploadedBy, projectId);

      return reply.send({
        success: true,
        data: result,
        message: '文件上传成功'
      });
    } catch (error) {
      console.error('文件上传失败:', error);

      return reply.status(500).send({
        success: false,
        message: '文件上传失败'
      });
    }
  }

  /**
   * 文件上传 (兼容旧接口)
   */
  async uploadFile(request: FastifyRequest, reply: FastifyReply) {
    return this.uploadProjectFile(request, reply);
  }

  /**
   * 获取钉钉部门列表
   */
  async getDepartments(request: FastifyRequest, reply: FastifyReply) {
    try {
      const departments = await this.dingTalkService.getAllDepartments();

      return reply.send({
        success: true,
        data: departments,
        message: '获取部门列表成功'
      });
    } catch (error) {
      console.error('获取部门列表失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取部门列表失败'
      });
    }
  }

  /**
   * 获取部门用户列表
   */
  async getDepartmentUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { deptId } = request.params as { deptId: string };
      const { cursor, size } = request.query as { cursor?: string; size?: string };

      if (!deptId) {
        return reply.status(400).send({
          success: false,
          message: '部门ID不能为空'
        });
      }

      const users = await this.dingTalkService.getDepartmentUsers(
        parseInt(deptId),
        cursor ? parseInt(cursor) : undefined,
        size ? parseInt(size) : undefined
      );

      return reply.send({
        success: true,
        data: users,
        message: '获取部门用户列表成功'
      });
    } catch (error) {
      console.error('获取部门用户列表失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取部门用户列表失败'
      });
    }
  }

  /**
   * 搜索用户
   */
  async searchUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { keyword, deptId } = request.query as { keyword?: string; deptId?: string };

      if (!keyword) {
        return reply.status(400).send({
          success: false,
          message: '搜索关键字不能为空'
        });
      }

      // 如果指定了部门ID，先获取该部门的用户列表，然后过滤
      let users: any[] = [];

      if (deptId) {
        const deptUsers = await this.dingTalkService.getDepartmentUsers(parseInt(deptId));
        users = deptUsers?.list || [];
      } else {
        // 获取所有部门的用户（这里可以优化为搜索API）
        const departments = await this.dingTalkService.getAllDepartments();
        if (departments) {
          for (const dept of departments) {
            const deptUsers = await this.dingTalkService.getDepartmentUsers(dept.dept_id);
            if (deptUsers?.list) {
              users.push(...deptUsers.list);
            }
          }
        }
      }

      // 根据关键字过滤用户
      const filteredUsers = users.filter(user =>
        user.name?.includes(keyword) ||
        user.mobile?.includes(keyword) ||
        user.email?.includes(keyword) ||
        user.job_number?.includes(keyword)
      );

      return reply.send({
        success: true,
        data: filteredUsers,
        message: '搜索用户成功'
      });
    } catch (error) {
      console.error('搜索用户失败:', error);

      return reply.status(500).send({
        success: false,
        message: '搜索用户失败'
      });
    }
  }

  /**
   * 获取用户详情
   */
  async getUserDetail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };

      if (!userid) {
        return reply.status(400).send({
          success: false,
          message: '用户ID不能为空'
        });
      }

      const user = await this.dingTalkService.getUserDetail(userid);

      return reply.send({
        success: true,
        data: user,
        message: '获取用户详情成功'
      });
    } catch (error) {
      console.error('获取用户详情失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取用户详情失败'
      });
    }
  }
}
