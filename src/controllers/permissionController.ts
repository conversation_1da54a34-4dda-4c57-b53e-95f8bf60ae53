import { FastifyRequest, FastifyReply } from 'fastify';
import { PermissionService, CreatePermissionData, UpdatePermissionData } from '../services/permissionService.js';
import { z } from 'zod';

// 请求验证schemas
const createPermissionSchema = z.object({
  name: z.string().min(1).max(100),
  displayName: z.string().min(1).max(100),
  description: z.string().optional(),
  module: z.string().min(1).max(50),
  action: z.string().min(1).max(50),
  resource: z.string().max(50).optional(),
  isSystem: z.boolean().optional(),
});

const updatePermissionSchema = z.object({
  displayName: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  module: z.string().min(1).max(50).optional(),
  action: z.string().min(1).max(50).optional(),
  resource: z.string().max(50).optional(),
});

const getPermissionsQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  pageSize: z.string().transform(Number).optional(),
  module: z.string().optional(),
  action: z.string().optional(),
  isSystem: z.string().transform(val => val === 'true').optional(),
  search: z.string().optional(),
});

const batchCreatePermissionsSchema = z.object({
  permissions: z.array(createPermissionSchema),
});

export class PermissionController {
  constructor(private permissionService: PermissionService) {}

  /**
   * 创建权限
   */
  async createPermission(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = createPermissionSchema.parse(request.body);

      const permission = await this.permissionService.createPermission(body);

      return reply.send({
        success: true,
        data: permission,
        message: '权限创建成功',
      });
    } catch (error: any) {
      console.error('创建权限失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '创建权限失败',
      });
    }
  }

  /**
   * 批量创建权限
   */
  async createPermissionsBatch(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = batchCreatePermissionsSchema.parse(request.body);

      const permissions = await this.permissionService.createPermissionsBatch(body.permissions);

      return reply.send({
        success: true,
        data: {
          permissions,
          total: permissions.length,
        },
        message: '批量创建权限成功',
      });
    } catch (error: any) {
      console.error('批量创建权限失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '批量创建权限失败',
      });
    }
  }

  /**
   * 更新权限
   */
  async updatePermission(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { permissionId } = request.params as { permissionId: string };
      const body = updatePermissionSchema.parse(request.body);

      const permission = await this.permissionService.updatePermission(permissionId, body);

      return reply.send({
        success: true,
        data: permission,
        message: '权限更新成功',
      });
    } catch (error: any) {
      console.error('更新权限失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '更新权限失败',
      });
    }
  }

  /**
   * 删除权限
   */
  async deletePermission(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { permissionId } = request.params as { permissionId: string };

      await this.permissionService.deletePermission(permissionId);

      return reply.send({
        success: true,
        message: '权限删除成功',
      });
    } catch (error: any) {
      console.error('删除权限失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '删除权限失败',
      });
    }
  }

  /**
   * 获取权限详情
   */
  async getPermission(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { permissionId } = request.params as { permissionId: string };

      const permission = await this.permissionService.getPermissionById(permissionId);

      if (!permission) {
        return reply.status(404).send({
          success: false,
          message: '权限不存在',
        });
      }

      return reply.send({
        success: true,
        data: permission,
        message: '获取权限详情成功',
      });
    } catch (error: any) {
      console.error('获取权限详情失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取权限详情失败',
      });
    }
  }

  /**
   * 获取权限列表
   */
  async getPermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const query = getPermissionsQuerySchema.parse(request.query);

      const result = await this.permissionService.getPermissions(query);

      return reply.send({
        success: true,
        data: result,
        message: '获取权限列表成功',
      });
    } catch (error: any) {
      console.error('获取权限列表失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取权限列表失败',
      });
    }
  }

  /**
   * 按模块分组获取权限
   */
  async getPermissionsByModule(request: FastifyRequest, reply: FastifyReply) {
    try {
      const modules = await this.permissionService.getPermissionsByModule();

      return reply.send({
        success: true,
        data: {
          modules,
          total: modules.length,
        },
        message: '获取权限模块成功',
      });
    } catch (error: any) {
      console.error('获取权限模块失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取权限模块失败',
      });
    }
  }

  /**
   * 获取所有模块列表
   */
  async getModules(request: FastifyRequest, reply: FastifyReply) {
    try {
      const modules = await this.permissionService.getModules();

      return reply.send({
        success: true,
        data: {
          modules,
          total: modules.length,
        },
        message: '获取模块列表成功',
      });
    } catch (error: any) {
      console.error('获取模块列表失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取模块列表失败',
      });
    }
  }

  /**
   * 获取所有操作类型列表
   */
  async getActions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const actions = await this.permissionService.getActions();

      return reply.send({
        success: true,
        data: {
          actions,
          total: actions.length,
        },
        message: '获取操作类型列表成功',
      });
    } catch (error: any) {
      console.error('获取操作类型列表失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取操作类型列表失败',
      });
    }
  }

  /**
   * 检查权限名称是否可用
   */
  async checkPermissionName(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { name } = request.query as { name: string };
      const { permissionId } = request.params as { permissionId?: string };

      if (!name) {
        return reply.status(400).send({
          success: false,
          message: '权限名称不能为空',
        });
      }

      const isAvailable = await this.permissionService.isPermissionNameAvailable(name, permissionId);

      return reply.send({
        success: true,
        data: {
          name,
          available: isAvailable,
        },
        message: isAvailable ? '权限名称可用' : '权限名称已存在',
      });
    } catch (error: any) {
      console.error('检查权限名称失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '检查权限名称失败',
      });
    }
  }
}
