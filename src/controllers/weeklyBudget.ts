import { parse } from 'csv-parse/sync';
import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ApprovalService } from '../services/approval.js';
import { ProjectService } from '../services/project.js';
import { ContractEntity, PaymentMethod } from '../types/approval.js';
import {
  ServiceType,
  TaxRate,
  WeeklyBudgetStatus
} from '../types/project.js';

// 验证模式
const createWeeklyBudgetSchema = z.object({
  title: z.string().min(1, '预算标题不能为空').max(200, '预算标题不能超过200字符'),
  // weekStartDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的开始日期格式'),
  // weekEndDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的结束日期格式'),
  serviceType: z.nativeEnum(ServiceType, { errorMap: () => ({ message: '无效的服务类型' }) }),
  serviceContent: z.string().min(1, '服务内容不能为空'),
  remarks: z.string().optional(),
  contractAmount: z.number().min(0.01, '合同金额必须大于0'),
  taxRate: z.nativeEnum(TaxRate, { errorMap: () => ({ message: '无效的税率' }) }),
  supplierId: z.string().optional(),
  paidAmount: z.number().min(0, '已付金额不能为负数').optional().default(0),
}).refine((data) => {
  // 验证已付金额不能超过合同金额
  if (data.paidAmount !== undefined) {
    return data.paidAmount <= data.contractAmount;
  }
  return true;
}, {
  message: '已付金额不能超过合同金额',
  path: ['paidAmount']
});

const updateWeeklyBudgetSchema = z.object({
  id: z.string().min(1, '预算ID不能为空'),
  title: z.string().min(1).max(200).optional(),
  // weekStartDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  // weekEndDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  serviceType: z.nativeEnum(ServiceType).optional(),
  serviceContent: z.string().min(1).optional(),
  remarks: z.string().optional(),
  contractAmount: z.number().min(0, '合同金额不能为负数').optional(),
  taxRate: z.nativeEnum(TaxRate).optional(),
  status: z.nativeEnum(WeeklyBudgetStatus).optional(),
  paidAmount: z.number().min(0, '已付金额不能为负数').optional(),
  supplierId: z.string().optional(),
}).refine((data) => {
  // 如果同时提供了合同金额和已付金额，验证已付金额不能超过合同金额
  if (data.contractAmount !== undefined && data.paidAmount !== undefined) {
    return data.paidAmount <= data.contractAmount;
  }
  return true;
}, {
  message: '已付金额不能超过合同金额',
  path: ['paidAmount']
});

const weeklyBudgetQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  pageSize: z.string().transform(Number).optional(),
  projectId: z.string().optional(),
  supplierId: z.string().optional(),
  serviceType: z.nativeEnum(ServiceType).optional(),
  status: z.nativeEnum(WeeklyBudgetStatus).optional(),
  // year: z.string().transform(Number).optional(),
  // weekNumber: z.string().transform(Number).optional(),
  // startDate: z.string().optional(),
  // endDate: z.string().optional(),
  sortBy: z.enum(['createdAt', 'contractAmount']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

const batchCreateWeeklyBudgetSchema = z.object({
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的开始日期格式'),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的结束日期格式'),
  serviceType: z.nativeEnum(ServiceType, { errorMap: () => ({ message: '无效的服务类型' }) }),
  defaultContractAmount: z.number().min(0.01, '默认合同金额必须大于0'),
  defaultTaxRate: z.nativeEnum(TaxRate, { errorMap: () => ({ message: '无效的税率' }) }),
}).refine((data) => {
  // 验证结束日期不能早于开始日期
  const startDate = new Date(data.startDate);
  const endDate = new Date(data.endDate);
  return endDate >= startDate;
}, {
  message: '结束日期不能早于开始日期',
  path: ['endDate']
});

export class WeeklyBudgetController {
  private projectService: ProjectService;
  private approvalService: ApprovalService;

  constructor() {
    this.projectService = new ProjectService();
    this.approvalService = new ApprovalService();
  }

  /**
   * 创建周预算
   */
  async createWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { projectId } = request.params as { projectId: string };
      const budgetData = createWeeklyBudgetSchema.parse(request.body);

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user';

      const budget = await this.projectService.createWeeklyBudget(projectId, budgetData, createdBy);

      return reply.send({
        success: true,
        data: budget,
        message: '创建周预算成功'
      });
    } catch (error) {
      console.error('创建周预算失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '创建周预算失败'
      });
    }
  }

  /**
   * 获取周预算列表
   */
  async getWeeklyBudgets(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = weeklyBudgetQuerySchema.parse(request.query);
      const result = await this.projectService.getWeeklyBudgets(queryParams);

      return reply.send({
        success: true,
        data: result,
        message: '获取周预算列表成功'
      });
    } catch (error) {
      console.error('获取周预算列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取周预算列表失败'
      });
    }
  }

  /**
   * 获取单个周预算
   */
  async getWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const budget = await this.projectService.getWeeklyBudget(id);

      if (!budget) {
        return reply.status(404).send({
          success: false,
          message: '周预算不存在'
        });
      }

      return reply.send({
        success: true,
        data: budget,
        message: '获取周预算成功'
      });
    } catch (error) {
      console.error('获取周预算失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取周预算失败'
      });
    }
  }

  /**
   * 更新周预算
   */
  async updateWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const body = { ...(typeof request.body === 'object' && request.body !== null ? request.body : {}), id };
      const budgetData = updateWeeklyBudgetSchema.parse(body);

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const updatedBy = user?.userid || 'current-user';

      // 获取当前周预算信息进行额外验证
      const currentBudget = await this.projectService.getWeeklyBudget(id);
      if (!currentBudget) {
        return reply.status(404).send({
          success: false,
          message: '周预算不存在'
        });
      }

      // 如果更新已付金额，需要验证不能超过合同金额
      if (budgetData.paidAmount !== undefined) {
        const contractAmount = budgetData.contractAmount ?? currentBudget.contractAmount;
        if (budgetData.paidAmount > contractAmount) {
          return reply.status(400).send({
            success: false,
            message: `已付金额(${budgetData.paidAmount})不能超过合同金额(${contractAmount})`
          });
        }
      }

      // 如果更新合同金额，需要验证不能小于已付金额
      if (budgetData.contractAmount !== undefined) {
        const paidAmount = budgetData.paidAmount ?? currentBudget.paidAmount;
        if (budgetData.contractAmount < paidAmount) {
          return reply.status(400).send({
            success: false,
            message: `合同金额(${budgetData.contractAmount})不能小于已付金额(${paidAmount})`
          });
        }
      }

      const budget = await this.projectService.updateWeeklyBudget(budgetData, updatedBy);

      return reply.send({
        success: true,
        data: budget,
        message: '更新周预算成功'
      });
    } catch (error) {
      console.error('更新周预算失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '更新周预算失败'
      });
    }
  }

  /**
   * 删除周预算
   */
  async deleteWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      await this.projectService.deleteWeeklyBudget(id);

      return reply.send({
        success: true,
        message: '删除周预算成功'
      });
    } catch (error) {
      console.error('删除周预算失败:', error);
      return reply.status(500).send({
        success: false,
        message: '删除周预算失败'
      });
    }
  }

  /**
   * 获取周预算统计
   */
  async getWeeklyBudgetStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.projectService.getWeeklyBudgetStats();

      return reply.send({
        success: true,
        data: stats,
        message: '获取周预算统计成功'
      });
    } catch (error) {
      console.error('获取周预算统计失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取周预算统计失败'
      });
    }
  }

  /**
   * 批量创建项目周预算
   */
  async batchCreateWeeklyBudgets(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { projectId } = request.params as { projectId: string };

      // 验证项目ID
      if (!projectId || projectId.trim() === '') {
        return reply.status(400).send({
          success: false,
          message: '项目ID不能为空'
        });
      }

      // 验证请求体数据
      const batchData = batchCreateWeeklyBudgetSchema.parse(request.body);

      // 验证项目是否存在
      const project = await this.projectService.getProject(projectId);
      if (!project) {
        return reply.status(404).send({
          success: false,
          message: '项目不存在'
        });
      }

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user';

      const budgets = await this.projectService.batchCreateWeeklyBudgets(
        projectId,
        batchData.startDate,
        batchData.endDate,
        batchData.serviceType,
        batchData.defaultContractAmount,
        batchData.defaultTaxRate,
        createdBy
      );

      return reply.send({
        success: true,
        data: budgets,
        message: '批量创建周预算成功'
      });
    } catch (error) {
      console.error('批量创建周预算失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '批量创建周预算失败'
      });
    }
  }

  /**
   * 批量上传周预算（CSV文件）
   */
  async bulkUploadWeeklyBudgets(request: FastifyRequest, reply: FastifyReply) {
    try {
      const data = await request.file();
      if (!data) {
        return reply.status(400).send({
          success: false,
          message: '没有上传文件'
        });
      }

      // 验证文件类型
      const allowedMimeTypes = ['text/csv', 'application/csv', 'text/plain'];
      const allowedExtensions = ['.csv'];
      const fileExtension = data.filename?.toLowerCase().slice(data.filename.lastIndexOf('.')) || '';

      if (!allowedMimeTypes.includes(data.mimetype) && !allowedExtensions.includes(fileExtension)) {
        return reply.status(400).send({
          success: false,
          message: '文件格式不正确，请上传CSV文件'
        });
      }

      // 验证文件大小（限制为10MB）
      const maxFileSize = 10 * 1024 * 1024; // 10MB
      if (data.file.bytesRead > maxFileSize) {
        return reply.status(400).send({
          success: false,
          message: '文件大小不能超过10MB'
        });
      }

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user';

      // 读取CSV文件内容
      const chunks: Buffer[] = [];
      for await (const chunk of data.file) {
        chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
      }
      const fileContent = Buffer.concat(chunks).toString('utf-8');

      // 验证文件内容不为空
      if (!fileContent.trim()) {
        return reply.status(400).send({
          success: false,
          message: 'CSV文件内容为空'
        });
      }

      // 用csv-parse同步解析CSV文件
      const csvData = parse(fileContent, {
        delimiter: ',',
        ltrim: true,
        rtrim: true,
        skip_empty_lines: false
      });

      // 验证CSV数据
      if (!csvData || csvData.length === 0) {
        return reply.status(400).send({
          success: false,
          message: 'CSV文件解析失败或无有效数据'
        });
      }

      // 验证CSV表头（至少需要包含必要的列）
      const requiredColumns = ['项目名称', '供应商名称', '服务类型', '服务内容', '合同金额', '税率'];
      const headers = csvData[0] || [];
      const missingColumns = requiredColumns.filter(col => !headers.includes(col));

      if (missingColumns.length > 0) {
        return reply.status(400).send({
          success: false,
          message: `CSV文件缺少必要的列: ${missingColumns.join(', ')}`
        });
      }

      console.log('csvData: ', csvData);
      // 调用服务层处理批量上传

      const result = await this.projectService.bulkUploadWeeklyBudgets(csvData, createdBy);

      return reply.send({
        success: true,
        data: result,
        message: '批量上传周预算成功'
      });
    } catch (error) {
      console.error('批量上传周预算失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '批量上传周预算失败'
      });
    }
  }

  /**
   * 发起周预算对公付款审批
   */
  async createPaymentApproval(request: FastifyRequest, reply: FastifyReply) {
    try {
      const createApprovalSchema = z.object({
        weeklyBudgetId: z.string().min(1, '周预算ID不能为空'),
        totalAmount: z.number().min(0.01, '付款总额必须大于0'),
        paymentReason: z.string().min(1, '付款事由不能为空'),
        department: z.number().min(1, '申请部门不能为空'),
        contractEntity: z.nativeEnum(ContractEntity).default(ContractEntity.COMPANY_A),
        expectedPaymentDate: z.string().min(1, '期望付款时间不能为空'),
        paymentMethod: z.nativeEnum(PaymentMethod).default(PaymentMethod.BANK_TRANSFER),
        receivingAccount: z.object({
          accountName: z.string().min(1, '账户名称不能为空'),
          accountNumber: z.string().min(1, '账号不能为空'),
          bankName: z.string().min(1, '开户银行不能为空'),
          bankCode: z.string().optional()
        }),
        relatedApprovalId: z.string().optional(),
        invoiceFiles: z.array(z.object({
          fileId: z.string().optional(),
          fileName: z.string().optional(),
          fileSize: z.string().optional(),
          fileType: z.string().optional(),
          name: z.string().optional(),
          url: z.string().optional()
        })).optional(),
        attachments: z.array(z.object({
          fileId: z.string().optional(),
          fileName: z.string().optional(),
          fileSize: z.string().optional(),
          fileType: z.string().optional(),
          name: z.string().optional(),
          url: z.string().optional()
        })).optional(),
        remark: z.string().optional(),

        // 兼容旧版本字段
        approvalAmount: z.number().optional(),
        reason: z.string().optional()
      });

      const approvalData = createApprovalSchema.parse(request.body);

      // 从JWT认证中间件获取当前用户信息
      const user = (request as any).user;
      if (!user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证',
          code: 'USER_NOT_AUTHENTICATED'
        });
      }

      // 验证周预算是否已经创建过审批
      const weeklyBudget = await this.projectService.getWeeklyBudget(approvalData.weeklyBudgetId);
      if (!weeklyBudget) {
        return reply.status(404).send({
          success: false,
          message: '周预算不存在',
          code: 'WEEKLY_BUDGET_NOT_FOUND'
        });
      }

      // 检查是否已经有审批在进行中或已完成
      if (weeklyBudget.approvalStatus.toLocaleUpperCase() !== 'NONE') {
        const statusMessages: Record<string, string> = {
          'PENDING': '该周预算已有审批在进行中，不能重复创建',
          'APPROVED': '该周预算的审批已通过，不能重复创建',
          'REJECTED': '该周预算的审批已被拒绝，如需重新申请请联系管理员',
          'CANCELLED': '该周预算的审批已取消，如需重新申请请联系管理员'
        };

        return reply.status(400).send({
          success: false,
          message: statusMessages[weeklyBudget.approvalStatus.toLocaleUpperCase()] || '该周预算已有审批记录，不能重复创建',
          code: 'APPROVAL_ALREADY_EXISTS',
          data: {
            currentApprovalStatus: weeklyBudget.approvalStatus
          }
        });
      }

      // 验证付款金额不能超过剩余未付金额
      const remainingAmount = weeklyBudget.contractAmount - weeklyBudget.paidAmount;
      if (approvalData.totalAmount > remainingAmount) {
        return reply.status(400).send({
          success: false,
          message: `付款金额(${approvalData.totalAmount})不能超过剩余未付金额(${remainingAmount})`,
          code: 'PAYMENT_AMOUNT_EXCEEDS_REMAINING',
          data: {
            totalAmount: approvalData.totalAmount,
            contractAmount: weeklyBudget.contractAmount,
            paidAmount: weeklyBudget.paidAmount,
            remainingAmount: remainingAmount
          }
        });
      }

      // 验证付款金额不能超过合同总金额
      if (approvalData.totalAmount > weeklyBudget.contractAmount) {
        return reply.status(400).send({
          success: false,
          message: `付款金额(${approvalData.totalAmount})不能超过合同总金额(${weeklyBudget.contractAmount})`,
          code: 'PAYMENT_AMOUNT_EXCEEDS_CONTRACT',
          data: {
            totalAmount: approvalData.totalAmount,
            contractAmount: weeklyBudget.contractAmount
          }
        });
      }

      // 验证期望付款日期格式
      const expectedPaymentDate = new Date(approvalData.expectedPaymentDate);
      if (isNaN(expectedPaymentDate.getTime())) {
        return reply.status(400).send({
          success: false,
          message: '期望付款时间格式不正确',
          code: 'INVALID_PAYMENT_DATE'
        });
      }

      // 验证期望付款日期不能是过去的日期
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      expectedPaymentDate.setHours(0, 0, 0, 0);

      if (expectedPaymentDate < today) {
        return reply.status(400).send({
          success: false,
          message: '期望付款时间不能早于今天',
          code: 'PAYMENT_DATE_IN_PAST'
        });
      }

      const originatorUserId = user.userid;

      const approval = await this.approvalService.createPaymentApproval(
        approvalData,
        originatorUserId
      );

      return reply.send({
        success: true,
        data: approval,
        message: '发起审批成功'
      });
    } catch (error) {
      console.error('发起审批失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '发起审批失败'
      });
    }
  }
}
