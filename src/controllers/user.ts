import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { DatabaseService } from '../services/database.js';
import { DingTalkService } from '../services/dingtalk.js';
import { RoleService } from '../services/roleService.js';
import { UserSyncService } from '../services/userSync.js';

// 请求参数验证 schemas
const syncUsersSchema = z.object({
  userIds: z.array(z.string()).min(1, '用户ID列表不能为空'),
  force: z.boolean().optional().default(false), // 是否强制同步
});

const getUsersSchema = z.object({
  userIds: z.array(z.string()).min(1, '用户ID列表不能为空'),
});

const syncAllUsersSchema = z.object({
  force: z.boolean().optional().default(false),
});

const assignUserRolesSchema = z.object({
  roleIds: z.array(z.string()).min(1, '角色ID列表不能为空'),
  expiresAt: z.string().transform(val => val ? new Date(val) : undefined).optional(),
});

export class UserController {
  private userSyncService: UserSyncService;
  private roleService: RoleService;
  private databaseService: DatabaseService;

  constructor() {
    this.databaseService = new DatabaseService();
    const dingTalkService = new DingTalkService();
    this.userSyncService = new UserSyncService(this.databaseService, dingTalkService);
    this.roleService = new RoleService(this.databaseService);
  }

  /**
   * 同步指定用户信息
   */
  async syncUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userIds, force } = syncUsersSchema.parse(request.body);

      console.log(`开始同步 ${userIds.length} 个用户信息...`);

      let results;
      if (force) {
        // 强制同步：先标记为需要同步，然后同步
        await this.databaseService.deactivateUsers(userIds);
        results = await this.userSyncService.syncUsersByIds(userIds);
      } else {
        // 正常同步：只同步过期的用户
        results = await this.userSyncService.syncUsersByIds(userIds);
      }

      return reply.send({
        success: true,
        data: {
          syncedUsers: results.success,
          failedUsers: results.failed,
          errors: results.errors,
          totalUsers: userIds.length
        },
        message: `用户同步完成: 成功 ${results.success}, 失败 ${results.failed}`
      });

    } catch (error) {
      console.error('同步用户失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '同步用户失败'
      });
    }
  }

  /**
   * 同步所有用户信息
   */
  async syncAllUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { force } = syncAllUsersSchema.parse(request.body || {});

      console.log('开始同步所有用户信息...');

      const results = await this.userSyncService.syncAllUsers();

      return reply.send({
        success: true,
        data: {
          syncedUsers: results.success,
          failedUsers: results.failed,
          errors: results.errors
        },
        message: `全量用户同步完成: 成功 ${results.success}, 失败 ${results.failed}`
      });

    } catch (error) {
      console.error('同步所有用户失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '同步所有用户失败'
      });
    }
  }

  /**
   * 批量获取用户信息
   */
  async getUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userIds } = getUsersSchema.parse(request.body);

      const users = await this.userSyncService.getUsersInfo(userIds);

      return reply.send({
        success: true,
        data: {
          users,
          total: users.length
        },
        message: '获取用户信息成功'
      });

    } catch (error) {
      console.error('获取用户信息失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取用户信息失败'
      });
    }
  }

  /**
   * 获取单个用户信息
   */
  async getUser(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };

      if (!userid) {
        return reply.status(400).send({
          success: false,
          message: '用户ID不能为空'
        });
      }

      const user = await this.userSyncService.getUserInfo(userid);

      if (!user) {
        return reply.status(404).send({
          success: false,
          message: '用户不存在'
        });
      }

      return reply.send({
        success: true,
        data: user,
        message: '获取用户信息成功'
      });

    } catch (error) {
      console.error('获取用户信息失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取用户信息失败'
      });
    }
  }

  /**
   * 获取本地用户列表（分页）
   */
  async getLocalUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const querySchema = z.object({
        page: z.string().optional().transform(val => val ? parseInt(val) : 1),
        pageSize: z.string().optional().transform(val => val ? parseInt(val) : 20),
        keyword: z.string().optional(),
        status: z.string().optional().transform(val => val === 'active' ? true : val === 'inactive' ? false : undefined),
        // 角色
        roleIds: z.string().optional().transform(val => val ? val.split(',') : undefined),
        departmentIds: z.string().optional().transform(val => val ? val.split(',') : undefined),
      });

      const { page, pageSize, keyword, status, roleIds, departmentIds } = querySchema.parse(request.query);

      // 构建查询条件
      const where: any = {};

      // 默认只查询活跃用户，除非明确指定要查询非活跃用户
      if (status !== undefined) {
        where.active = status;
      } else {
        where.active = true; // 默认只查询活跃用户
      }

      if (roleIds) {
        where.userRoles = {
          some: {
            roleId: {
              in: roleIds
            }
          }
        };
      }

      if (departmentIds) {
        where.deptIdList = {
          hasSome: departmentIds.map(id => parseInt(id))
        };
      }

      if (keyword) {
        where.OR = [
          { name: { contains: keyword, mode: 'insensitive' } },
          { userid: { contains: keyword, mode: 'insensitive' } },
          { mobile: { contains: keyword, mode: 'insensitive' } },
        ];
      }

      const skip = (page - 1) * pageSize;

      // 使用 DatabaseService 的 prisma 实例，包含角色信息的联表查询
      const [users, total] = await Promise.all([
        (this.databaseService as any).prisma.user.findMany({
          skip,
          take: pageSize,
          where,
          include: {
            userRoles: {
              include: {
                role: {
                  select: {
                    id: true,
                    name: true,
                    displayName: true,
                    description: true,
                    isSystem: true,
                    isActive: true
                  }
                }
              },
              where: {
                OR: [
                  { expiresAt: null },
                  { expiresAt: { gt: new Date() } }
                ]
              }
            }
          },
          orderBy: { lastSyncAt: 'desc' }
        }),
        (this.databaseService as any).prisma.user.count({ where })
      ]);

      const mappedUsers = users.map((user: any) => ({
        userid: user.userid,
        unionid: user.unionid,
        name: user.name,
        avatar: user.avatar,
        stateCode: user.stateCode,
        managerUserid: user.managerUserid,
        mobile: user.mobile,
        hideMobile: user.hideMobile,
        telephone: user.telephone,
        jobNumber: user.jobNumber,
        title: user.title,
        email: user.email,
        workPlace: user.workPlace,
        remark: user.remark,
        loginId: user.loginId,
        exclusiveAccountType: user.exclusiveAccountType,
        exclusiveAccount: user.exclusiveAccount,
        deptIdList: user.deptIdList,
        extension: user.extension,
        hiredDate: user.hiredDate,
        active: user.active,
        realAuthed: user.realAuthed,
        orgEmail: user.orgEmail,
        orgEmailType: user.orgEmailType,
        senior: user.senior,
        admin: user.admin,
        boss: user.boss,
        isActive: user.isActive,
        lastSyncAt: user.lastSyncAt,
        // 添加角色信息
        roles: user.userRoles?.map((userRole: any) => ({
          id: userRole.role.id,
          name: userRole.role.name,
          displayName: userRole.role.displayName,
          description: userRole.role.description,
          isSystem: userRole.role.isSystem,
          isActive: userRole.role.isActive,
          assignedAt: userRole.createdAt,
          expiresAt: userRole.expiresAt
        })) || []
      }));

      const responseData = {
        success: true,
        data: {
          users: mappedUsers,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        },
        message: '获取本地用户列表成功'
      };

      return reply.send(responseData);

    } catch (error) {
      console.error('获取本地用户列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取本地用户列表失败'
      });
    }
  }

  /**
   * 获取用户同步状态统计
   */
  async getUserSyncStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 使用 DatabaseService 的 prisma 实例
      const prisma = (this.databaseService as any).prisma;

      const [
        totalUsers,
        activeUsers,
        inactiveUsers,
        recentSyncUsers,
        outdatedUsers
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { isActive: true } }),
        prisma.user.count({ where: { isActive: false } }),
        prisma.user.count({
          where: {
            lastSyncAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
            }
          }
        }),
        prisma.user.count({
          where: {
            lastSyncAt: {
              lt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 超过24小时
            }
          }
        })
      ]);

      return reply.send({
        success: true,
        data: {
          totalUsers,
          activeUsers,
          inactiveUsers,
          recentSyncUsers,
          outdatedUsers,
          syncRate: totalUsers > 0 ? Math.round((recentSyncUsers / totalUsers) * 100) : 0
        },
        message: '获取用户同步统计成功'
      });

    } catch (error) {
      console.error('获取用户同步统计失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取用户同步统计失败'
      });
    }
  }

  /**
   * 批量获取本地用户
   */
  async getLocalUsersBatch(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userIds } = getUsersSchema.parse(request.body);

      const users = await this.databaseService.getUsers(userIds);

      return reply.send({
        success: true,
        data: {
          users,
          total: users.length
        },
        message: '获取本地用户成功'
      });

    } catch (error) {
      console.error('获取本地用户失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取本地用户失败'
      });
    }
  }

  /**
   * 批量根据用户id获取用户名
   */
  async getUserNames(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userIds } = getUsersSchema.parse(request.body);

      const users = await this.databaseService.getUsers(userIds);

      return reply.send({
        success: true,
        data: {
          users: users.map((user: any) => ({
            userid: user.userid,
            name: user.name,
            roles: user.userRoles?.map((userRole: any) => ({
              id: userRole.role.id,
              name: userRole.role.name,
              displayName: userRole.role.displayName,
              description: userRole.role.description,
              isSystem: userRole.role.isSystem,
              isActive: userRole.role.isActive,
              assignedAt: userRole.createdAt,
              expiresAt: userRole.expiresAt
            })) || []
          })),
          total: users.length
        },
        message: '获取用户名成功'
      });

    } catch (error) {
      console.error('获取用户名失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取用户名失败'
      });
    }
  }

  /**
   * 根据用户id获取用户名
   */
  async getUserName(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };

      if (!userid) {
        return reply.status(400).send({
          success: false,
          message: '用户ID不能为空'
        });
      }

      const user = await this.databaseService.getUser(userid);

      if (!user) {
        return reply.status(404).send({
          success: false,
          message: '用户不存在'
        });
      }

      return reply.send({
        success: true,
        data: {
          userid: user.userid,
          name: user.name,
          roles: user.userRoles?.map((userRole: any) => ({
            id: userRole.role.id,
            name: userRole.role.name,
            displayName: userRole.role.displayName,
            description: userRole.role.description,
            isSystem: userRole.role.isSystem,
            isActive: userRole.role.isActive,
            assignedAt: userRole.createdAt,
            expiresAt: userRole.expiresAt
          })) || []
        },
        message: '获取用户名成功'
      });

    } catch (error) {
      console.error('获取用户名失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取用户名失败'
      });
    }
  }

  /**
   * 给用户分配角色
   */
  async assignUserRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userid } = request.params as { userid: string };
      const { roleIds } = assignUserRolesSchema.parse(request.body);

      if (!userid) {
        return reply.status(400).send({
          success: false,
          message: '用户ID不能为空'
        });
      }

      if (!roleIds || roleIds.length === 0) {
        return reply.status(400).send({
          success: false,
          message: '角色ID列表不能为空'
        });
      }

      await this.roleService.assignUserRoles({
        userid,
        roleIds,
        assignedBy: request.user!.userid
      });

      return reply.send({
        success: true,
        message: '给用户分配角色成功'
      });

    }
    catch (error) {
      console.error('给用户分配角色失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '给用户分配角色失败'
      });
    }
  }
}
