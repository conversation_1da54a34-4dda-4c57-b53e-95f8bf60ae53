import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ProjectService } from '../services/project.js';
import {
  BudgetQueryParams,
  BudgetStatus,
  CreateBudgetRequest,
  ServiceType,
  TaxRate,
  UpdateBudgetRequest
} from '../types/project.js';

// 验证模式
const createBudgetSchema = z.object({
  title: z.string().min(1, '预算标题不能为空').max(200, '预算标题不能超过200字符'),
  serviceType: z.nativeEnum(ServiceType, { errorMap: () => ({ message: '无效的服务类型' }) }),
  serviceContent: z.string().min(1, '服务内容不能为空'),
  remarks: z.string().optional(),
  contractAmount: z.number().min(0.01, '合同金额必须大于0'),
  taxRate: z.nativeEnum(TaxRate, { errorMap: () => ({ message: '无效的税率' }) }),
  supplierId: z.string().optional(),
});

const updateBudgetSchema = z.object({
  id: z.string().min(1, '预算ID不能为空'),
  title: z.string().min(1).max(200).optional(),
  serviceType: z.nativeEnum(ServiceType).optional(),
  serviceContent: z.string().min(1).optional(),
  remarks: z.string().optional(),
  contractAmount: z.number().min(0, '合同金额不能为负数').optional(),
  taxRate: z.nativeEnum(TaxRate).optional(),
  status: z.nativeEnum(BudgetStatus).optional(),
  totalPaidAmount: z.number().min(0, '总已付金额不能为负数').optional(),
  supplierId: z.string().optional(),
}).refine((data) => {
  // 如果同时提供了合同金额和总已付金额，验证总已付金额不能超过合同金额
  if (data.contractAmount !== undefined && data.totalPaidAmount !== undefined) {
    return data.totalPaidAmount <= data.contractAmount;
  }
  return true;
}, {
  message: '总已付金额不能超过合同金额',
  path: ['totalPaidAmount']
});

const budgetQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  pageSize: z.coerce.number().min(1).max(100).optional().default(20),
  projectId: z.string().optional(),
  supplierId: z.string().optional(),
  serviceType: z.nativeEnum(ServiceType).optional(),
  status: z.nativeEnum(BudgetStatus).optional(),
  sortBy: z.enum(['title', 'contractAmount', 'createdAt']).optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

export class BudgetController {
  private projectService: ProjectService;

  constructor() {
    this.projectService = new ProjectService();
  }

  // 创建预算
  async createBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { projectId } = request.params as { projectId: string };
      const body = request.body;
      const userId = (request as any).user?.userid;

      if (!userId) {
        return reply.status(401).send({ error: '用户未认证' });
      }

      // 验证请求数据
      const validatedData = createBudgetSchema.parse(body);

      // 创建预算
      const budget = await this.projectService.createBudget(
        projectId,
        validatedData as CreateBudgetRequest,
        userId
      );

      reply.send({
        success: true,
        data: budget,
        message: '预算创建成功'
      });
    } catch (error: any) {
      console.error('创建预算失败:', error);
      
      if (error.name === 'ZodError') {
        return reply.status(400).send({
          error: '请求参数验证失败',
          details: error.errors
        });
      }

      reply.status(500).send({
        error: '创建预算失败',
        message: error.message
      });
    }
  }

  // 获取预算列表
  async getBudgets(request: FastifyRequest, reply: FastifyReply) {
    try {
      const query = request.query;
      
      // 验证查询参数
      const validatedQuery = budgetQuerySchema.parse(query);

      // 获取预算列表
      const result = await this.projectService.getBudgets(validatedQuery as BudgetQueryParams);

      reply.send({
        success: true,
        data: result,
        message: '获取预算列表成功'
      });
    } catch (error: any) {
      console.error('获取预算列表失败:', error);
      
      if (error.name === 'ZodError') {
        return reply.status(400).send({
          error: '查询参数验证失败',
          details: error.errors
        });
      }

      reply.status(500).send({
        error: '获取预算列表失败',
        message: error.message
      });
    }
  }

  // 获取单个预算
  async getBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      const budget = await this.projectService.getBudget(id);

      if (!budget) {
        return reply.status(404).send({
          error: '预算不存在'
        });
      }

      reply.send({
        success: true,
        data: budget,
        message: '获取预算详情成功'
      });
    } catch (error: any) {
      console.error('获取预算详情失败:', error);
      reply.status(500).send({
        error: '获取预算详情失败',
        message: error.message
      });
    }
  }

  // 更新预算
  async updateBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const body = request.body;
      const userId = (request as any).user?.userid;

      if (!userId) {
        return reply.status(401).send({ error: '用户未认证' });
      }

      // 验证请求数据
      const validatedData = updateBudgetSchema.parse({ id, ...body });

      // 更新预算
      const budget = await this.projectService.updateBudget(
        validatedData as UpdateBudgetRequest,
        userId
      );

      reply.send({
        success: true,
        data: budget,
        message: '预算更新成功'
      });
    } catch (error: any) {
      console.error('更新预算失败:', error);
      
      if (error.name === 'ZodError') {
        return reply.status(400).send({
          error: '请求参数验证失败',
          details: error.errors
        });
      }

      reply.status(500).send({
        error: '更新预算失败',
        message: error.message
      });
    }
  }

  // 删除预算
  async deleteBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };

      await this.projectService.deleteBudget(id);

      reply.send({
        success: true,
        message: '预算删除成功'
      });
    } catch (error: any) {
      console.error('删除预算失败:', error);
      reply.status(500).send({
        error: '删除预算失败',
        message: error.message
      });
    }
  }

  // 获取预算统计
  async getBudgetStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.projectService.getBudgetStats();

      reply.send({
        success: true,
        data: stats,
        message: '获取预算统计成功'
      });
    } catch (error: any) {
      console.error('获取预算统计失败:', error);
      reply.status(500).send({
        error: '获取预算统计失败',
        message: error.message
      });
    }
  }
}
