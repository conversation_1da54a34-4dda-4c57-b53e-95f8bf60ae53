import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ProjectService } from '../services/project.js';
import {
  RevenueStatus,
  RevenueType
} from '../types/project.js';

// 验证模式
const createRevenueSchema = z.object({
  title: z.string().min(1, '收入标题不能为空').max(200, '收入标题不能超过200字符'),
  revenueType: z.nativeEnum(RevenueType, { errorMap: () => ({ message: '无效的收入类型' }) }),
  plannedAmount: z.number().min(0, '预计金额不能为负数'),
  plannedDate: z.string().refine((date) => !isNaN(Date.parse(date)), { message: '无效的日期格式' }).optional(),
  milestone: z.string().max(200, '里程碑描述不能超过200字符').optional(),
  paymentTerms: z.string().optional(),
  notes: z.string().optional(),
});

const updateRevenueSchema = z.object({
  id: z.string().min(1, '收入ID不能为空'),
  title: z.string().min(1).max(200).optional(),
  revenueType: z.nativeEnum(RevenueType).optional(),
  status: z.nativeEnum(RevenueStatus).optional(),
  plannedAmount: z.number().min(0).optional(),
  actualAmount: z.number().min(0).optional(),
  invoiceAmount: z.number().min(0).optional(),
  plannedDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  confirmedDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  invoiceDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  receivedDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  milestone: z.string().max(200).optional(),
  invoiceNumber: z.string().max(100).optional(),
  paymentTerms: z.string().optional(),
  notes: z.string().optional(),
});

const revenueQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  pageSize: z.string().transform(Number).optional(),
  projectId: z.string().optional(),
  status: z.nativeEnum(RevenueStatus).optional(),
  revenueType: z.nativeEnum(RevenueType).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.enum(['plannedDate', 'plannedAmount', 'actualAmount', 'createdAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// 确认收入验证模式
const confirmRevenueSchema = z.object({
  actualAmount: z.number().min(0, '实际收入金额不能为负数'),
  confirmedDate: z.string().refine((date) => !isNaN(Date.parse(date)), { message: '无效的日期格式' }).optional(),
  notes: z.string().optional(),
});

// 批量确认收入验证模式
const batchConfirmRevenueSchema = z.object({
  revenues: z.array(z.object({
    id: z.string().min(1, '收入ID不能为空'),
    actualAmount: z.number().min(0, '实际收入金额不能为负数'),
    confirmedDate: z.string().refine((date) => !isNaN(Date.parse(date)), { message: '无效的日期格式' }).optional(),
    notes: z.string().optional(),
  })).min(1, '至少需要确认一个收入').max(100, '一次最多确认100个收入'),
});

export class RevenueController {
  private projectService: ProjectService;

  constructor() {
    this.projectService = new ProjectService();
  }

  /**
   * 创建项目收入
   */
  async createRevenue(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { projectId } = request.params as { projectId: string };
      const revenueData = createRevenueSchema.parse(request.body);

      // 获取当前用户ID
      const createdBy = 'current-user'; // 实际应该从认证信息中获取

      const revenue = await this.projectService.createProjectRevenue(projectId, revenueData, createdBy);

      return reply.send({
        success: true,
        data: revenue,
        message: '创建项目收入成功'
      });
    } catch (error) {
      console.error('创建项目收入失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '创建项目收入失败'
      });
    }
  }

  /**
   * 获取项目收入列表
   */
  async getRevenues(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = revenueQuerySchema.parse(request.query);
      console.log('获取项目收入列表:', queryParams);
      const result = await this.projectService.getProjectRevenues(queryParams);
      console.log('获取项目收入列表成功:', result);

      return reply.send({
        success: true,
        data: result,
        message: '获取项目收入列表成功'
      });
    } catch (error) {
      console.error('获取项目收入列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取项目收入列表失败'
      });
    }
  }

  /**
   * 获取单个项目收入
   */
  async getRevenue(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const revenue = await this.projectService.getProjectRevenue(id);

      if (!revenue) {
        return reply.status(404).send({
          success: false,
          message: '项目收入不存在'
        });
      }

      return reply.send({
        success: true,
        data: revenue,
        message: '获取项目收入成功'
      });
    } catch (error) {
      console.error('获取项目收入失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取项目收入失败'
      });
    }
  }

  /**
   * 更新项目收入
   */
  async updateRevenue(request: FastifyRequest, reply: FastifyReply) {
    try {
      const revenueData = updateRevenueSchema.parse(request.body);

      // 获取当前用户ID
      const updatedBy = 'current-user'; // 实际应该从认证信息中获取

      const revenue = await this.projectService.updateProjectRevenue(revenueData, updatedBy);

      return reply.send({
        success: true,
        data: revenue,
        message: '更新项目收入成功'
      });
    } catch (error) {
      console.error('更新项目收入失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '更新项目收入失败'
      });
    }
  }

  /**
   * 删除项目收入
   */
  async deleteRevenue(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      await this.projectService.deleteProjectRevenue(id);

      return reply.send({
        success: true,
        message: '删除项目收入成功'
      });
    } catch (error) {
      console.error('删除项目收入失败:', error);
      return reply.status(500).send({
        success: false,
        message: '删除项目收入失败'
      });
    }
  }

  /**
   * 确认收入
   */
  async confirmRevenue(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const confirmData = confirmRevenueSchema.parse(request.body);

      // 获取当前用户ID
      const updatedBy = 'current-user'; // 实际应该从认证信息中获取

      const revenue = await this.projectService.confirmProjectRevenue(id, confirmData, updatedBy);

      return reply.send({
        success: true,
        data: revenue,
        message: '确认收入成功'
      });
    } catch (error) {
      console.error('确认收入失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '确认收入失败'
      });
    }
  }

  /**
   * 批量确认收入
   */
  async batchConfirmRevenues(request: FastifyRequest, reply: FastifyReply) {
    try {
      const batchData = batchConfirmRevenueSchema.parse(request.body);

      // 获取当前用户ID
      const updatedBy = 'current-user'; // 实际应该从认证信息中获取

      const result = await this.projectService.batchConfirmProjectRevenues(batchData.revenues, updatedBy);

      return reply.send({
        success: true,
        data: result,
        message: `批量确认收入完成，成功 ${result.successCount} 个，失败 ${result.failureCount} 个`
      });
    } catch (error) {
      console.error('批量确认收入失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '批量确认收入失败'
      });
    }
  }

  /**
   * 获取收入统计
   */
  async getRevenueStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.projectService.getRevenueStats();

      return reply.send({
        success: true,
        data: stats,
        message: '获取收入统计成功'
      });
    } catch (error) {
      console.error('获取收入统计失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取收入统计失败'
      });
    }
  }
}
