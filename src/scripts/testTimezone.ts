import { TimezoneUtils } from '../utils/timezone.js';
import { env } from '../config/env.js';

/**
 * 测试时区配置和工具函数
 */
async function testTimezone() {
  console.log('🌍 时区配置测试');
  console.log('='.repeat(50));

  // 1. 基本时区信息
  console.log('1️⃣ 基本时区信息:');
  console.log(`配置的时区: ${env.TIMEZONE}`);
  console.log(`系统时区: ${process.env.TZ || 'undefined'}`);
  console.log(`当前时区: ${TimezoneUtils.getTimezone()}`);
  
  const timezoneInfo = TimezoneUtils.getTimezoneOffset();
  console.log(`时区偏移: ${timezoneInfo.offsetString} (${timezoneInfo.offset} 分钟)`);

  // 2. 当前时间测试
  console.log('\n2️⃣ 当前时间测试:');
  const now = TimezoneUtils.now();
  console.log(`当前时间 (Date): ${now}`);
  console.log(`当前时间 (ISO): ${TimezoneUtils.formatToISO(now)}`);
  console.log(`当前时间 (本地): ${TimezoneUtils.formatToLocal(now)}`);
  console.log(`当前日期: ${TimezoneUtils.formatToLocalDate(now)}`);
  console.log(`当前时间: ${TimezoneUtils.formatToLocalTime(now)}`);

  // 3. 时间范围测试
  console.log('\n3️⃣ 时间范围测试:');
  console.log(`今天开始: ${TimezoneUtils.formatToLocal(TimezoneUtils.getTodayStart())}`);
  console.log(`今天结束: ${TimezoneUtils.formatToLocal(TimezoneUtils.getTodayEnd())}`);
  console.log(`本周开始: ${TimezoneUtils.formatToLocal(TimezoneUtils.getWeekStart())}`);
  console.log(`本周结束: ${TimezoneUtils.formatToLocal(TimezoneUtils.getWeekEnd())}`);
  console.log(`本月开始: ${TimezoneUtils.formatToLocal(TimezoneUtils.getMonthStart())}`);
  console.log(`本月结束: ${TimezoneUtils.formatToLocal(TimezoneUtils.getMonthEnd())}`);
  console.log(`本年开始: ${TimezoneUtils.formatToLocal(TimezoneUtils.getYearStart())}`);
  console.log(`本年结束: ${TimezoneUtils.formatToLocal(TimezoneUtils.getYearEnd())}`);

  // 4. 日期操作测试
  console.log('\n4️⃣ 日期操作测试:');
  const testDate = new Date('2024-06-22T10:30:00Z');
  console.log(`测试日期: ${TimezoneUtils.formatToLocal(testDate)}`);
  console.log(`加3天: ${TimezoneUtils.formatToLocal(TimezoneUtils.addDays(testDate, 3))}`);
  console.log(`加5小时: ${TimezoneUtils.formatToLocal(TimezoneUtils.addHours(testDate, 5))}`);
  
  const today = new Date();
  const yesterday = TimezoneUtils.addDays(today, -1);
  console.log(`今天与昨天相差: ${TimezoneUtils.daysBetween(today, yesterday)} 天`);

  // 5. 日期判断测试
  console.log('\n5️⃣ 日期判断测试:');
  console.log(`今天是否为今天: ${TimezoneUtils.isToday(today)}`);
  console.log(`昨天是否为今天: ${TimezoneUtils.isToday(yesterday)}`);
  console.log(`今天是否为本周: ${TimezoneUtils.isThisWeek(today)}`);
  console.log(`今天是否为本月: ${TimezoneUtils.isThisMonth(today)}`);

  // 6. 持续时间格式化测试
  console.log('\n6️⃣ 持续时间格式化测试:');
  console.log(`30秒: ${TimezoneUtils.formatDuration(30 * 1000)}`);
  console.log(`5分钟: ${TimezoneUtils.formatDuration(5 * 60 * 1000)}`);
  console.log(`2小时: ${TimezoneUtils.formatDuration(2 * 60 * 60 * 1000)}`);
  console.log(`3天: ${TimezoneUtils.formatDuration(3 * 24 * 60 * 60 * 1000)}`);

  // 7. 数据库时间测试
  console.log('\n7️⃣ 数据库时间测试:');
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();
    
    // 测试数据库时间
    const dbTime = await prisma.$queryRaw`SELECT NOW() as current_time`;
    console.log(`数据库时间: ${JSON.stringify(dbTime)}`);
    
    await prisma.$disconnect();
  } catch (error) {
    console.log(`数据库连接失败: ${error instanceof Error ? error.message : String(error)}`);
  }

  // 8. 不同时区格式测试
  console.log('\n8️⃣ 不同时区格式测试:');
  const sampleDate = new Date();
  
  // 测试不同地区的时间格式
  const timezones = [
    'Asia/Shanghai',
    'Asia/Tokyo', 
    'America/New_York',
    'Europe/London',
    'UTC'
  ];

  timezones.forEach(tz => {
    const formatted = sampleDate.toLocaleString('zh-CN', {
      timeZone: tz,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    console.log(`${tz}: ${formatted}`);
  });

  console.log('\n' + '='.repeat(50));
  console.log('🎉 时区测试完成！');
}

// 运行测试
testTimezone()
  .then(() => {
    console.log('✅ 时区测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 时区测试失败:', error);
    process.exit(1);
  });
