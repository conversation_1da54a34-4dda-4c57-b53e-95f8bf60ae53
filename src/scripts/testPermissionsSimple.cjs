const { PrismaClient } = require('@prisma/client');

async function testPermissionSystem() {
  const prisma = new PrismaClient();

  try {
    console.log('🧪 开始测试权限系统...');

    // 1. 测试获取角色列表
    console.log('\n📋 测试获取角色列表...');
    const roles = await prisma.role.findMany({
      orderBy: { createdAt: 'asc' },
    });
    console.log(`✅ 找到 ${roles.length} 个角色:`);
    roles.forEach(role => {
      console.log(`  - ${role.displayName} (${role.name}) - ${role.isSystem ? '系统角色' : '自定义角色'}`);
    });

    // 2. 测试获取权限列表
    console.log('\n🔐 测试获取权限列表...');
    const permissions = await prisma.permission.findMany({
      take: 10,
      orderBy: { module: 'asc' },
    });
    console.log(`✅ 找到权限 (显示前10个):`);
    permissions.forEach(permission => {
      console.log(`  - ${permission.displayName} (${permission.name}) - ${permission.module}.${permission.action}`);
    });

    // 3. 测试按模块分组获取权限
    console.log('\n📦 测试按模块分组获取权限...');
    const moduleStats = await prisma.permission.groupBy({
      by: ['module'],
      _count: true,
      orderBy: { module: 'asc' },
    });
    console.log(`✅ 找到 ${moduleStats.length} 个模块:`);
    moduleStats.forEach(stat => {
      console.log(`  - ${stat.module}: ${stat._count} 个权限`);
    });

    // 4. 测试获取超级管理员角色的权限
    console.log('\n👑 测试获取超级管理员角色权限...');
    const superAdminRole = roles.find(role => role.name === 'super_admin');
    if (superAdminRole) {
      const rolePermissions = await prisma.rolePermission.findMany({
        where: { roleId: superAdminRole.id },
        include: {
          permission: true,
        },
      });
      console.log(`✅ 超级管理员拥有 ${rolePermissions.length} 个权限`);
      
      // 显示前5个权限作为示例
      console.log('  示例权限:');
      rolePermissions.slice(0, 5).forEach(rp => {
        console.log(`    - ${rp.permission.displayName} (${rp.permission.name})`);
      });
    } else {
      console.log('❌ 未找到超级管理员角色');
    }

    // 5. 测试用户权限（如果有用户数据）
    console.log('\n👤 测试用户权限查询...');
    const users = await prisma.user.findMany({
      take: 1,
      where: { isActive: true },
    });

    if (users.length > 0) {
      const testUser = users[0];
      console.log(`测试用户: ${testUser.name} (${testUser.userid})`);

      // 为测试用户分配普通用户角色
      const userRole = roles.find(role => role.name === 'user');
      if (userRole) {
        // 先删除现有角色
        await prisma.userRole.deleteMany({
          where: { userid: testUser.userid },
        });

        // 分配新角色
        await prisma.userRole.create({
          data: {
            userid: testUser.userid,
            roleId: userRole.id,
            createdBy: 'system',
          },
        });
        console.log(`✅ 为用户分配了角色: ${userRole.displayName}`);

        // 获取用户权限信息
        const userRoles = await prisma.userRole.findMany({
          where: { userid: testUser.userid },
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        });

        let totalPermissions = 0;
        userRoles.forEach(ur => {
          totalPermissions += ur.role.rolePermissions.length;
        });

        console.log(`✅ 用户拥有 ${totalPermissions} 个权限`);
        console.log(`✅ 用户拥有 ${userRoles.length} 个角色`);

        // 显示用户的权限
        if (userRoles.length > 0) {
          console.log('  用户权限:');
          userRoles[0].role.rolePermissions.forEach(rp => {
            console.log(`    - ${rp.permission.displayName} (${rp.permission.name})`);
          });
        }
      }
    } else {
      console.log('⚠️ 没有找到用户数据，跳过用户权限测试');
    }

    // 6. 测试部门权限（如果有部门数据）
    console.log('\n🏢 测试部门权限...');
    const departments = await prisma.department.findMany({
      take: 1,
    });

    if (departments.length > 0) {
      const testDept = departments[0];
      console.log(`测试部门: ${testDept.name} (${testDept.deptId})`);

      // 为测试部门分配项目经理角色
      const pmRole = roles.find(role => role.name === 'project_manager');
      if (pmRole) {
        // 先删除现有角色
        await prisma.departmentRole.deleteMany({
          where: { deptId: testDept.deptId },
        });

        // 分配新角色
        await prisma.departmentRole.create({
          data: {
            deptId: testDept.deptId,
            roleId: pmRole.id,
            createdBy: 'system',
          },
        });
        console.log(`✅ 为部门分配了角色: ${pmRole.displayName}`);

        // 获取部门角色
        const deptRoles = await prisma.departmentRole.findMany({
          where: { deptId: testDept.deptId },
          include: {
            role: true,
          },
        });
        console.log(`✅ 部门拥有 ${deptRoles.length} 个角色`);
      }
    } else {
      console.log('⚠️ 没有找到部门数据，跳过部门权限测试');
    }

    console.log('\n🎉 权限系统测试完成！');

  } catch (error) {
    console.error('❌ 权限系统测试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testPermissionSystem()
  .then(() => {
    console.log('✅ 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
