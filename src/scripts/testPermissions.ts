import { DatabaseService } from '../services/database.js';
import { PermissionService } from '../services/permissionService.js';
import { RoleService } from '../services/roleService.js';
import { UserPermissionService } from '../services/userPermissionService.js';

async function testPermissionSystem() {
  console.log('🧪 开始测试权限系统...');

  const databaseService = new DatabaseService();
  const roleService = new RoleService(databaseService);
  const permissionService = new PermissionService(databaseService);
  const userPermissionService = new UserPermissionService(databaseService);

  try {
    // 1. 测试获取角色列表
    console.log('\n📋 测试获取角色列表...');
    const rolesResult = await roleService.getRoles({ page: 1, pageSize: 10 });
    console.log(`✅ 找到 ${rolesResult.total} 个角色:`);
    rolesResult.roles.forEach(role => {
      console.log(`  - ${role.displayName} (${role.name}) - ${role.isSystem ? '系统角色' : '自定义角色'}`);
    });

    // 2. 测试获取权限列表
    console.log('\n🔐 测试获取权限列表...');
    const permissionsResult = await permissionService.getPermissions({ page: 1, pageSize: 10 });
    console.log(`✅ 找到 ${permissionsResult.total} 个权限:`);
    permissionsResult.permissions.slice(0, 5).forEach(permission => {
      console.log(`  - ${permission.displayName} (${permission.name}) - ${permission.module}.${permission.action}`);
    });

    // 3. 测试按模块分组获取权限
    console.log('\n📦 测试按模块分组获取权限...');
    const modulePermissions = await permissionService.getPermissionsByModule();
    console.log(`✅ 找到 ${modulePermissions.length} 个模块:`);
    modulePermissions.forEach(module => {
      console.log(`  - ${module.module}: ${module.permissions.length} 个权限`);
    });

    // 4. 测试获取超级管理员角色的权限
    console.log('\n👑 测试获取超级管理员角色权限...');
    const superAdminRole = rolesResult.roles.find(role => role.name === 'super_admin');
    if (superAdminRole) {
      const rolePermissions = await roleService.getRolePermissions(superAdminRole.id);
      console.log(`✅ 超级管理员拥有 ${rolePermissions.length} 个权限`);
    } else {
      console.log('❌ 未找到超级管理员角色');
    }

    // 5. 测试用户权限（如果有用户数据）
    console.log('\n👤 测试用户权限查询...');
    const users = await databaseService.client.user.findMany({
      take: 1,
      where: { isActive: true },
    });

    if (users.length > 0) {
      const testUser = users[0]!;
      console.log(`测试用户: ${testUser.name} (${testUser.userid})`);

      // 为测试用户分配普通用户角色
      const userRole = rolesResult.roles.find(role => role.name === 'user');
      if (userRole) {
        await userPermissionService.assignUserRoles({
          userid: testUser.userid,
          roleIds: [userRole.id],
          assignedBy: 'system',
        });
        console.log(`✅ 为用户分配了角色: ${userRole.displayName}`);

        // 获取用户权限信息
        const userPermissionInfo = await userPermissionService.getUserPermissionInfo(testUser.userid);
        console.log(`✅ 用户拥有 ${userPermissionInfo.permissions.length} 个权限`);
        console.log(`✅ 用户拥有 ${userPermissionInfo.roles.length} 个角色`);
        console.log(`  - 直接角色: ${userPermissionInfo.directRoles.length} 个`);
        console.log(`  - 继承角色: ${userPermissionInfo.inheritedRoles.length} 个`);

        // 测试权限检查
        const testPermissions = ['project.read', 'project.create', 'user.read'];
        const permissionResults = await userPermissionService.hasPermissions(testUser.userid, testPermissions);
        console.log('✅ 权限检查结果:');
        Object.entries(permissionResults).forEach(([permission, hasPermission]) => {
          console.log(`  - ${permission}: ${hasPermission ? '✅ 有权限' : '❌ 无权限'}`);
        });
      }
    } else {
      console.log('⚠️ 没有找到用户数据，跳过用户权限测试');
    }

    // 6. 测试部门权限（如果有部门数据）
    console.log('\n🏢 测试部门权限...');
    const departments = await databaseService.client.department.findMany({
      take: 1,
    });

    if (departments.length > 0) {
      const testDept = departments[0]!;
      console.log(`测试部门: ${testDept.name} (${testDept.deptId})`);

      // 为测试部门分配项目经理角色
      const pmRole = rolesResult.roles.find(role => role.name === 'project_manager');
      if (pmRole) {
        await userPermissionService.assignDepartmentRoles({
          deptId: testDept.deptId,
          roleIds: [pmRole.id],
          assignedBy: 'system',
        });
        console.log(`✅ 为部门分配了角色: ${pmRole.displayName}`);

        // 获取部门角色
        const deptRoles = await userPermissionService.getDepartmentRoles(testDept.deptId);
        console.log(`✅ 部门拥有 ${deptRoles.length} 个角色`);
      }
    } else {
      console.log('⚠️ 没有找到部门数据，跳过部门权限测试');
    }

    console.log('\n🎉 权限系统测试完成！');

  } catch (error) {
    console.error('❌ 权限系统测试失败:', error);
    throw error;
  } finally {
    await databaseService.disconnect();
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  testPermissionSystem()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

export { testPermissionSystem };

