/**
 * 测试财务导出功能
 */

import { FinancialExportService } from '../services/financialExport.js';
import { TimezoneUtils } from '../utils/timezone.js';

async function testFinancialExport() {
  console.log('🧪 开始测试财务导出功能...\n');

  try {
    const exportService = new FinancialExportService();

    // 测试参数
    const testParams = {
      year: 2024,
      includeCompleted: true,
      includeCancelled: false
    };

    console.log('📊 测试参数:', testParams);
    console.log(`🕐 当前时间: ${TimezoneUtils.formatToLocal(TimezoneUtils.now())}`);
    console.log(`🌍 时区: ${TimezoneUtils.getTimezone()}\n`);

    // 执行导出
    console.log('🚀 开始生成财务报表...');
    const startTime = Date.now();
    
    const buffer = await exportService.exportFinancialReport(testParams);
    
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`✅ 财务报表生成成功！`);
    console.log(`📄 文件大小: ${(buffer.length / 1024).toFixed(2)} KB`);
    console.log(`⏱️  生成耗时: ${duration} ms`);
    console.log(`🕐 完成时间: ${TimezoneUtils.formatToLocal(TimezoneUtils.now())}`);

    // 可选：保存到文件
    const fs = await import('fs');
    const filename = `财务报表_${testParams.year}年_${TimezoneUtils.formatToLocalDate(TimezoneUtils.now()).replace(/-/g, '')}.xlsx`;
    fs.writeFileSync(filename, buffer);
    console.log(`💾 文件已保存: ${filename}`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
    }
  }
}

// 运行测试
testFinancialExport().then(() => {
  console.log('\n🎉 财务导出测试完成！');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 测试过程中发生错误:', error);
  process.exit(1);
});
