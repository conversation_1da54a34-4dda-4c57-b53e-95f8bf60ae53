import { FastifyReply, FastifyRequest } from 'fastify';
import { DingTalkService } from '../services/dingtalk.js';
import { jwtService } from '../services/jwt.js';

// JWT认证中间件
export async function jwtAuthMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    // 从请求头获取JWT token
    const authorization = request.headers.authorization;

    if (!authorization || !authorization.startsWith('Bearer ')) {
      return reply.status(401).send({
        success: false,
        message: '缺少访问令牌',
        code: 'MISSING_TOKEN'
      });
    }

    const token = authorization.substring(7);
    const payload = await jwtService.verifyAccessToken(token);

    if (!payload) {
      return reply.status(401).send({
        success: false,
        message: '访问令牌无效或已过期',
        code: 'INVALID_TOKEN'
      });
    }

    // 将用户信息添加到请求上下文
    (request as any).user = {
      userid: payload.userid,
      name: payload.name,
      mobile: payload.mobile,
      deptIds: payload.deptIds,
      isAdmin: payload.isAdmin,
      isBoss: payload.isBoss
    };

    // 记录用户访问日志
    request.log.info({
      userid: payload.userid,
      name: payload.name,
      action: `${request.method} ${request.url}`
    }, '用户访问');

  } catch (error) {
    request.log.error(error, 'JWT认证失败');
    return reply.status(401).send({
      success: false,
      message: '认证服务异常',
      code: 'AUTH_SERVICE_ERROR'
    });
  }
}

// 钉钉免登认证中间件（已弃用，保留用于兼容性）
export async function dingTalkAuthMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    // 从请求头获取钉钉免登码
    const authCode = request.headers['x-dingtalk-auth-code'] as string;

    if (!authCode) {
      return reply.status(401).send({
        success: false,
        message: '缺少钉钉免登认证码',
        code: 'MISSING_AUTH_CODE'
      });
    }

    // 验证免登码并获取用户信息
    const dingTalkService = new DingTalkService();
    const userInfo = await dingTalkService.getUserInfoByAuthCode(authCode);

    if (!userInfo) {
      return reply.status(401).send({
        success: false,
        message: '钉钉免登认证失败',
        code: 'AUTH_FAILED'
      });
    }

    // 将用户信息添加到请求上下文
    (request as any).user = {
      userid: userInfo.userid,
      name: userInfo.name,
      mobile: userInfo.mobile,
      deptIds: userInfo.dept_id_list || userInfo.department || [],
      isAdmin: userInfo.admin || false,
      isBoss: userInfo.boss || false
    };

    // 记录用户访问日志
    request.log.info({
      userid: userInfo.userid,
      name: userInfo.name,
      action: `${request.method} ${request.url}`
    }, '用户访问');

  } catch (error) {
    request.log.error(error, '钉钉免登认证失败');
    return reply.status(401).send({
      success: false,
      message: '认证服务异常',
      code: 'AUTH_SERVICE_ERROR'
    });
  }
}

// 管理员权限检查中间件
export async function adminAuthMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  const user = (request as any).user;

  if (!user) {
    return reply.status(401).send({
      success: false,
      message: '用户未认证',
      code: 'USER_NOT_AUTHENTICATED'
    });
  }

  if (!user.isAdmin && !user.isBoss) {
    return reply.status(403).send({
      success: false,
      message: '权限不足，需要管理员权限',
      code: 'INSUFFICIENT_PERMISSIONS'
    });
  }
}

// 部门权限检查中间件
export function departmentAuthMiddleware(allowedDeptIds: number[]) {
  return async function (request: FastifyRequest, reply: FastifyReply) {
    const user = (request as any).user;

    if (!user) {
      return reply.status(401).send({
        success: false,
        message: '用户未认证',
        code: 'USER_NOT_AUTHENTICATED'
      });
    }

    // 管理员和老板可以访问所有部门数据
    if (user.isAdmin || user.isBoss) {
      return;
    }

    // 检查用户是否属于允许的部门
    const hasPermission = user.deptIds.some((deptId: number) =>
      allowedDeptIds.includes(deptId)
    );

    if (!hasPermission) {
      return reply.status(403).send({
        success: false,
        message: '权限不足，无法访问该部门数据',
        code: 'DEPARTMENT_ACCESS_DENIED'
      });
    }
  };
}

// 用户类型定义
export interface AuthenticatedUser {
  userid: string;
  name: string;
  mobile?: string;
  deptIds: number[];
  isAdmin: boolean;
  isBoss: boolean;
}

// 扩展 FastifyRequest 类型
declare module 'fastify' {
  interface FastifyRequest {
    user?: AuthenticatedUser;
  }
}
