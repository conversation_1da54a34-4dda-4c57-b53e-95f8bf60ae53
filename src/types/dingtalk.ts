// 钉钉API响应基础类型
export interface DingTalkBaseResponse {
  errcode: number;
  errmsg: string;
}

// 获取访问令牌响应
export interface AccessTokenResponse extends DingTalkBaseResponse {
  access_token?: string;
  expires_in?: number;
}

// 用户信息
export interface UserInfo {
  userid: string;
  name: string;
  avatar?: string;
  mobile?: string;
  email?: string;
  department?: number[];
  dept_id_list?: number[];  // 钉钉API返回的部门ID列表
  position?: string;
  job_number?: string;
  admin?: boolean;          // 是否为管理员
  boss?: boolean;           // 是否为老板
  leader?: boolean;         // 是否为部门主管

  // 扩展字段 - 根据钉钉API文档添加
  unionid?: string;         // 员工在当前开发者企业账号范围内的唯一标识
  state_code?: string;      // 国际电话区号
  manager_userid?: string;  // 员工的直属主管userid
  hide_mobile?: boolean;    // 是否隐藏手机号
  telephone?: string;       // 分机号
  title?: string;           // 职位
  work_place?: string;      // 办公地点
  remark?: string;          // 备注
  login_id?: string;        // 登录ID
  exclusive_account_type?: string; // 专属账号类型
  exclusive_account?: boolean;     // 是否专属账号
  extension?: string;       // 扩展属性
  hired_date?: number;      // 入职时间（Unix时间戳）
  active?: boolean;         // 是否激活
  real_authed?: boolean;    // 是否实名认证
  org_email?: string;       // 企业邮箱
  org_email_type?: string;  // 企业邮箱类型
  senior?: boolean;         // 是否高管
}

// 获取用户信息响应
export interface GetUserResponse extends DingTalkBaseResponse {
  result?: UserInfo;
}

// 部门信息
export interface Department {
  dept_id: number;
  name: string;
  parent_id: number;
  order?: number;
  create_dept_group?: boolean;
  auto_add_user?: boolean;
}

// 获取部门列表响应
export interface GetDepartmentListResponse extends DingTalkBaseResponse {
  result?: Department[];
}

// 钉钉免登码
export interface AuthCodeInfo {
  authCode: string;
}

// 通过免登码获取用户信息响应
export interface GetUserInfoByCodeResponse extends DingTalkBaseResponse {
  result?: {
    userid: string;
    device_id?: string;
    sys?: boolean;
    associated_unionid?: string;
  };
}

// JSAPI签名信息
export interface JSAPISignature {
  agentId: string;
  corpId: string;
  timeStamp: number;
  nonceStr: string;
  signature: string;
}

// 钉钉配置信息
export interface DingTalkConfig {
  appKey: string;
  appSecret: string;
  corpId: string;
  agentId?: string;
}

// 应用配置信息
export interface AppConfig {
  appKey: string;
  corpId: string;
  agentId: string;
  jsApiList: string[];
  debug?: boolean;
}

// 用户权限信息
export interface UserPermissions {
  userid: string;
  permissions: string[];
  roles: string[];
}

// 消息推送
export interface MessageRequest {
  userIds: string[];
  title: string;
  content: string;
  messageType?: 'text' | 'markdown' | 'actionCard';
}

// 通知消息模板类型
export interface NotificationTemplate {
  title: string;
  content: string;
  messageType: 'text' | 'markdown';
}

// 项目创建通知数据
export interface ProjectCreatedNotificationData {
  projectName: string;
  brandName: string;
  executorPMName: string;
  budget: number;
  startDate: string;
  endDate: string;
  creatorName: string;
}

// 周预算超额通知数据
export interface WeeklyBudgetExceededNotificationData {
  projectName: string;
  brandName: string;
  weeklyBudgetTitle: string;
  contractAmount: number;
  projectCost: number;
  exceedPercentage: number;
  creatorName: string;
}

// 文件上传响应
export interface FileUploadResponse extends DingTalkBaseResponse {
  media_id: string;
  created_at: number;
  type: string;
}

// 通讯录用户列表
export interface UserListResponse extends DingTalkBaseResponse {
  result?: {
    list: UserInfo[];
    has_more: boolean;
    next_cursor?: number;
  };
}

// 考勤数据
export interface AttendanceRecord {
  userid: string;
  check_type: string;
  location_result: string;
  location_title: string;
  location_detail: string;
  user_check_time: number;
  base_check_time: number;
  source_type: string;
}

// 审批实例
export interface ApprovalInstance {
  process_instance_id: string;
  business_id: string;
  title: string;
  create_time: number;
  finish_time?: number;
  originator_userid: string;
  status: string;
  result: string;
}
