<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目创建表单 - 钉钉用户集成演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 15px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
            margin-bottom: 20px;
        }

        .form-group.full-width {
            flex: 100%;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .required {
            color: #ff4d4f;
        }

        input,
        select,
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        input:focus,
        select:focus,
        textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        .user-selector {
            position: relative;
            width: 100%;
        }

        .user-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .user-item {
            padding: 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }

        .user-item:hover {
            background-color: #f5f5f5;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            font-weight: 500;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 500;
            color: #333;
        }

        .user-mobile {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .selected-users {
            margin-top: 10px;
        }

        .selected-user {
            display: inline-block;
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
        }

        .remove-user {
            margin-left: 5px;
            color: #999;
            cursor: pointer;
        }

        .remove-user:hover {
            color: #ff4d4f;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin: 30px 0 20px 0;
            color: #333;
            border-left: 4px solid #1890ff;
            padding-left: 12px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            margin-right: 10px;
        }

        .btn-secondary:hover {
            background-color: #e6e6e6;
        }

        .form-actions {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e8e8e8;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            color: #ff4d4f;
            padding: 10px;
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 4px;
            margin-top: 10px;
        }

        .success {
            color: #52c41a;
            padding: 10px;
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            margin-top: 10px;
        }

        .auth-warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .auth-warning h4 {
            margin-top: 0;
            color: #d48806;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>📋 项目创建表单</h1>

        <div class="auth-warning" id="authWarning">
            <h4>🔐 认证提醒</h4>
            <p>此表单需要钉钉免登认证才能提交。请确保您已通过钉钉客户端访问并完成认证。</p>
            <p>如果您还未认证，请先访问 <a href="/dingtalk-auth-demo.html">钉钉认证页面</a> 完成认证。</p>
        </div>

        <form id="projectForm">
            <!-- 基本信息 -->
            <div class="section-title">基本信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="documentType">单据类型 <span class="required">*</span></label>
                    <select id="documentType" name="documentType" required>
                        <option value="">请选择单据类型</option>
                        <option value="project_proposal">项目提案</option>
                        <option value="project_plan">项目计划</option>
                        <option value="project_execution">项目执行</option>
                        <option value="project_summary">项目总结</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="brandId">品牌 <span class="required">*</span></label>
                    <select id="brandId" name="brandId" required>
                        <option value="">请选择品牌</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="projectName">项目名称 <span class="required">*</span></label>
                <input type="text" id="projectName" name="projectName" placeholder="请输入项目名称" required>
            </div>

            <!-- 项目周期 -->
            <div class="section-title">项目周期</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="startDate">开始日期 <span class="required">*</span></label>
                    <input type="date" id="startDate" name="startDate" required>
                </div>
                <div class="form-group">
                    <label for="endDate">结束日期 <span class="required">*</span></label>
                    <input type="date" id="endDate" name="endDate" required>
                </div>
            </div>

            <!-- 预算信息 -->
            <div class="section-title">预算信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="planningBudget">规划预算 <span class="required">*</span></label>
                    <input type="number" id="planningBudget" name="planningBudget" placeholder="0.00" step="0.01"
                        min="0" required>
                </div>
                <div class="form-group">
                    <label for="influencerBudget">达人预算 <span class="required">*</span></label>
                    <input type="number" id="influencerBudget" name="influencerBudget" placeholder="0.00" step="0.01"
                        min="0" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="adBudget">投流预算 <span class="required">*</span></label>
                    <input type="number" id="adBudget" name="adBudget" placeholder="0.00" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="otherBudget">其他预算 <span class="required">*</span></label>
                    <input type="number" id="otherBudget" name="otherBudget" placeholder="0.00" step="0.01" min="0"
                        required>
                </div>
            </div>

            <!-- 成本信息 -->
            <div class="section-title">成本信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="influencerCost">达人成本 <span class="required">*</span></label>
                    <input type="number" id="influencerCost" name="influencerCost" placeholder="0.00" step="0.01"
                        min="0" required>
                </div>
                <div class="form-group">
                    <label for="adCost">投流成本 <span class="required">*</span></label>
                    <input type="number" id="adCost" name="adCost" placeholder="0.00" step="0.01" min="0" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="otherCost">其他成本 <span class="required">*</span></label>
                    <input type="number" id="otherCost" name="otherCost" placeholder="0.00" step="0.01" min="0"
                        required>
                </div>
                <div class="form-group">
                    <label for="estimatedInfluencerRebate">预估达人返点 <span class="required">*</span></label>
                    <input type="number" id="estimatedInfluencerRebate" name="estimatedInfluencerRebate"
                        placeholder="0.00" step="0.01" min="0" required>
                </div>
            </div>

            <!-- 人员配置 -->
            <div class="section-title">人员配置</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="executorPM">执行PM <span class="required">*</span></label>
                    <div class="user-selector" id="executorPMSelector">
                        <input type="text" class="user-input" id="executorPMInput" placeholder="搜索并选择执行PM..."
                            autocomplete="off">
                        <div class="user-dropdown" id="executorPMDropdown"></div>
                        <div class="selected-users" id="executorPMSelected"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="contentMedia">内容媒介 <span class="required">*</span></label>
                    <div class="user-selector" id="contentMediaSelector">
                        <input type="text" class="user-input" id="contentMediaInput" placeholder="搜索并选择内容媒介（可多选）..."
                            autocomplete="off">
                        <div class="user-dropdown" id="contentMediaDropdown"></div>
                        <div class="selected-users" id="contentMediaSelected"></div>
                    </div>
                </div>
            </div>

            <!-- 项目详情 -->
            <div class="section-title">项目详情</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="contractType">合同类型 <span class="required">*</span></label>
                    <select id="contractType" name="contractType" required>
                        <option value="">请选择合同类型</option>
                        <option value="monthly_frame">月度框架</option>
                        <option value="quarterly_frame">季度框架</option>
                        <option value="annual_frame">年度框架</option>
                        <option value="project_based">项目制</option>
                        <option value="performance_based">效果付费</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="settlementRules">项目结算规则 <span class="required">*</span></label>
                <textarea id="settlementRules" name="settlementRules" placeholder="请输入项目结算规则" required></textarea>
            </div>

            <div class="form-group">
                <label for="kpi">KPI <span class="required">*</span></label>
                <textarea id="kpi" name="kpi" placeholder="请输入KPI指标" required></textarea>
            </div>

            <!-- 表单操作 -->
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                <button type="submit" class="btn btn-primary">创建项目</button>
            </div>

            <div id="message"></div>
        </form>
    </div>

    <script src="/user-selector.js"></script>
    <script>
        let authCode = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            checkAuthentication();
            loadBrands();
            initUserSelectors();
        });

        // 检查认证状态
        function checkAuthentication() {
            // 从URL参数获取认证码
            const urlParams = new URLSearchParams(window.location.search);
            authCode = urlParams.get('authCode');

            if (authCode) {
                // 隐藏认证警告
                document.getElementById('authWarning').style.display = 'none';
                console.log('已获取认证码:', authCode);
            } else {
                console.warn('未获取到认证码，表单提交可能会失败');
            }
        }

        // 加载品牌列表
        async function loadBrands() {
            try {
                const headers = {};
                if (authCode) {
                    headers['X-DingTalk-Auth-Code'] = authCode;
                }

                const response = await fetch('/api/brands?status=active', {
                    headers: headers
                });
                const data = await response.json();

                if (data.success) {
                    const brandSelect = document.getElementById('brandId');
                    brandSelect.innerHTML = '<option value="">请选择品牌</option>';

                    data.data.brands.forEach(brand => {
                        const option = document.createElement('option');
                        option.value = brand.id;
                        option.textContent = brand.name;
                        brandSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载品牌失败:', error);
            }
        }

        // 初始化用户选择器
        function initUserSelectors() {
            // 执行PM选择器（单选）
            window.pmSelector = new UserSelector(
                'executorPMInput',
                'executorPMDropdown',
                'executorPMSelected',
                false
            );

            // 内容媒介选择器（多选）
            window.mediaSelector = new UserSelector(
                'contentMediaInput',
                'contentMediaDropdown',
                'contentMediaSelected',
                true
            );
        }

        // 表单提交
        document.getElementById('projectForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = '';

            try {
                // 验证人员选择
                const selectedPM = window.pmSelector.getSelectedUsers();
                const selectedMedia = window.mediaSelector.getSelectedUsers();

                if (selectedPM.length === 0) {
                    throw new Error('请选择执行PM');
                }

                if (selectedMedia.length === 0) {
                    throw new Error('请选择至少一个内容媒介');
                }

                // 收集表单数据
                const formData = new FormData(this);
                const projectData = {
                    documentType: formData.get('documentType'),
                    brandId: formData.get('brandId'),
                    projectName: formData.get('projectName'),
                    period: {
                        startDate: formData.get('startDate'),
                        endDate: formData.get('endDate')
                    },
                    budget: {
                        planningBudget: parseFloat(formData.get('planningBudget')),
                        influencerBudget: parseFloat(formData.get('influencerBudget')),
                        adBudget: parseFloat(formData.get('adBudget')),
                        otherBudget: parseFloat(formData.get('otherBudget'))
                    },
                    cost: {
                        influencerCost: parseFloat(formData.get('influencerCost')),
                        adCost: parseFloat(formData.get('adCost')),
                        otherCost: parseFloat(formData.get('otherCost')),
                        estimatedInfluencerRebate: parseFloat(formData.get('estimatedInfluencerRebate'))
                    },
                    executorPM: selectedPM[0].userid,
                    contentMediaIds: selectedMedia.map(user => user.userid),
                    contractType: formData.get('contractType'),
                    settlementRules: formData.get('settlementRules'),
                    kpi: formData.get('kpi')
                };

                // 提交数据
                const headers = {
                    'Content-Type': 'application/json'
                };

                // 添加认证头
                if (authCode) {
                    headers['X-DingTalk-Auth-Code'] = authCode;
                } else {
                    throw new Error('缺少钉钉认证码，请先完成认证');
                }

                const response = await fetch('/api/projects', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(projectData)
                });

                const result = await response.json();

                if (result.success) {
                    messageDiv.innerHTML = '<div class="success">项目创建成功！</div>';
                    // 可以重定向到项目详情页面
                    setTimeout(() => {
                        // window.location.href = `/projects/${result.data.id}`;
                        resetForm();
                    }, 2000);
                } else {
                    throw new Error(result.message || '创建项目失败');
                }

            } catch (error) {
                console.error('创建项目失败:', error);
                messageDiv.innerHTML = `<div class="error">${error.message}</div>`;
            }
        });

        // 重置表单
        function resetForm() {
            document.getElementById('projectForm').reset();
            document.getElementById('message').innerHTML = '';

            // 重置用户选择器
            if (window.pmSelector) {
                window.pmSelector.reset();
            }
            if (window.mediaSelector) {
                window.mediaSelector.reset();
            }
        }
    </script>
</body>

</html>