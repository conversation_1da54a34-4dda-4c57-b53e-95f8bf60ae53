<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 12px 12px 0 0;
            margin: -20px -20px 20px -20px;
        }

        .header h1 {
            color: white;
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            margin: 0;
        }

        .nav-menu {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.9);
            color: #1890ff;
            font-weight: bold;
        }

        h1 {
            color: #333;
            text-align: center;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-section h3 {
            color: #1890ff;
            margin-top: 0;
        }

        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #40a9ff;
        }

        .result {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            border-color: #52c41a;
            background: #f6ffed;
        }

        .error {
            border-color: #ff4d4f;
            background: #fff2f0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🧪 API测试页面</h1>
            <p>测试钉钉后端API接口的功能</p>

            <!-- 导航菜单 -->
            <div class="nav-menu">
                <a href="index.html" class="nav-link">🏠 首页</a>
                <a href="dingtalk-design-demo.html" class="nav-link">🎨 完整功能</a>
                <a href="dingtalk-login-demo.html" class="nav-link">🔐 免登录演示</a>
                <a href="test-api.html" class="nav-link active">🧪 API测试</a>
                <a href="signature-test.html" class="nav-link">🔑 签名测试</a>
                <a href="official-login-demo.html" class="nav-link">📱 官方演示</a>
            </div>
        </div>

        <div class="test-section">
            <h3>1. 健康检查</h3>
            <p>测试服务器是否正常运行</p>
            <button onclick="testHealth()">测试健康检查</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 应用配置</h3>
            <p>获取钉钉应用的配置信息</p>
            <button onclick="testAppConfig()">获取应用配置</button>
            <div id="configResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. JSAPI签名</h3>
            <p>获取钉钉JSAPI签名信息</p>
            <button onclick="testJSAPISignature()">获取JSAPI签名</button>
            <div id="signatureResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 增强版JSAPI签名</h3>
            <p>获取使用真实ticket的JSAPI签名</p>
            <button onclick="testEnhancedSignature()">获取增强版签名</button>
            <div id="enhancedResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. 部门列表</h3>
            <p>获取企业部门列表</p>
            <button onclick="testDepartments()">获取部门列表</button>
            <div id="departmentsResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>6. 部门用户</h3>
            <p>获取指定部门的用户列表</p>
            <input type="number" id="deptId" value="1" min="1" placeholder="部门ID">
            <button onclick="testDepartmentUsers()">获取部门用户</button>
            <div id="usersResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>7. 应用统计</h3>
            <p>获取应用统计信息</p>
            <button onclick="testAppStats()">获取应用统计</button>
            <div id="statsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 通用API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 显示结果
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';

            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `错误: ${result.error || result.data?.message || '未知错误'}`;
            }
        }

        // 测试函数
        async function testHealth() {
            const result = await apiCall('/api/health');
            showResult('healthResult', result);
        }

        async function testAppConfig() {
            const result = await apiCall('/api/app/config');
            showResult('configResult', result);
        }

        async function testJSAPISignature() {
            const currentUrl = encodeURIComponent(window.location.href);
            const result = await apiCall(`/api/auth/jsapi-signature?url=${currentUrl}`);
            showResult('signatureResult', result);
        }

        async function testEnhancedSignature() {
            const currentUrl = encodeURIComponent(window.location.href);
            const result = await apiCall(`/api/app/jsapi-signature/enhanced?url=${currentUrl}`);
            showResult('enhancedResult', result);
        }

        async function testDepartments() {
            const result = await apiCall('/api/auth/departments');
            showResult('departmentsResult', result);
        }

        async function testDepartmentUsers() {
            const deptId = document.getElementById('deptId').value;
            const result = await apiCall(`/api/app/department/users?deptId=${deptId}&cursor=0&size=10`);
            showResult('usersResult', result);
        }

        async function testAppStats() {
            const result = await apiCall('/api/app/stats');
            showResult('statsResult', result);
        }

        // 页面加载完成后显示当前URL
        window.addEventListener('load', () => {
            console.log('当前页面URL:', window.location.href);
            console.log('编码后的URL:', encodeURIComponent(window.location.href));
        });
    </script>
</body>

</html>