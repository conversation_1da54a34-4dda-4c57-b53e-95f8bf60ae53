<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当月周预算导出</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        select,
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .preview-btn {
            background-color: #28a745;
        }

        .preview-btn:hover {
            background-color: #218838;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
            color: #666;
        }

        .preview-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }

        .preview-info h3 {
            margin-top: 0;
            color: #333;
        }

        .preview-info p {
            margin: 5px 0;
            color: #666;
        }

        .error-message {
            color: #dc3545;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>📊 当月周预算导出</h1>

        <form id="exportForm">
            <div class="form-group">
                <label for="year">年份:</label>
                <select id="year" name="year" required>
                    <option value="">请选择年份</option>
                </select>
            </div>

            <div class="form-group">
                <label for="month">月份:</label>
                <select id="month" name="month" required>
                    <option value="">请选择月份</option>
                    <option value="1">1月</option>
                    <option value="2">2月</option>
                    <option value="3">3月</option>
                    <option value="4">4月</option>
                    <option value="5">5月</option>
                    <option value="6">6月</option>
                    <option value="7">7月</option>
                    <option value="8">8月</option>
                    <option value="9">9月</option>
                    <option value="10">10月</option>
                    <option value="11">11月</option>
                    <option value="12">12月</option>
                </select>
            </div>

            <div class="form-group">
                <label for="token" class="token">JWT Token:</label>
                <input type="text" id="token" name="token" placeholder="请输入您的JWT Token">
            </div>

            <button type="button" id="previewBtn" class="preview-btn">📋 预览信息</button>
            <button type="submit" id="exportBtn">📥 导出Excel</button>
        </form>

        <div class="loading" id="loading">
            ⏳ 正在处理，请稍候...
        </div>

        <div id="previewInfo" class="preview-info" style="display: none;">
            <h3>导出预览</h3>
            <div id="previewContent"></div>
        </div>

        <div id="errorMessage" class="error-message" style="display: none;"></div>
    </div>

    <script>
        // 初始化年份选项
        function initializeYears() {
            const yearSelect = document.getElementById('year');
            const currentYear = new Date().getFullYear();

            for (let year = currentYear; year >= currentYear - 5; year--) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                if (year === currentYear) {
                    option.selected = true;
                }
                yearSelect.appendChild(option);
            }
        }

        // 设置默认月份为当前月份
        function setDefaultMonth() {
            const monthSelect = document.getElementById('month');
            const currentMonth = new Date().getMonth() + 1;
            monthSelect.value = currentMonth;
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // 显示加载状态
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('exportBtn').disabled = show;
            document.getElementById('previewBtn').disabled = show;
        }

        // 获取预览信息
        async function getPreview() {
            const year = document.getElementById('year').value;
            const month = document.getElementById('month').value;

            if (!year || !month) {
                showError('请选择年份和月份');
                return;
            }

            showLoading(true);

            try {
                const response = await fetch(`/api/monthly-budget/export/preview?year=${year}&month=${month}`, {
                    headers: {
                        'Authorization': `Bearer ${document.getElementById('token').value}`
                    }
                });
                const result = await response.json();

                if (result.success) {
                    const previewDiv = document.getElementById('previewInfo');
                    const contentDiv = document.getElementById('previewContent');

                    contentDiv.innerHTML = `
                        <p><strong>时间范围:</strong> ${result.data.year}年${result.data.monthName}</p>
                        <p><strong>预计大小:</strong> ${result.data.estimatedSize}</p>
                        <p><strong>预计时间:</strong> ${result.data.estimatedTime}</p>
                        <p><strong>包含字段:</strong> ${result.data.columns.join(', ')}</p>
                        <p><strong>说明:</strong> ${result.data.description}</p>
                    `;

                    previewDiv.style.display = 'block';
                } else {
                    showError(result.message || '获取预览信息失败');
                }
            } catch (error) {
                console.error('获取预览失败:', error);
                showError('获取预览信息失败，请检查网络连接');
            } finally {
                showLoading(false);
            }
        }

        // 导出Excel
        async function exportExcel() {
            const year = document.getElementById('year').value;
            const month = document.getElementById('month').value;

            if (!year || !month) {
                showError('请选择年份和月份');
                return;
            }

            showLoading(true);

            try {
                const response = await fetch(`/api/monthly-budget/export?year=${year}&month=${month}`, {
                    headers: {
                        'Authorization': `Bearer ${document.getElementById('token').value}`
                    }
                });

                if (response.ok) {
                    // 创建下载链接
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `当月周预算报表_${year}年${month}月_${new Date().toISOString().split('T')[0]}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    // 显示成功消息
                    const previewDiv = document.getElementById('previewInfo');
                    const contentDiv = document.getElementById('previewContent');
                    contentDiv.innerHTML = '<p style="color: #28a745; font-weight: bold;">✅ Excel文件已成功下载！</p>';
                    previewDiv.style.display = 'block';
                } else {
                    const result = await response.json();
                    showError(result.message || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showError('导出失败，请检查网络连接');
            } finally {
                showLoading(false);
            }
        }

        // 事件监听
        document.getElementById('previewBtn').addEventListener('click', getPreview);
        document.getElementById('exportForm').addEventListener('submit', function (e) {
            e.preventDefault();
            exportExcel();
        });

        // 初始化
        initializeYears();
        setDefaultMonth();
    </script>
</body>

</html>