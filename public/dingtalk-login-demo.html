<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉免登录演示</title>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
    <script src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .nav-menu {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.9);
            color: #1890ff;
            font-weight: bold;
        }

        .content {
            padding: 30px;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.loading {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }

        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .btn {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 150px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .user-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }

        .user-info h3 {
            color: #0369a1;
            margin-bottom: 15px;
        }

        .user-info p {
            margin: 8px 0;
            line-height: 1.5;
        }

        .actions {
            text-align: center;
            margin: 20px 0;
        }

        .debug-info {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .step {
            background: #fafafa;
            border-left: 4px solid #1890ff;
            padding: 15px;
            margin: 10px 0;
        }

        .step h4 {
            color: #1890ff;
            margin-bottom: 8px;
        }

        .step-number {
            display: inline-block;
            background: #1890ff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            font-size: 12px;
            margin-right: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔐 钉钉免登录演示</h1>
            <p>完整的钉钉H5微应用免登录流程演示</p>

            <!-- 导航菜单 -->
            <div class="nav-menu">
                <a href="index.html" class="nav-link">🏠 首页</a>
                <a href="dingtalk-design-demo.html" class="nav-link">🎨 完整功能</a>
                <a href="dingtalk-login-demo.html" class="nav-link active">🔐 免登录演示</a>
                <a href="test-api.html" class="nav-link">🧪 API测试</a>
                <a href="signature-test.html" class="nav-link">🔑 签名测试</a>
                <a href="official-login-demo.html" class="nav-link">📱 官方演示</a>
            </div>
        </div>

        <div class="content">
            <div id="status" class="status loading">
                正在初始化钉钉环境...
            </div>

            <div class="step">
                <h4><span class="step-number">1</span>环境检测</h4>
                <p>检测钉钉客户端环境，加载JSAPI</p>
            </div>

            <div class="step">
                <h4><span class="step-number">2</span>获取配置</h4>
                <p>从服务端获取应用配置和JSAPI签名</p>
            </div>

            <div class="step">
                <h4><span class="step-number">3</span>初始化JSAPI</h4>
                <p>使用配置信息初始化钉钉JSAPI</p>
            </div>

            <div class="step">
                <h4><span class="step-number">4</span>免登录认证</h4>
                <p>获取免登授权码并验证用户身份</p>
            </div>

            <div class="actions">
                <button class="btn" onclick="manualLogin()" id="loginBtn">手动登录</button>
                <button class="btn" onclick="getJSAPISignature()">获取JSAPI签名</button>
                <button class="btn" onclick="showDebugInfo()">显示调试信息</button>
            </div>

            <div id="userInfo" style="display: none;"></div>
            <div id="debugInfo" class="debug-info" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 初始化调试工具
        initDingH5RemoteDebug();

        // 全局变量
        let isDingTalkEnv = false;
        let isReady = false;
        let appConfig = null;
        let currentUser = null;
        let debugLogs = [];

        // 添加调试日志
        function addDebugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLogs.push(`[${timestamp}] [${type.toUpperCase()}] ${message}`);
            console.log(`[钉钉免登录] ${message}`);
        }

        // 更新状态显示
        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
            addDebugLog(message, type);
        }

        // 页面加载完成后开始初始化
        window.addEventListener('load', () => {
            addDebugLog('页面加载完成，开始初始化');
            checkDingTalkEnvironment();
        });

        // 检查钉钉环境
        function checkDingTalkEnvironment() {
            addDebugLog('开始检查钉钉环境');

            if (typeof dd !== 'undefined') {
                addDebugLog('检测到钉钉JSAPI');
                isDingTalkEnv = true;
                initDingTalk();
            } else {
                addDebugLog('未检测到钉钉JSAPI，等待2秒后重试');
                setTimeout(() => {
                    if (typeof dd !== 'undefined') {
                        addDebugLog('延迟检测到钉钉JSAPI');
                        isDingTalkEnv = true;
                        initDingTalk();
                    } else {
                        addDebugLog('未检测到钉钉环境，将使用模拟模式', 'warn');
                        updateStatus('error', '❌ 未检测到钉钉环境，请在钉钉客户端中打开');
                        isReady = true;
                    }
                }, 2000);
            }
        }

        // 初始化钉钉
        async function initDingTalk() {
            try {
                addDebugLog('开始初始化钉钉JSAPI');
                updateStatus('loading', '正在获取应用配置...');

                // 1. 获取应用配置
                addDebugLog('正在获取应用配置');
                appConfig = await getAppConfigData();
                addDebugLog(`应用配置获取成功: ${JSON.stringify(appConfig)}`);

                // 2. 获取JSAPI签名
                addDebugLog('正在获取JSAPI签名');
                const signature = await getJSAPISignatureData();
                addDebugLog(`JSAPI签名获取成功: ${JSON.stringify(signature)}`);

                // 3. 配置钉钉JSAPI
                addDebugLog('正在配置钉钉JSAPI');
                const config = {
                    agentId: appConfig.agentId,
                    corpId: appConfig.corpId,
                    timeStamp: signature.timeStamp,
                    nonceStr: signature.nonceStr,
                    signature: signature.signature,
                    jsApiList: appConfig.jsApiList || [
                        'runtime.permission.requestAuthCode',
                        'biz.contact.choose',
                        'device.notification.confirm',
                        'device.notification.alert',
                        'device.notification.prompt',
                        'biz.ding.post'
                    ]
                };

                addDebugLog(`JSAPI配置: ${JSON.stringify(config)}`);
                dd.config(config);

                dd.ready(() => {
                    addDebugLog('钉钉JSAPI初始化成功');
                    isReady = true;
                    updateStatus('success', '✅ 钉钉环境初始化成功！');

                    // 自动尝试免登录
                    setTimeout(() => {
                        addDebugLog('开始自动免登录');
                        autoLogin();
                    }, 1000);
                });

                dd.error((err) => {
                    addDebugLog(`钉钉JSAPI错误: ${JSON.stringify(err)}`, 'error');
                    updateStatus('error', `❌ 钉钉环境初始化失败: ${err.message || err}`);
                });

            } catch (error) {
                addDebugLog(`初始化钉钉失败: ${error.message}`, 'error');
                updateStatus('error', `❌ 初始化失败: ${error.message}`);
            }
        }

        // 获取应用配置数据
        async function getAppConfigData() {
            try {
                const response = await fetch('/api/app/config', {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();

                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || '获取应用配置失败');
                }
            } catch (error) {
                addDebugLog(`获取应用配置失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 清理URL，移除钉钉调试参数
        function cleanUrl(url) {
            try {
                const urlObj = new URL(url);

                // 移除钉钉调试相关的参数
                const debugParams = [
                    'dd_debug_h5',
                    'dd_debug_v1',
                    'dd_debug_os',
                    'dd_debug_v2',
                    'dd_debug_unifiedAppId',
                    'dd_debug_token',
                    'dd_debug_uuid',
                    'dd_debug_pid'
                ];

                debugParams.forEach(param => {
                    urlObj.searchParams.delete(param);
                });

                return urlObj.toString();
            } catch (error) {
                addDebugLog(`URL清理失败: ${error.message}`, 'warn');
                return url;
            }
        }

        // 获取JSAPI签名数据
        async function getJSAPISignatureData() {
            try {
                // 清理当前URL，移除调试参数
                const cleanedUrl = cleanUrl(window.location.href);
                addDebugLog(`原始URL: ${window.location.href}`);
                addDebugLog(`清理后URL: ${cleanedUrl}`);

                const currentUrl = encodeURIComponent(cleanedUrl);
                const response = await fetch(`/api/auth/jsapi-signature?url=${currentUrl}`, {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();

                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || '获取JSAPI签名失败');
                }
            } catch (error) {
                addDebugLog(`获取JSAPI签名失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 自动免登录
        async function autoLogin() {
            if (!isDingTalkEnv || !isReady) {
                addDebugLog('环境未准备就绪，跳过自动登录', 'warn');
                return;
            }

            try {
                addDebugLog('开始自动免登录');
                await authenticateUser();
            } catch (error) {
                addDebugLog(`自动登录失败: ${error.message}`, 'error');
                updateStatus('error', '自动登录失败，请手动点击登录按钮');
            }
        }

        // 手动登录
        async function manualLogin() {
            if (!isDingTalkEnv) {
                updateStatus('error', '❌ 需要在钉钉环境中使用');
                return;
            }

            if (!isReady) {
                updateStatus('error', '❌ 钉钉环境尚未准备就绪，请稍后再试');
                return;
            }

            await authenticateUser();
        }

        // 用户认证
        async function authenticateUser() {
            try {
                addDebugLog('开始用户认证流程');
                updateStatus('loading', '正在获取免登授权码...');

                dd.runtime.permission.requestAuthCode({
                    redirection: "none",
                    corpId: appConfig.corpId,
                    onSuccess: async (result) => {
                        try {
                            addDebugLog(`获取到免登码: ${result.code}`);
                            updateStatus('loading', '正在验证用户身份...');

                            const response = await fetch('/api/auth/user-info', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ authCode: result.code })
                            });

                            const userInfo = await response.json();

                            if (userInfo && userInfo.success) {
                                currentUser = userInfo.data;
                                addDebugLog(`用户认证成功: ${JSON.stringify(currentUser)}`);
                                updateStatus('success', `✅ 登录成功！欢迎 ${currentUser.name || currentUser.userid}`);

                                // 显示用户信息
                                showUserInfo(currentUser);
                            } else {
                                throw new Error(userInfo.message || '用户认证失败');
                            }
                        } catch (error) {
                            addDebugLog(`用户认证失败: ${error.message}`, 'error');
                            updateStatus('error', `❌ 用户认证失败: ${error.message}`);
                        }
                    },
                    onFail: (err) => {
                        addDebugLog(`获取免登码失败: ${JSON.stringify(err)}`, 'error');
                        updateStatus('error', `❌ 获取免登码失败: ${err.message || JSON.stringify(err)}`);
                    }
                });
            } catch (error) {
                addDebugLog(`免登录过程出错: ${error.message}`, 'error');
                updateStatus('error', `❌ 免登录过程出错: ${error.message}`);
            }
        }

        // 显示用户信息
        function showUserInfo(userInfo) {
            const userInfoHtml = `
                <div class="user-info">
                    <h3>👤 当前用户信息</h3>
                    <p><strong>用户ID:</strong> ${userInfo.userid || 'N/A'}</p>
                    <p><strong>姓名:</strong> ${userInfo.name || 'N/A'}</p>
                    <p><strong>手机号:</strong> ${userInfo.mobile || 'N/A'}</p>
                    <p><strong>邮箱:</strong> ${userInfo.email || 'N/A'}</p>
                    <p><strong>职位:</strong> ${userInfo.position || 'N/A'}</p>
                    <p><strong>工号:</strong> ${userInfo.job_number || 'N/A'}</p>
                    <p><strong>部门:</strong> ${userInfo.department ? userInfo.department.join(', ') : 'N/A'}</p>
                </div>
            `;

            document.getElementById('userInfo').innerHTML = userInfoHtml;
            document.getElementById('userInfo').style.display = 'block';
        }

        // 获取JSAPI签名（演示用）
        async function getJSAPISignature() {
            try {
                updateStatus('loading', '正在获取JSAPI签名...');
                const signature = await getJSAPISignatureData();
                updateStatus('success', '✅ JSAPI签名获取成功');

                const signatureInfo = `
                    <div class="user-info">
                        <h3>🔑 JSAPI签名信息</h3>
                        <p><strong>AgentId:</strong> ${signature.agentId}</p>
                        <p><strong>CorpId:</strong> ${signature.corpId}</p>
                        <p><strong>TimeStamp:</strong> ${signature.timeStamp}</p>
                        <p><strong>NonceStr:</strong> ${signature.nonceStr}</p>
                        <p><strong>Signature:</strong> ${signature.signature}</p>
                        <p><strong>当前URL:</strong> ${cleanUrl(window.location.href)}</p>
                    </div>
                `;

                document.getElementById('userInfo').innerHTML = signatureInfo;
                document.getElementById('userInfo').style.display = 'block';
            } catch (error) {
                updateStatus('error', `❌ 获取JSAPI签名失败: ${error.message}`);
            }
        }

        // 显示调试信息
        function showDebugInfo() {
            const debugEl = document.getElementById('debugInfo');
            if (debugEl.style.display === 'none') {
                debugEl.innerHTML = debugLogs.join('\n');
                debugEl.style.display = 'block';
            } else {
                debugEl.style.display = 'none';
            }
        }
    </script>
</body>

</html>