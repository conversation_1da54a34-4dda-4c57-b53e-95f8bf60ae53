<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口文档 - 项目管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav-menu {
            background: #fff;
            padding: 20px 30px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: #f8f9fa;
            color: #495057;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid #dee2e6;
        }

        .nav-link:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: #667eea;
            color: white;
            font-weight: bold;
        }

        .content {
            display: flex;
            min-height: calc(100vh - 200px);
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e1e8ed;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
            font-weight: 600;
        }

        .sidebar ul {
            list-style: none;
            margin-bottom: 25px;
        }

        .sidebar li {
            margin-bottom: 8px;
        }

        .sidebar a {
            color: #6c757d;
            text-decoration: none;
            padding: 8px 12px;
            display: block;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sidebar a:hover {
            background: #e9ecef;
            color: #495057;
        }

        .sidebar a.active {
            background: #667eea;
            color: white;
        }

        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .api-section {
            display: none;
            margin-bottom: 40px;
        }

        .api-section.active {
            display: block;
        }

        .api-item {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .api-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e8ed;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .api-header:hover {
            background: #e9ecef;
        }

        .api-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .method {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .method.get {
            background: #28a745;
        }

        .method.post {
            background: #007bff;
        }

        .method.put {
            background: #ffc107;
            color: #212529;
        }

        .method.delete {
            background: #dc3545;
        }

        .api-path {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #495057;
        }

        .api-body {
            padding: 20px;
            display: none;
        }

        .api-body.active {
            display: block;
        }

        .section-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #495057;
            margin: 20px 0 10px 0;
            padding-bottom: 5px;
            border-bottom: 2px solid #e1e8ed;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .test-form {
            background: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .response-area {
            margin-top: 15px;
        }

        .response-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
        }

        .response-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.active {
            transform: rotate(90deg);
        }

        .description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .parameter-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        .parameter-table th,
        .parameter-table td {
            border: 1px solid #e1e8ed;
            padding: 8px 12px;
            text-align: left;
        }

        .parameter-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .required {
            color: #dc3545;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e1e8ed;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>📚 API接口文档</h1>
            <p>项目管理系统 RESTful API 文档和测试工具</p>
        </div>

        <!-- 导航菜单 -->
        <div class="nav-menu">
            <a href="index.html" class="nav-link">🏠 首页</a>
            <a href="dingtalk-design-demo.html" class="nav-link">🎨 完整功能</a>
            <a href="dingtalk-login-demo.html" class="nav-link">🔐 免登录演示</a>
            <a href="test-api.html" class="nav-link">🧪 API测试</a>
            <a href="signature-test.html" class="nav-link">🔑 签名测试</a>
            <a href="official-login-demo.html" class="nav-link">📱 官方演示</a>
            <a href="project-management.html" class="nav-link">📊 项目管理</a>
            <a href="api-docs.html" class="nav-link active">📚 API文档</a>
        </div>

        <div class="content">
            <div class="sidebar">
                <h3>📋 接口分类</h3>
                <ul>
                    <li><a href="#overview" class="sidebar-link active">概述</a></li>
                    <li><a href="#brands" class="sidebar-link">品牌管理</a></li>
                    <li><a href="#projects" class="sidebar-link">项目管理</a></li>
                    <li><a href="#stats" class="sidebar-link">统计分析</a></li>
                    <li><a href="#upload" class="sidebar-link">文件上传</a></li>
                    <li><a href="#errors" class="sidebar-link">错误处理</a></li>
                    <li><a href="#examples" class="sidebar-link">代码示例</a></li>
                </ul>

                <h3>🔗 快速链接</h3>
                <ul>
                    <li><a href="/api/projects/stats" target="_blank">统计数据</a></li>
                    <li><a href="/api/brands" target="_blank">品牌列表</a></li>
                    <li><a href="/api/projects" target="_blank">项目列表</a></li>
                    <li><a href="project-management.html" target="_blank">项目管理页面</a></li>
                </ul>
            </div>

            <div class="main-content">
                <!-- 概述 -->
                <div id="overview" class="api-section active">
                    <h2>📋 API概述</h2>
                    <div class="description">
                        项目管理系统提供完整的RESTful API，支持项目管理、品牌管理、文件上传和统计分析等功能。
                    </div>

                    <div class="section-title">基础信息</div>
                    <div class="code-block">
                        <pre>Base URL: http://localhost:3000/api
Content-Type: application/json
字符编码: UTF-8</pre>
                    </div>

                    <div class="section-title">认证方式</div>
                    <div class="description">
                        目前API暂未启用认证，后续可集成钉钉免登录或JWT认证。
                    </div>

                    <div class="section-title">响应格式</div>
                    <div class="code-block">
                        <pre>{
  "success": true,
  "data": { ... },
  "message": "操作成功"
}</pre>
                    </div>

                    <div class="section-title">分页格式</div>
                    <div class="code-block">
                        <pre>{
  "success": true,
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}</pre>
                    </div>
                </div>

                <!-- 品牌管理 -->
                <div id="brands" class="api-section">
                    <h2>🏷️ 品牌管理API</h2>

                    <!-- 获取品牌列表 -->
                    <div class="api-item">
                        <div class="api-header" onclick="toggleApiBody(this)">
                            <div class="api-title">
                                <span class="method get">GET</span>
                                <span class="api-path">/brands</span>
                                <span>获取品牌列表</span>
                            </div>
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="api-body">
                            <div class="description">
                                获取品牌列表，支持分页、搜索和状态筛选。
                            </div>

                            <div class="section-title">请求参数</div>
                            <table class="parameter-table">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>page</td>
                                        <td>number</td>
                                        <td>否</td>
                                        <td>页码，默认1</td>
                                    </tr>
                                    <tr>
                                        <td>pageSize</td>
                                        <td>number</td>
                                        <td>否</td>
                                        <td>每页数量，默认50</td>
                                    </tr>
                                    <tr>
                                        <td>status</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>品牌状态：active, inactive</td>
                                    </tr>
                                    <tr>
                                        <td>keyword</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>搜索关键字</td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="test-form">
                                <h4>🧪 接口测试</h4>
                                <div class="form-group">
                                    <label>页码</label>
                                    <input type="number" class="form-control" id="brands-page" value="1" min="1">
                                </div>
                                <div class="form-group">
                                    <label>每页数量</label>
                                    <input type="number" class="form-control" id="brands-pageSize" value="10" min="1"
                                        max="100">
                                </div>
                                <div class="form-group">
                                    <label>状态</label>
                                    <select class="form-control" id="brands-status">
                                        <option value="">全部</option>
                                        <option value="active">启用</option>
                                        <option value="inactive">禁用</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>搜索关键字</label>
                                    <input type="text" class="form-control" id="brands-keyword" placeholder="输入品牌名称">
                                </div>
                                <button class="btn" onclick="testGetBrands()">发送请求</button>
                                <div id="brands-response" class="response-area"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 创建品牌 -->
                    <div class="api-item">
                        <div class="api-header" onclick="toggleApiBody(this)">
                            <div class="api-title">
                                <span class="method post">POST</span>
                                <span class="api-path">/brands</span>
                                <span>创建品牌</span>
                            </div>
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="api-body">
                            <div class="description">
                                创建新的品牌。
                            </div>

                            <div class="section-title">请求体</div>
                            <div class="code-block">
                                <pre>{
  "name": "品牌名称",
  "description": "品牌描述",
  "logo": "https://example.com/logo.png"
}</pre>
                            </div>

                            <div class="test-form">
                                <h4>🧪 接口测试</h4>
                                <div class="form-group">
                                    <label>品牌名称 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="create-brand-name" placeholder="输入品牌名称"
                                        required>
                                </div>
                                <div class="form-group">
                                    <label>品牌描述</label>
                                    <textarea class="form-control" id="create-brand-description"
                                        placeholder="输入品牌描述"></textarea>
                                </div>
                                <div class="form-group">
                                    <label>Logo URL</label>
                                    <input type="url" class="form-control" id="create-brand-logo"
                                        placeholder="https://example.com/logo.png">
                                </div>
                                <button class="btn" onclick="testCreateBrand()">创建品牌</button>
                                <div id="create-brand-response" class="response-area"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目管理 -->
                <div id="projects" class="api-section">
                    <h2>📊 项目管理API</h2>

                    <!-- 获取项目列表 -->
                    <div class="api-item">
                        <div class="api-header" onclick="toggleApiBody(this)">
                            <div class="api-title">
                                <span class="method get">GET</span>
                                <span class="api-path">/projects</span>
                                <span>获取项目列表</span>
                            </div>
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="api-body">
                            <div class="description">
                                获取项目列表，支持多种筛选条件和排序方式。
                            </div>

                            <div class="section-title">请求参数</div>
                            <table class="parameter-table">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>page</td>
                                        <td>number</td>
                                        <td>否</td>
                                        <td>页码，默认1</td>
                                    </tr>
                                    <tr>
                                        <td>pageSize</td>
                                        <td>number</td>
                                        <td>否</td>
                                        <td>每页数量，默认20</td>
                                    </tr>
                                    <tr>
                                        <td>status</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>项目状态：draft, active, completed, cancelled</td>
                                    </tr>
                                    <tr>
                                        <td>brandId</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>品牌ID</td>
                                    </tr>
                                    <tr>
                                        <td>contractType</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>合同类型：annual_frame, quarterly_frame, single, po_order, jing_task</td>
                                    </tr>
                                    <tr>
                                        <td>keyword</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>项目名称关键字</td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="test-form">
                                <h4>🧪 接口测试</h4>
                                <div class="form-group">
                                    <label>页码</label>
                                    <input type="number" class="form-control" id="projects-page" value="1" min="1">
                                </div>
                                <div class="form-group">
                                    <label>每页数量</label>
                                    <input type="number" class="form-control" id="projects-pageSize" value="10" min="1"
                                        max="100">
                                </div>
                                <div class="form-group">
                                    <label>项目状态</label>
                                    <select class="form-control" id="projects-status">
                                        <option value="">全部</option>
                                        <option value="draft">草稿</option>
                                        <option value="active">进行中</option>
                                        <option value="completed">已完成</option>
                                        <option value="cancelled">已取消</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>项目名称</label>
                                    <input type="text" class="form-control" id="projects-keyword"
                                        placeholder="输入项目名称关键字">
                                </div>
                                <button class="btn" onclick="testGetProjects()">发送请求</button>
                                <div id="projects-response" class="response-area"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 创建项目 -->
                    <div class="api-item">
                        <div class="api-header" onclick="toggleApiBody(this)">
                            <div class="api-title">
                                <span class="method post">POST</span>
                                <span class="api-path">/projects</span>
                                <span>创建项目</span>
                            </div>
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="api-body">
                            <div class="description">
                                创建新的项目，包含完整的预算、成本和人员信息。
                            </div>

                            <div class="section-title">请求体结构</div>
                            <div class="code-block">
                                <pre>{
  "documentType": "project_initiation",
  "brandId": "brand-001",
  "projectName": "春节营销活动",
  "period": {
    "startDate": "2024-02-01",
    "endDate": "2024-02-29"
  },
  "budget": {
    "planningBudget": 1000000.00,
    "influencerBudget": 400000.00,
    "adBudget": 300000.00,
    "otherBudget": 100000.00
  },
  "cost": {
    "influencerCost": 350000.00,
    "adCost": 280000.00,
    "otherCost": 80000.00,
    "estimatedInfluencerRebate": 20000.00
  },
  "executorPM": "user-001",
  "contentMediaIds": ["user-002", "user-003"],
  "contractType": "annual_frame",
  "settlementRules": "按月结算规则",
  "kpi": "目标KPI要求"
}</pre>
                            </div>

                            <div class="section-title">字段说明</div>
                            <table class="parameter-table">
                                <thead>
                                    <tr>
                                        <th>字段名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>documentType</td>
                                        <td>string</td>
                                        <td class="required">是</td>
                                        <td>单据类型，固定值：project_initiation</td>
                                    </tr>
                                    <tr>
                                        <td>brandId</td>
                                        <td>string</td>
                                        <td class="required">是</td>
                                        <td>品牌ID</td>
                                    </tr>
                                    <tr>
                                        <td>projectName</td>
                                        <td>string</td>
                                        <td class="required">是</td>
                                        <td>项目名称</td>
                                    </tr>
                                    <tr>
                                        <td>period.startDate</td>
                                        <td>string</td>
                                        <td class="required">是</td>
                                        <td>开始日期 (YYYY-MM-DD)</td>
                                    </tr>
                                    <tr>
                                        <td>period.endDate</td>
                                        <td>string</td>
                                        <td class="required">是</td>
                                        <td>结束日期 (YYYY-MM-DD)</td>
                                    </tr>
                                    <tr>
                                        <td>budget.planningBudget</td>
                                        <td>number</td>
                                        <td class="required">是</td>
                                        <td>项目规划预算</td>
                                    </tr>
                                    <tr>
                                        <td>executorPM</td>
                                        <td>string</td>
                                        <td class="required">是</td>
                                        <td>执行PM用户ID</td>
                                    </tr>
                                    <tr>
                                        <td>contentMediaIds</td>
                                        <td>array</td>
                                        <td class="required">是</td>
                                        <td>内容媒介用户ID列表</td>
                                    </tr>
                                    <tr>
                                        <td>contractType</td>
                                        <td>string</td>
                                        <td class="required">是</td>
                                        <td>合同类型</td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="section-title">响应示例</div>
                            <div class="code-block">
                                <pre>{
  "success": true,
  "data": {
    "id": "project-001",
    "projectName": "春节营销活动",
    "profit": {
      "profit": 310000.00,
      "grossMargin": 31.00
    },
    "status": "draft",
    "createdAt": "2024-01-15T08:00:00.000Z"
  },
  "message": "项目创建成功"
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计分析 -->
                <div id="stats" class="api-section">
                    <h2>📈 统计分析API</h2>

                    <!-- 获取项目统计 -->
                    <div class="api-item">
                        <div class="api-header" onclick="toggleApiBody(this)">
                            <div class="api-title">
                                <span class="method get">GET</span>
                                <span class="api-path">/projects/stats</span>
                                <span>获取项目统计</span>
                            </div>
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="api-body">
                            <div class="description">
                                获取项目的统计数据，包括总数、预算、利润等信息。
                            </div>

                            <div class="test-form">
                                <h4>🧪 接口测试</h4>
                                <button class="btn" onclick="testGetStats()">获取统计数据</button>
                                <div id="stats-response" class="response-area"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件上传 -->
                <div id="upload" class="api-section">
                    <h2>📎 文件上传API</h2>

                    <!-- 上传文件 -->
                    <div class="api-item">
                        <div class="api-header" onclick="toggleApiBody(this)">
                            <div class="api-title">
                                <span class="method post">POST</span>
                                <span class="api-path">/upload</span>
                                <span>上传文件</span>
                            </div>
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="api-body">
                            <div class="description">
                                上传项目相关的文件，支持多种文件格式。
                            </div>

                            <div class="section-title">请求类型</div>
                            <div class="code-block">
                                <pre>Content-Type: multipart/form-data</pre>
                            </div>

                            <div class="section-title">请求参数</div>
                            <table class="parameter-table">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>file</td>
                                        <td>File</td>
                                        <td class="required">是</td>
                                        <td>要上传的文件</td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="section-title">响应示例</div>
                            <div class="code-block">
                                <pre>{
  "success": true,
  "data": {
    "id": "file-001",
    "filename": "contract_20240115.pdf",
    "originalName": "合同文件.pdf",
    "size": 1024000,
    "mimeType": "application/pdf",
    "url": "/uploads/contract_20240115.pdf"
  },
  "message": "文件上传成功"
}</pre>
                            </div>

                            <div class="test-form">
                                <h4>🧪 接口测试</h4>
                                <div class="form-group">
                                    <label>选择文件 <span class="required">*</span></label>
                                    <input type="file" class="form-control" id="upload-file"
                                        accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg">
                                </div>
                                <button class="btn" onclick="testUploadFile()">上传文件</button>
                                <div id="upload-response" class="response-area"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 错误处理 -->
                <div id="errors" class="api-section">
                    <h2>❌ 错误处理</h2>

                    <div class="section-title">统一错误响应格式</div>
                    <div class="code-block">
                        <pre>{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息（可选）"
}</pre>
                    </div>

                    <div class="section-title">常见错误码</div>
                    <table class="parameter-table">
                        <thead>
                            <tr>
                                <th>HTTP状态码</th>
                                <th>错误码</th>
                                <th>描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>400</td>
                                <td>INVALID_REQUEST</td>
                                <td>请求参数无效</td>
                            </tr>
                            <tr>
                                <td>401</td>
                                <td>UNAUTHORIZED</td>
                                <td>未授权访问</td>
                            </tr>
                            <tr>
                                <td>403</td>
                                <td>FORBIDDEN</td>
                                <td>权限不足</td>
                            </tr>
                            <tr>
                                <td>404</td>
                                <td>NOT_FOUND</td>
                                <td>资源不存在</td>
                            </tr>
                            <tr>
                                <td>409</td>
                                <td>CONFLICT</td>
                                <td>资源冲突（如重复创建）</td>
                            </tr>
                            <tr>
                                <td>422</td>
                                <td>VALIDATION_ERROR</td>
                                <td>数据验证失败</td>
                            </tr>
                            <tr>
                                <td>500</td>
                                <td>INTERNAL_ERROR</td>
                                <td>服务器内部错误</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="section-title">错误示例</div>
                    <div class="description">验证错误：</div>
                    <div class="code-block">
                        <pre>{
  "success": false,
  "message": "数据验证失败",
  "code": "VALIDATION_ERROR",
  "details": {
    "projectName": "项目名称不能为空",
    "budget.planningBudget": "规划预算必须大于0"
  }
}</pre>
                    </div>

                    <div class="description">资源不存在：</div>
                    <div class="code-block">
                        <pre>{
  "success": false,
  "message": "项目不存在",
  "code": "NOT_FOUND"
}</pre>
                    </div>
                </div>

                <!-- 代码示例 -->
                <div id="examples" class="api-section">
                    <h2>💻 代码示例</h2>

                    <div class="section-title">JavaScript/Fetch</div>
                    <div class="code-block">
                        <pre>// 获取项目列表
async function getProjects() {
  try {
    const response = await fetch('/api/projects?page=1&pageSize=20');
    const data = await response.json();

    if (data.success) {
      console.log('项目列表:', data.data.projects);
    } else {
      console.error('获取失败:', data.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 创建品牌
async function createBrand(brandData) {
  try {
    const response = await fetch('/api/brands', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(brandData)
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('创建品牌失败:', error);
    throw error;
  }
}

// 文件上传
async function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
}</pre>
                    </div>

                    <div class="section-title">jQuery</div>
                    <div class="code-block">
                        <pre>// 获取项目统计
$.ajax({
  url: '/api/projects/stats',
  method: 'GET',
  success: function(data) {
    if (data.success) {
      console.log('统计数据:', data.data);
    }
  },
  error: function(xhr, status, error) {
    console.error('请求失败:', error);
  }
});

// 创建项目
$.ajax({
  url: '/api/projects',
  method: 'POST',
  contentType: 'application/json',
  data: JSON.stringify({
    documentType: 'project_initiation',
    brandId: 'brand-001',
    projectName: '新项目',
    // ... 其他字段
  }),
  success: function(data) {
    console.log('项目创建成功:', data.data);
  }
});</pre>
                    </div>

                    <div class="section-title">cURL</div>
                    <div class="code-block">
                        <pre># 获取品牌列表
curl -X GET "http://localhost:3000/api/brands?page=1&pageSize=10" \
  -H "Content-Type: application/json"

# 创建品牌
curl -X POST "http://localhost:3000/api/brands" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试品牌",
    "description": "这是一个测试品牌"
  }'

# 上传文件
curl -X POST "http://localhost:3000/api/upload" \
  -F "file=@/path/to/your/file.pdf"

# 获取项目统计
curl -X GET "http://localhost:3000/api/projects/stats" \
  -H "Content-Type: application/json"</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换API详情显示
        function toggleApiBody(header) {
            const body = header.nextElementSibling;
            const icon = header.querySelector('.toggle-icon');

            body.classList.toggle('active');
            icon.classList.toggle('active');
        }

        // 侧边栏导航
        document.querySelectorAll('.sidebar-link').forEach(link => {
            link.addEventListener('click', function (e) {
                e.preventDefault();

                // 更新侧边栏激活状态
                document.querySelectorAll('.sidebar-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');

                // 显示对应的内容区域
                const targetId = this.getAttribute('href').substring(1);
                document.querySelectorAll('.api-section').forEach(section => {
                    section.classList.remove('active');
                });
                document.getElementById(targetId).classList.add('active');
            });
        });

        // API测试函数
        async function testGetBrands() {
            const page = document.getElementById('brands-page').value;
            const pageSize = document.getElementById('brands-pageSize').value;
            const status = document.getElementById('brands-status').value;
            const keyword = document.getElementById('brands-keyword').value;

            const params = new URLSearchParams();
            if (page) params.append('page', page);
            if (pageSize) params.append('pageSize', pageSize);
            if (status) params.append('status', status);
            if (keyword) params.append('keyword', keyword);

            try {
                const response = await fetch(`/api/brands?${params}`);
                const data = await response.json();

                document.getElementById('brands-response').innerHTML = `
                    <div class="response-success">
                        <strong>响应成功:</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('brands-response').innerHTML = `
                    <div class="response-error">
                        <strong>请求失败:</strong> ${error.message}
                    </div>
                `;
            }
        }

        async function testCreateBrand() {
            const name = document.getElementById('create-brand-name').value;
            const description = document.getElementById('create-brand-description').value;
            const logo = document.getElementById('create-brand-logo').value;

            if (!name) {
                alert('请输入品牌名称');
                return;
            }

            const data = { name };
            if (description) data.description = description;
            if (logo) data.logo = logo;

            try {
                const response = await fetch('/api/brands', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('create-brand-response').innerHTML = `
                        <div class="response-success">
                            <strong>创建成功:</strong>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                    // 清空表单
                    document.getElementById('create-brand-name').value = '';
                    document.getElementById('create-brand-description').value = '';
                    document.getElementById('create-brand-logo').value = '';
                } else {
                    document.getElementById('create-brand-response').innerHTML = `
                        <div class="response-error">
                            <strong>创建失败:</strong> ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('create-brand-response').innerHTML = `
                    <div class="response-error">
                        <strong>请求失败:</strong> ${error.message}
                    </div>
                `;
            }
        }

        async function testGetProjects() {
            const page = document.getElementById('projects-page').value;
            const pageSize = document.getElementById('projects-pageSize').value;
            const status = document.getElementById('projects-status').value;
            const brandId = document.getElementById('projects-brandId').value;
            const keyword = document.getElementById('projects-keyword').value;

            const params = new URLSearchParams();
            if (page) params.append('page', page);
            if (pageSize) params.append('pageSize', pageSize);
            if (status) params.append('status', status);
            if (brandId) params.append('brandId', brandId);
            if (keyword) params.append('keyword', keyword);

            try {
                const response = await fetch(`/api/projects?${params}`);
                const data = await response.json();

                document.getElementById('projects-response').innerHTML = `
                    <div class="response-success">
                        <strong>响应成功:</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('projects-response').innerHTML = `
                    <div class="response-error">
                        <strong>请求失败:</strong> ${error.message}
                    </div>
                `;
            }
        }

        async function testGetStats() {
            try {
                const response = await fetch('/api/projects/stats');
                const data = await response.json();

                document.getElementById('stats-response').innerHTML = `
                    <div class="response-success">
                        <strong>统计数据:</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('stats-response').innerHTML = `
                    <div class="response-error">
                        <strong>请求失败:</strong> ${error.message}
                    </div>
                `;
            }
        }

        async function testUploadFile() {
            const fileInput = document.getElementById('upload-file');
            const file = fileInput.files[0];

            if (!file) {
                alert('请选择要上传的文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                document.getElementById('upload-response').innerHTML = `
                    <div class="response-success">
                        <strong>正在上传...</strong> ${file.name} (${(file.size / 1024).toFixed(1)} KB)
                    </div>
                `;

                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('upload-response').innerHTML = `
                        <div class="response-success">
                            <strong>上传成功:</strong>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    // 清空文件选择
                    fileInput.value = '';
                } else {
                    document.getElementById('upload-response').innerHTML = `
                        <div class="response-error">
                            <strong>上传失败:</strong> ${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('upload-response').innerHTML = `
                    <div class="response-error">
                        <strong>上传失败:</strong> ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>

</html>