<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉官方免登录演示</title>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
    <script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            color: white;
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            margin: 0;
        }

        .nav-menu {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.9);
            color: #1890ff;
            font-weight: bold;
        }

        .content {
            padding: 30px;
        }

        .step-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #1890ff;
        }

        .step-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 10px;
        }

        .step-content {
            color: #666;
            line-height: 1.6;
        }

        .status-card {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e8e8e8;
        }

        .status-card.loading {
            background: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
        }

        .status-card.success {
            background: #f6ffed;
            border-color: #b7eb8f;
            color: #52c41a;
        }

        .status-card.error {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #ff4d4f;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #40a9ff;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
            transform: none;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 20px;
        }

        .info-card h3 {
            margin-top: 0;
            color: #1890ff;
        }

        .code-block {
            background: #f5f5f5;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .debug-log {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-info {
            color: #4fc3f7;
        }

        .log-success {
            color: #81c784;
        }

        .log-warning {
            color: #ffb74d;
        }

        .log-error {
            color: #e57373;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .nav-menu {
                flex-direction: column;
                gap: 5px;
            }

            .content {
                padding: 20px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>📱 钉钉官方免登录演示</h1>
            <p>基于官方文档的完整免登录实现</p>

            <!-- 导航菜单 -->
            <div class="nav-menu">
                <a href="index.html" class="nav-link">🏠 首页</a>
                <a href="dingtalk-design-demo.html" class="nav-link">🎨 完整功能</a>
                <a href="dingtalk-login-demo.html" class="nav-link">🔐 免登录演示</a>
                <a href="test-api.html" class="nav-link">🧪 API测试</a>
                <a href="signature-test.html" class="nav-link">🔑 签名测试</a>
                <a href="official-login-demo.html" class="nav-link active">📱 官方演示</a>
            </div>
        </div>

        <div class="content">
            <!-- 环境检查 -->
            <div class="step-card">
                <div class="step-title">🔍 环境检查</div>
                <div class="step-content">
                    <div id="envCheck">正在检查运行环境...</div>
                </div>
            </div>

            <!-- 当前状态 -->
            <div id="currentStatus" class="status-card loading">
                🔄 正在初始化钉钉环境...
            </div>

            <!-- 操作按钮 -->
            <div style="text-align: center; margin: 20px 0;">
                <button id="startLoginBtn" class="btn" onclick="startOfficialLogin()" disabled>
                    🚀 开始官方免登录流程
                </button>
                <button id="checkApiBtn" class="btn" onclick="checkApiStatus()">
                    🔍 检查API状态
                </button>
                <button id="clearLogBtn" class="btn" onclick="clearDebugLog()">
                    🗑️ 清空日志
                </button>
            </div>

            <!-- 故障排除提示 -->
            <div class="info-card" style="background: #fff7e6; border-color: #ffd591;">
                <h3 style="color: #fa8c16;">🚨 遇到问题？</h3>
                <div class="step-content">
                    <p><strong>如果遇到 "jsapi ticket 读取失败" 等错误，请尝试：</strong></p>
                    <ol>
                        <li><strong>运行诊断工具</strong>：<code>npm run diagnose</code></li>
                        <li><strong>检查环境配置</strong>：确认 .env 文件中的钉钉应用配置</li>
                        <li><strong>验证应用状态</strong>：在钉钉开放平台检查应用是否启用</li>
                        <li><strong>测试网络连接</strong>：确认服务器可以访问钉钉API</li>
                    </ol>
                    <p>📚 详细排查指南请参考：<a href="../docs/jsapi-troubleshooting.md" target="_blank">JSAPI问题排查指南</a></p>
                </div>
            </div>

            <!-- 信息展示 -->
            <div class="info-grid">
                <!-- 流程步骤 -->
                <div class="info-card">
                    <h3>📋 免登录流程</h3>
                    <div id="processSteps">
                        <div class="step-content">
                            1. 环境检查<br>
                            2. 获取应用配置<br>
                            3. 获取JSAPI签名<br>
                            4. 配置钉钉JSAPI<br>
                            5. 获取免登授权码<br>
                            6. 获取用户信息<br>
                            7. 显示登录结果
                        </div>
                    </div>
                </div>

                <!-- 用户信息 -->
                <div class="info-card">
                    <h3>👤 用户信息</h3>
                    <div id="userInfo">
                        <div class="step-content">等待获取用户信息...</div>
                    </div>
                </div>
            </div>

            <!-- 技术细节 -->
            <div class="info-card">
                <h3>🔧 技术细节</h3>
                <div id="technicalDetails">
                    <div class="code-block" id="configDetails">等待配置信息...</div>
                </div>
            </div>

            <!-- 调试日志 -->
            <div class="info-card">
                <h3>📝 调试日志</h3>
                <div class="debug-log" id="debugLog">
                    <div class="log-entry log-info">[INFO] 页面加载完成，开始初始化...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isReady = false;
        let isDingTalkEnv = false;
        let appConfig = null;

        // 日志记录
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML =
                '<div class="log-entry log-info">[INFO] 日志已清空</div>';
        }

        // 更新状态
        function updateStatus(message, type = 'loading') {
            const statusEl = document.getElementById('currentStatus');
            statusEl.className = `status-card ${type}`;
            statusEl.textContent = message;
            addLog(message, type === 'loading' ? 'info' : type);
        }

        // 环境检查
        function checkEnvironment() {
            addLog('开始环境检查...');

            const checks = {
                protocol: location.protocol === 'https:' ? '✅' : '❌',
                userAgent: /DingTalk/i.test(navigator.userAgent) ? '✅' : '❌',
                ddObject: typeof dd !== 'undefined' ? '✅' : '❌',
                device: getDeviceType()
            };

            isDingTalkEnv = checks.userAgent === '✅' || checks.ddObject === '✅';

            const envCheckEl = document.getElementById('envCheck');
            envCheckEl.innerHTML = `
                <div>HTTPS协议: ${checks.protocol} ${location.protocol}</div>
                <div>钉钉环境: ${checks.userAgent} ${navigator.userAgent.includes('DingTalk') ? 'DingTalk客户端' : '非钉钉环境'}</div>
                <div>DD对象: ${checks.ddObject} ${typeof dd !== 'undefined' ? 'JSAPI已加载' : 'JSAPI未加载'}</div>
                <div>设备类型: 📱 ${checks.device}</div>
            `;

            addLog(`环境检查完成: HTTPS=${checks.protocol}, DingTalk=${checks.userAgent}, DD=${checks.ddObject}, Device=${checks.device}`);

            return checks;
        }

        // 获取设备类型
        function getDeviceType() {
            const ua = navigator.userAgent;
            if (/Android/i.test(ua)) return 'Android';
            if (/iPhone|iPad/i.test(ua)) return 'iOS';
            return 'PC';
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            addLog('页面加载完成，开始初始化...');

            setTimeout(() => {
                const envChecks = checkEnvironment();

                if (isDingTalkEnv) {
                    initializeDingTalk();
                } else {
                    updateStatus('⚠️ 当前不在钉钉环境中，将使用模拟模式', 'warning');
                    document.getElementById('startLoginBtn').disabled = false;
                    showMockMode();
                }
            }, 1000);
        });

        // 初始化钉钉环境
        function initializeDingTalk() {
            addLog('开始初始化钉钉JSAPI...');
            updateStatus('🔄 正在初始化钉钉JSAPI...');

            if (typeof dd === 'undefined') {
                addLog('DD对象未找到，等待JSAPI加载...', 'warning');
                let retryCount = 0;
                const maxRetries = 10;

                const checkDD = setInterval(() => {
                    retryCount++;
                    if (typeof dd !== 'undefined') {
                        clearInterval(checkDD);
                        addLog('DD对象加载成功');
                        setupDingTalk();
                    } else if (retryCount >= maxRetries) {
                        clearInterval(checkDD);
                        addLog('DD对象加载失败，切换到模拟模式', 'error');
                        updateStatus('❌ JSAPI加载失败，使用模拟模式', 'error');
                        document.getElementById('startLoginBtn').disabled = false;
                        showMockMode();
                    }
                }, 500);
            } else {
                setupDingTalk();
            }
        }

        // 设置钉钉环境
        function setupDingTalk() {
            addLog('设置钉钉环境...');

            dd.ready(() => {
                addLog('钉钉JSAPI初始化成功');
                isReady = true;
                updateStatus('✅ 钉钉环境初始化成功！', 'success');
                document.getElementById('startLoginBtn').disabled = false;
            });

            dd.error((err) => {
                addLog(`钉钉JSAPI初始化失败: ${JSON.stringify(err)}`, 'error');
                updateStatus('❌ 钉钉环境初始化失败', 'error');
                showMockMode();
            });
        }

        // 显示模拟模式
        function showMockMode() {
            document.getElementById('startLoginBtn').disabled = false;
            document.getElementById('startLoginBtn').textContent = '🎭 模拟免登录流程';
        }

        // 开始官方免登录流程
        async function startOfficialLogin() {
            addLog('=== 开始官方免登录流程 ===');
            updateStatus('🚀 开始免登录流程...', 'loading');

            try {
                // 步骤1: 获取应用配置
                await step1_getAppConfig();

                // 步骤2: 获取JSAPI签名
                await step2_getJSAPISignature();

                // 步骤3: 配置钉钉JSAPI
                await step3_configDingTalk();

                // 步骤4: 获取免登授权码
                await step4_getAuthCode();

            } catch (error) {
                addLog(`免登录流程失败: ${error.message}`, 'error');
                updateStatus('❌ 免登录流程失败', 'error');
            }
        }

        // 步骤1: 获取应用配置
        async function step1_getAppConfig() {
            addLog('步骤1: 获取应用配置...');
            updateStatus('📋 正在获取应用配置...');

            try {
                const response = await fetch('/api/app/config');
                const data = await response.json();

                if (data.success) {
                    appConfig = data.data;
                    addLog(`应用配置获取成功: AgentId=${appConfig.agentId}, CorpId=${appConfig.corpId}`);

                    // 显示配置信息
                    document.getElementById('configDetails').textContent = JSON.stringify(appConfig, null, 2);
                } else {
                    throw new Error(data.message || '获取应用配置失败');
                }
            } catch (error) {
                if (!isDingTalkEnv) {
                    // 模拟模式
                    appConfig = {
                        agentId: 'mock_agent_id',
                        corpId: 'mock_corp_id'
                    };
                    addLog('模拟模式: 使用模拟应用配置');
                    document.getElementById('configDetails').textContent = JSON.stringify(appConfig, null, 2);
                } else {
                    throw error;
                }
            }
        }

        // 步骤2: 获取JSAPI签名
        async function step2_getJSAPISignature() {
            addLog('步骤2: 获取JSAPI签名...');
            updateStatus('🔐 正在获取JSAPI签名...');

            try {
                const currentUrl = encodeURIComponent(window.location.href.split('#')[0]);
                const response = await fetch(`/api/auth/jsapi-signature?url=${currentUrl}`);
                const data = await response.json();

                if (data.success) {
                    appConfig.signature = data.data;
                    addLog(`JSAPI签名获取成功: ${data.data.signature.substring(0, 10)}...`);

                    // 更新配置显示
                    document.getElementById('configDetails').textContent = JSON.stringify({
                        ...appConfig,
                        signature: appConfig.signature
                    }, null, 2);
                } else {
                    throw new Error(data.message || '获取JSAPI签名失败');
                }
            } catch (error) {
                if (!isDingTalkEnv) {
                    // 模拟模式
                    appConfig.signature = {
                        timeStamp: Date.now(),
                        nonceStr: 'mock_nonce_str',
                        signature: 'mock_signature_hash'
                    };
                    addLog('模拟模式: 使用模拟JSAPI签名');
                } else {
                    throw error;
                }
            }
        }

        // 步骤3: 配置钉钉JSAPI
        async function step3_configDingTalk() {
            addLog('步骤3: 配置钉钉JSAPI...');
            updateStatus('⚙️ 正在配置钉钉JSAPI...');

            if (!isDingTalkEnv) {
                addLog('模拟模式: 跳过JSAPI配置');
                return;
            }

            return new Promise((resolve, reject) => {
                const config = {
                    agentId: appConfig.agentId,
                    corpId: appConfig.corpId,
                    timeStamp: appConfig.signature.timeStamp,
                    nonceStr: appConfig.signature.nonceStr,
                    signature: appConfig.signature.signature,
                    jsApiList: ['runtime.permission.requestAuthCode']
                };

                addLog(`配置参数: AgentId=${config.agentId}, CorpId=${config.corpId}, TimeStamp=${config.timeStamp}`);
                addLog(`签名信息: NonceStr=${config.nonceStr}, Signature=${config.signature.substring(0, 10)}...`);

                dd.config(config);

                dd.ready(() => {
                    addLog('钉钉JSAPI配置成功');
                    resolve();
                });

                dd.error((err) => {
                    addLog(`钉钉JSAPI配置失败: ${JSON.stringify(err)}`, 'error');
                    reject(new Error(`JSAPI配置失败: ${err.message || err}`));
                });
            });
        }

        // 步骤4: 获取免登授权码
        async function step4_getAuthCode() {
            addLog('步骤4: 获取免登授权码...');
            updateStatus('🎫 正在获取免登授权码...');

            if (!isDingTalkEnv) {
                // 模拟模式
                const mockUserInfo = {
                    userid: 'mock_user_001',
                    name: '模拟用户',
                    mobile: '138****8888',
                    email: '<EMAIL>',
                    position: '软件工程师',
                    department: [1, 2]
                };

                addLog('模拟模式: 使用模拟用户信息');
                displayUserInfo(mockUserInfo);
                updateStatus('✅ 模拟免登录成功！', 'success');
                return;
            }

            return new Promise((resolve, reject) => {
                dd.runtime.permission.requestAuthCode({
                    corpId: appConfig.corpId,
                    redirection: "none",
                    onSuccess: async (result) => {
                        addLog(`免登授权码获取成功: ${result.code}`);

                        try {
                            // 步骤5: 通过授权码获取用户信息
                            await step5_getUserInfo(result.code);
                            resolve();
                        } catch (error) {
                            reject(error);
                        }
                    },
                    onFail: (err) => {
                        addLog(`获取免登授权码失败: ${JSON.stringify(err)}`, 'error');
                        reject(new Error(`获取免登授权码失败: ${err.message || err}`));
                    }
                });
            });
        }

        // 步骤5: 获取用户信息
        async function step5_getUserInfo(authCode) {
            addLog('步骤5: 获取用户信息...');
            updateStatus('👤 正在获取用户信息...');

            try {
                const response = await fetch('/api/auth/user-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        authCode: authCode
                    })
                });

                const data = await response.json();

                if (data.success) {
                    addLog(`用户信息获取成功: ${data.data.name} (${data.data.userid})`);
                    displayUserInfo(data.data);
                    updateStatus('✅ 免登录成功！', 'success');
                } else {
                    throw new Error(data.message || '获取用户信息失败');
                }
            } catch (error) {
                addLog(`获取用户信息失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 显示用户信息
        function displayUserInfo(userInfo) {
            const userInfoEl = document.getElementById('userInfo');
            userInfoEl.innerHTML = `
                <div class="step-content">
                    <div><strong>用户ID:</strong> ${userInfo.userid || 'N/A'}</div>
                    <div><strong>姓名:</strong> ${userInfo.name || 'N/A'}</div>
                    <div><strong>手机号:</strong> ${userInfo.mobile || 'N/A'}</div>
                    <div><strong>邮箱:</strong> ${userInfo.email || 'N/A'}</div>
                    <div><strong>职位:</strong> ${userInfo.position || 'N/A'}</div>
                    <div><strong>部门ID:</strong> ${userInfo.department ? userInfo.department.join(', ') : 'N/A'}</div>
                </div>
                <div class="code-block" style="margin-top: 10px;">${JSON.stringify(userInfo, null, 2)}</div>
            `;

            addLog('用户信息显示完成');
        }

        // 检查API状态
        async function checkApiStatus() {
            addLog('=== 开始API状态检查 ===');
            updateStatus('🔍 正在检查API状态...', 'loading');

            const apiTests = [
                { name: '健康检查', url: '/api/health', method: 'GET' },
                { name: '应用配置', url: '/api/app/config', method: 'GET' },
                { name: 'JSAPI签名', url: `/api/auth/jsapi-signature?url=${encodeURIComponent(window.location.href)}`, method: 'GET' },
                { name: '部门列表', url: '/api/auth/departments', method: 'GET' }
            ];

            let passed = 0;
            let total = apiTests.length;

            for (const test of apiTests) {
                try {
                    addLog(`测试 ${test.name}...`);
                    const response = await fetch(test.url, { method: test.method });
                    const data = await response.json();

                    if (response.ok && data.success) {
                        addLog(`✅ ${test.name} - 成功`, 'success');
                        passed++;
                    } else {
                        addLog(`❌ ${test.name} - 失败: ${data.message || '未知错误'}`, 'error');
                    }
                } catch (error) {
                    addLog(`❌ ${test.name} - 请求失败: ${error.message}`, 'error');
                }
            }

            const successRate = ((passed / total) * 100).toFixed(1);
            if (passed === total) {
                updateStatus(`✅ API状态检查完成 - 全部通过 (${passed}/${total})`, 'success');
                addLog(`🎉 所有API接口正常，成功率: ${successRate}%`, 'success');
            } else {
                updateStatus(`⚠️ API状态检查完成 - 部分失败 (${passed}/${total})`, 'warning');
                addLog(`⚠️ 部分API接口异常，成功率: ${successRate}%`, 'warning');
            }
        }
    </script>
</body>

</html>