<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>项目管理系统</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      min-height: 100vh;
    }

    .header {
      background: linear-gradient(135deg, #1890ff, #36cfc9);
      color: white;
      padding: 20px 30px;
    }

    .header h1 {
      color: white;
      margin: 0 0 10px 0;
      font-size: 2em;
      font-weight: 300;
    }

    .header p {
      font-size: 1.1em;
      opacity: 0.9;
      margin: 0;
    }

    .nav-menu {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 10px;
    }

    .nav-link {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      text-decoration: none;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      transition: all 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .nav-link:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .nav-link.active {
      background: rgba(255, 255, 255, 0.9);
      color: #1890ff;
      font-weight: bold;
    }

    .content {
      padding: 30px;
    }

    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 15px;
    }

    .search-filters {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
      align-items: center;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .form-group label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .form-control {
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 14px;
      min-width: 120px;
    }

    .form-control:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .btn {
      background: #1890ff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 5px;
    }

    .btn:hover {
      background: #40a9ff;
      transform: translateY(-1px);
    }

    .btn-success {
      background: #52c41a;
    }

    .btn-success:hover {
      background: #73d13d;
    }

    .btn-danger {
      background: #ff4d4f;
    }

    .btn-danger:hover {
      background: #ff7875;
    }

    .btn-secondary {
      background: #d9d9d9;
      color: #333;
    }

    .btn-secondary:hover {
      background: #f0f0f0;
    }

    .table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table {
      width: 100%;
      border-collapse: collapse;
    }

    .table th,
    .table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #f0f0f0;
    }

    .table th {
      background: #fafafa;
      font-weight: 600;
      color: #333;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .table tbody tr:hover {
      background: #f5f5f5;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-draft {
      background: #fff7e6;
      color: #fa8c16;
    }

    .status-active {
      background: #f6ffed;
      color: #52c41a;
    }

    .status-completed {
      background: #e6f7ff;
      color: #1890ff;
    }

    .status-cancelled {
      background: #fff2f0;
      color: #ff4d4f;
    }

    .actions {
      display: flex;
      gap: 8px;
    }

    .btn-sm {
      padding: 4px 8px;
      font-size: 12px;
    }

    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      margin-top: 20px;
    }

    .pagination button {
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      background: white;
      cursor: pointer;
      border-radius: 4px;
    }

    .pagination button:hover {
      border-color: #1890ff;
      color: #1890ff;
    }

    .pagination button:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .pagination .current {
      background: #1890ff;
      color: white;
      border-color: #1890ff;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }

    .modal.show {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-content {
      background: white;
      border-radius: 8px;
      width: 90%;
      max-width: 800px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-header h3 {
      margin: 0;
      color: #333;
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #999;
    }

    .modal-body {
      padding: 20px;
    }

    .modal-footer {
      padding: 20px;
      border-top: 1px solid #f0f0f0;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-group-full {
      grid-column: 1 / -1;
    }

    .currency {
      color: #52c41a;
      font-weight: 600;
    }

    .percentage {
      color: #1890ff;
      font-weight: 600;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: #999;
    }

    .empty {
      text-align: center;
      padding: 40px;
      color: #999;
    }

    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stats-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .stats-card h3 {
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }

    .stats-card .value {
      font-size: 24px;
      font-weight: bold;
      color: #1890ff;
    }

    @media (max-width: 768px) {
      .content {
        padding: 15px;
      }

      .toolbar {
        flex-direction: column;
        align-items: stretch;
      }

      .search-filters {
        flex-direction: column;
      }

      .form-control {
        min-width: auto;
      }

      .table-container {
        overflow-x: auto;
      }

      .modal-content {
        width: 95%;
        margin: 10px;
      }

      .form-row {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>📊 项目管理系统</h1>
      <p>项目立项、品牌管理、预算控制一体化平台</p>

      <!-- 导航菜单 -->
      <div class="nav-menu">
        <a href="index.html" class="nav-link">🏠 首页</a>
        <a href="dingtalk-design-demo.html" class="nav-link">🎨 完整功能</a>
        <a href="dingtalk-login-demo.html" class="nav-link">🔐 免登录演示</a>
        <a href="test-api.html" class="nav-link">🧪 API测试</a>
        <a href="signature-test.html" class="nav-link">🔑 签名测试</a>
        <a href="official-login-demo.html" class="nav-link">📱 官方演示</a>
        <a href="project-management.html" class="nav-link active">📊 项目管理</a>
      </div>
    </div>

    <div class="content">
      <!-- 统计卡片 -->
      <div class="stats-cards" id="statsCards">
        <div class="stats-card">
          <h3>总项目数</h3>
          <div class="value" id="totalProjects">-</div>
        </div>
        <div class="stats-card">
          <h3>活跃项目</h3>
          <div class="value" id="activeProjects">-</div>
        </div>
        <div class="stats-card">
          <h3>总预算</h3>
          <div class="value currency" id="totalBudget">-</div>
        </div>
        <div class="stats-card">
          <h3>总利润</h3>
          <div class="value currency" id="totalProfit">-</div>
        </div>
        <div class="stats-card">
          <h3>平均毛利率</h3>
          <div class="value percentage" id="averageMargin">-</div>
        </div>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="search-filters">
          <div class="form-group">
            <label>项目名称</label>
            <input type="text" class="form-control" id="searchKeyword" placeholder="搜索项目名称">
          </div>
          <div class="form-group">
            <label>品牌</label>
            <select class="form-control" id="filterBrand">
              <option value="">全部品牌</option>
            </select>
          </div>
          <div class="form-group">
            <label>合同类型</label>
            <select class="form-control" id="filterContract">
              <option value="">全部类型</option>
              <option value="annual_frame">年框</option>
              <option value="quarterly_frame">季框</option>
              <option value="single">单次</option>
              <option value="po_order">PO单</option>
              <option value="jing_task">京任务</option>
            </select>
          </div>
          <div class="form-group">
            <label>项目状态</label>
            <select class="form-control" id="filterStatus">
              <option value="">全部状态</option>
              <option value="draft">草稿</option>
              <option value="active">进行中</option>
              <option value="completed">已完成</option>
              <option value="cancelled">已取消</option>
            </select>
          </div>
          <button class="btn" onclick="searchProjects()">🔍 搜索</button>
          <button class="btn btn-secondary" onclick="resetFilters()">🔄 重置</button>
        </div>
        <div>
          <button class="btn btn-success" onclick="showCreateModal()">➕ 新建项目</button>
          <button class="btn btn-secondary" onclick="showBrandModal()">🏷️ 品牌管理</button>
        </div>
      </div>

      <!-- 项目列表 -->
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>项目名称</th>
              <th>品牌</th>
              <th>执行周期</th>
              <th>规划预算</th>
              <th>项目利润</th>
              <th>毛利率</th>
              <th>执行PM</th>
              <th>合同类型</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="projectTableBody">
            <tr>
              <td colspan="10" class="loading">正在加载项目数据...</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination" id="pagination">
        <!-- 分页按钮将通过JavaScript生成 -->
      </div>
    </div>
  </div>

  <!-- 项目创建/编辑模态框 -->
  <div class="modal" id="projectModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="projectModalTitle">新建项目</h3>
        <button class="modal-close" onclick="closeModal('projectModal')">&times;</button>
      </div>
      <div class="modal-body">
        <form id="projectForm">
          <input type="hidden" id="projectId">

          <div class="form-row">
            <div class="form-group">
              <label for="documentType">单据类型 *</label>
              <select class="form-control" id="documentType" required>
                <option value="project_initiation">项目立项表</option>
              </select>
            </div>
            <div class="form-group">
              <label for="brandId">品牌 *</label>
              <select class="form-control" id="brandId" required>
                <option value="">请选择品牌</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group form-group-full">
              <label for="projectName">项目名称 *</label>
              <input type="text" class="form-control" id="projectName" required placeholder="请输入项目名称">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="startDate">开始日期 *</label>
              <input type="date" class="form-control" id="startDate" required>
            </div>
            <div class="form-group">
              <label for="endDate">结束日期 *</label>
              <input type="date" class="form-control" id="endDate" required>
            </div>
          </div>

          <h4 style="margin: 20px 0 10px 0; color: #1890ff;">预算信息</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="planningBudget">项目规划预算 *</label>
              <input type="number" class="form-control" id="planningBudget" required min="0" step="0.01"
                placeholder="0.00">
            </div>
            <div class="form-group">
              <label for="influencerBudget">达人预算 *</label>
              <input type="number" class="form-control" id="influencerBudget" required min="0" step="0.01"
                placeholder="0.00">
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="adBudget">投流预算 *</label>
              <input type="number" class="form-control" id="adBudget" required min="0" step="0.01" placeholder="0.00">
            </div>
            <div class="form-group">
              <label for="otherBudget">其他预算 *</label>
              <input type="number" class="form-control" id="otherBudget" required min="0" step="0.01"
                placeholder="0.00">
            </div>
          </div>

          <h4 style="margin: 20px 0 10px 0; color: #1890ff;">成本信息</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="influencerCost">达人成本 *</label>
              <input type="number" class="form-control" id="influencerCost" required min="0" step="0.01"
                placeholder="0.00">
            </div>
            <div class="form-group">
              <label for="adCost">投流成本 *</label>
              <input type="number" class="form-control" id="adCost" required min="0" step="0.01" placeholder="0.00">
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="otherCost">其他成本 *</label>
              <input type="number" class="form-control" id="otherCost" required min="0" step="0.01" placeholder="0.00">
            </div>
            <div class="form-group">
              <label for="estimatedInfluencerRebate">预估达人返点 *</label>
              <input type="number" class="form-control" id="estimatedInfluencerRebate" required min="0" step="0.01"
                placeholder="0.00">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="executorPM">执行PM *</label>
              <select class="form-control" id="executorPM" required>
                <option value="">请选择执行PM</option>
              </select>
            </div>
            <div class="form-group">
              <label for="contractType">合同类型 *</label>
              <select class="form-control" id="contractType" required>
                <option value="">请选择合同类型</option>
                <option value="annual_frame">年框</option>
                <option value="quarterly_frame">季框</option>
                <option value="single">单次</option>
                <option value="po_order">PO单</option>
                <option value="jing_task">京任务</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group form-group-full">
              <label for="contentMediaIds">内容媒介 *</label>
              <select class="form-control" id="contentMediaIds" multiple required>
                <option value="">请选择内容媒介（可多选）</option>
              </select>
              <small style="color: #666;">按住Ctrl键可多选</small>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group form-group-full">
              <label for="settlementRules">项目结算规则 *</label>
              <textarea class="form-control" id="settlementRules" required rows="4" placeholder="请输入项目结算规则"></textarea>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group form-group-full">
              <label for="kpi">KPI *</label>
              <textarea class="form-control" id="kpi" required rows="4" placeholder="请输入KPI要求"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="closeModal('projectModal')">取消</button>
        <button type="button" class="btn btn-success" onclick="saveProject()">保存</button>
      </div>
    </div>
  </div>

  <!-- 品牌管理模态框 -->
  <div class="modal" id="brandModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>品牌管理</h3>
        <button class="modal-close" onclick="closeModal('brandModal')">&times;</button>
      </div>
      <div class="modal-body">
        <div style="margin-bottom: 20px;">
          <button class="btn btn-success" onclick="showCreateBrandForm()">➕ 新建品牌</button>
        </div>

        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>品牌名称</th>
                <th>描述</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="brandTableBody">
              <tr>
                <td colspan="5" class="loading">正在加载品牌数据...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="closeModal('brandModal')">关闭</button>
      </div>
    </div>
  </div>

  <!-- 品牌创建/编辑模态框 -->
  <div class="modal" id="brandFormModal">
    <div class="modal-content" style="max-width: 500px;">
      <div class="modal-header">
        <h3 id="brandFormModalTitle">新建品牌</h3>
        <button class="modal-close" onclick="closeModal('brandFormModal')">&times;</button>
      </div>
      <div class="modal-body">
        <form id="brandForm">
          <input type="hidden" id="brandFormId">

          <div class="form-group">
            <label for="brandName">品牌名称 *</label>
            <input type="text" class="form-control" id="brandName" required placeholder="请输入品牌名称">
          </div>

          <div class="form-group">
            <label for="brandDescription">品牌描述</label>
            <textarea class="form-control" id="brandDescription" rows="3" placeholder="请输入品牌描述"></textarea>
          </div>

          <div class="form-group">
            <label for="brandLogo">品牌Logo URL</label>
            <input type="url" class="form-control" id="brandLogo" placeholder="请输入Logo URL">
          </div>

          <div class="form-group">
            <label for="brandStatus">状态</label>
            <select class="form-control" id="brandStatus">
              <option value="active">启用</option>
              <option value="inactive">禁用</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="closeModal('brandFormModal')">取消</button>
        <button type="button" class="btn btn-success" onclick="saveBrand()">保存</button>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentPage = 1;
    let pageSize = 20;
    let totalPages = 1;
    let brands = [];
    let users = [];

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
      loadInitialData();
    });

    // 加载初始数据
    async function loadInitialData() {
      try {
        await Promise.all([
          loadProjectStats(),
          loadBrands(),
          loadUsers(),
          loadProjects()
        ]);
      } catch (error) {
        console.error('加载初始数据失败:', error);
        showError('加载数据失败，请刷新页面重试');
      }
    }

    // 加载项目统计
    async function loadProjectStats() {
      try {
        const response = await fetch('/api/projects/stats');
        const data = await response.json();

        if (data.success) {
          const stats = data.data;
          document.getElementById('totalProjects').textContent = stats.totalProjects;
          document.getElementById('activeProjects').textContent = stats.activeProjects;
          document.getElementById('totalBudget').textContent = formatCurrency(stats.totalBudget);
          document.getElementById('totalProfit').textContent = formatCurrency(stats.totalProfit);
          document.getElementById('averageMargin').textContent = stats.averageGrossMargin.toFixed(2) + '%';
        }
      } catch (error) {
        console.error('加载项目统计失败:', error);
      }
    }

    // 加载品牌列表
    async function loadBrands() {
      try {
        const response = await fetch('/api/brands?status=active&pageSize=100');
        const data = await response.json();

        if (data.success) {
          brands = data.data.brands;
          updateBrandSelects();
        }
      } catch (error) {
        console.error('加载品牌列表失败:', error);
      }
    }

    // 更新品牌下拉框
    function updateBrandSelects() {
      const selects = ['filterBrand', 'brandId'];

      selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (!select) return;

        // 保存当前选中值
        const currentValue = select.value;

        // 清空选项（保留第一个默认选项）
        const firstOption = select.firstElementChild;
        select.innerHTML = '';
        if (firstOption) {
          select.appendChild(firstOption);
        }

        // 添加品牌选项
        brands.forEach(brand => {
          const option = document.createElement('option');
          option.value = brand.id;
          option.textContent = brand.name;
          select.appendChild(option);
        });

        // 恢复选中值
        select.value = currentValue;
      });
    }

    // 加载用户列表（模拟）
    async function loadUsers() {
      try {
        // 这里应该调用钉钉API获取用户列表
        // 暂时使用模拟数据
        users = [
          { userid: 'user-001', name: '张三', department: '营销部' },
          { userid: 'user-002', name: '李四', department: '创意部' },
          { userid: 'user-003', name: '王五', department: '媒介部' },
          { userid: 'user-004', name: '赵六', department: '项目部' }
        ];

        updateUserSelects();
      } catch (error) {
        console.error('加载用户列表失败:', error);
      }
    }

    // 更新用户下拉框
    function updateUserSelects() {
      const selects = ['executorPM', 'contentMediaIds'];

      selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (!select) return;

        // 保存当前选中值
        const currentValue = select.value;

        // 清空选项（保留第一个默认选项）
        const firstOption = select.firstElementChild;
        select.innerHTML = '';
        if (firstOption) {
          select.appendChild(firstOption);
        }

        // 添加用户选项
        users.forEach(user => {
          const option = document.createElement('option');
          option.value = user.userid;
          option.textContent = `${user.name} (${user.department})`;
          select.appendChild(option);
        });

        // 恢复选中值
        select.value = currentValue;
      });
    }

    // 加载项目列表
    async function loadProjects() {
      try {
        const params = new URLSearchParams({
          page: currentPage,
          pageSize: pageSize
        });

        // 添加过滤条件
        const keyword = document.getElementById('searchKeyword').value;
        const brandId = document.getElementById('filterBrand').value;
        const contractType = document.getElementById('filterContract').value;
        const status = document.getElementById('filterStatus').value;

        if (keyword) params.append('keyword', keyword);
        if (brandId) params.append('brandId', brandId);
        if (contractType) params.append('contractType', contractType);
        if (status) params.append('status', status);

        const response = await fetch(`/projects?${params}`);
        const data = await response.json();

        if (data.success) {
          const result = data.data;
          totalPages = result.totalPages;
          renderProjectTable(result.projects);
          renderPagination();
        } else {
          showError(data.message || '加载项目列表失败');
        }
      } catch (error) {
        console.error('加载项目列表失败:', error);
        showError('加载项目列表失败，请检查网络连接');
      }
    }

    // 渲染项目表格
    function renderProjectTable(projects) {
      const tbody = document.getElementById('projectTableBody');

      if (projects.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="empty">暂无项目数据</td></tr>';
        return;
      }

      tbody.innerHTML = projects.map(project => `
        <tr>
          <td>
            <div style="font-weight: 600;">${project.projectName}</div>
            <div style="font-size: 12px; color: #666;">${project.documentType === 'project_initiation' ? '项目立项表' : project.documentType}</div>
          </td>
          <td>${project.brand ? project.brand.name : '未知品牌'}</td>
          <td>
            <div>${formatDate(project.period.startDate)}</div>
            <div style="font-size: 12px; color: #666;">至 ${formatDate(project.period.endDate)}</div>
          </td>
          <td class="currency">${formatCurrency(project.budget.planningBudget)}</td>
          <td class="currency">${formatCurrency(project.profit.profit)}</td>
          <td class="percentage">${project.profit.grossMargin.toFixed(2)}%</td>
          <td>${project.executorPMInfo ? project.executorPMInfo.name : project.executorPM}</td>
          <td>${getContractTypeLabel(project.contractType)}</td>
          <td><span class="status-badge status-${project.status}">${getStatusLabel(project.status)}</span></td>
          <td>
            <div class="actions">
              <button class="btn btn-sm" onclick="editProject('${project.id}')">编辑</button>
              <button class="btn btn-sm btn-danger" onclick="deleteProject('${project.id}')">删除</button>
            </div>
          </td>
        </tr>
      `).join('');
    }

    // 渲染分页
    function renderPagination() {
      const pagination = document.getElementById('pagination');

      if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
      }

      let html = '';

      // 上一页
      html += `<button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">上一页</button>`;

      // 页码
      for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
          html += `<button class="current">${i}</button>`;
        } else if (i === 1 || i === totalPages || Math.abs(i - currentPage) <= 2) {
          html += `<button onclick="changePage(${i})">${i}</button>`;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
          html += '<span>...</span>';
        }
      }

      // 下一页
      html += `<button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">下一页</button>`;

      pagination.innerHTML = html;
    }

    // 切换页码
    function changePage(page) {
      if (page < 1 || page > totalPages || page === currentPage) return;
      currentPage = page;
      loadProjects();
    }

    // 搜索项目
    function searchProjects() {
      currentPage = 1;
      loadProjects();
    }

    // 重置过滤条件
    function resetFilters() {
      document.getElementById('searchKeyword').value = '';
      document.getElementById('filterBrand').value = '';
      document.getElementById('filterContract').value = '';
      document.getElementById('filterStatus').value = '';
      searchProjects();
    }

    // 显示创建项目模态框
    function showCreateModal() {
      document.getElementById('projectModalTitle').textContent = '新建项目';
      document.getElementById('projectForm').reset();
      document.getElementById('projectId').value = '';
      showModal('projectModal');
    }

    // 编辑项目
    async function editProject(id) {
      try {
        const response = await fetch(`/api/projects/${id}`);
        const data = await response.json();

        if (data.success) {
          const project = data.data;
          document.getElementById('projectModalTitle').textContent = '编辑项目';
          fillProjectForm(project);
          showModal('projectModal');
        } else {
          showError(data.message || '获取项目信息失败');
        }
      } catch (error) {
        console.error('获取项目信息失败:', error);
        showError('获取项目信息失败');
      }
    }

    // 填充项目表单
    function fillProjectForm(project) {
      document.getElementById('projectId').value = project.id;
      document.getElementById('documentType').value = project.documentType;
      document.getElementById('brandId').value = project.brandId;
      document.getElementById('projectName').value = project.projectName;
      document.getElementById('startDate').value = formatDateForInput(project.period.startDate);
      document.getElementById('endDate').value = formatDateForInput(project.period.endDate);
      document.getElementById('planningBudget').value = project.budget.planningBudget;
      document.getElementById('influencerBudget').value = project.budget.influencerBudget;
      document.getElementById('adBudget').value = project.budget.adBudget;
      document.getElementById('otherBudget').value = project.budget.otherBudget;
      document.getElementById('influencerCost').value = project.cost.influencerCost;
      document.getElementById('adCost').value = project.cost.adCost;
      document.getElementById('otherCost').value = project.cost.otherCost;
      document.getElementById('estimatedInfluencerRebate').value = project.cost.estimatedInfluencerRebate;
      document.getElementById('executorPM').value = project.executorPM;
      document.getElementById('contractType').value = project.contractType;
      document.getElementById('settlementRules').value = project.settlementRules;
      document.getElementById('kpi').value = project.kpi;

      // 设置多选的内容媒介
      const contentMediaSelect = document.getElementById('contentMediaIds');
      Array.from(contentMediaSelect.options).forEach(option => {
        option.selected = project.contentMediaIds.includes(option.value);
      });
    }

    // 保存项目
    async function saveProject() {
      try {
        const form = document.getElementById('projectForm');
        if (!form.checkValidity()) {
          form.reportValidity();
          return;
        }

        const projectId = document.getElementById('projectId').value;
        const isEdit = !!projectId;

        const projectData = {
          documentType: document.getElementById('documentType').value,
          brandId: document.getElementById('brandId').value,
          projectName: document.getElementById('projectName').value,
          period: {
            startDate: document.getElementById('startDate').value,
            endDate: document.getElementById('endDate').value
          },
          budget: {
            planningBudget: parseFloat(document.getElementById('planningBudget').value),
            influencerBudget: parseFloat(document.getElementById('influencerBudget').value),
            adBudget: parseFloat(document.getElementById('adBudget').value),
            otherBudget: parseFloat(document.getElementById('otherBudget').value)
          },
          cost: {
            influencerCost: parseFloat(document.getElementById('influencerCost').value),
            adCost: parseFloat(document.getElementById('adCost').value),
            otherCost: parseFloat(document.getElementById('otherCost').value),
            estimatedInfluencerRebate: parseFloat(document.getElementById('estimatedInfluencerRebate').value)
          },
          executorPM: document.getElementById('executorPM').value,
          contractType: document.getElementById('contractType').value,
          settlementRules: document.getElementById('settlementRules').value,
          kpi: document.getElementById('kpi').value,
          contentMediaIds: Array.from(document.getElementById('contentMediaIds').selectedOptions).map(option => option.value)
        };

        if (isEdit) {
          projectData.id = projectId;
        }

        const url = isEdit ? '/projects' : '/projects';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(projectData)
        });

        const data = await response.json();

        if (data.success) {
          showSuccess(isEdit ? '项目更新成功' : '项目创建成功');
          closeModal('projectModal');
          loadProjects();
          loadProjectStats();
        } else {
          showError(data.message || '保存项目失败');
        }
      } catch (error) {
        console.error('保存项目失败:', error);
        showError('保存项目失败');
      }
    }

    // 删除项目
    async function deleteProject(id) {
      if (!confirm('确定要删除这个项目吗？此操作不可恢复。')) {
        return;
      }

      try {
        const response = await fetch(`/api/projects/${id}`, {
          method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
          showSuccess('项目删除成功');
          loadProjects();
          loadProjectStats();
        } else {
          showError(data.message || '删除项目失败');
        }
      } catch (error) {
        console.error('删除项目失败:', error);
        showError('删除项目失败');
      }
    }

    // 显示品牌管理模态框
    async function showBrandModal() {
      showModal('brandModal');
      await loadBrandTable();
    }

    // 加载品牌表格
    async function loadBrandTable() {
      try {
        const response = await fetch('/api/brands?pageSize=100');
        const data = await response.json();

        if (data.success) {
          renderBrandTable(data.data.brands);
        } else {
          showError(data.message || '加载品牌列表失败');
        }
      } catch (error) {
        console.error('加载品牌列表失败:', error);
        showError('加载品牌列表失败');
      }
    }

    // 渲染品牌表格
    function renderBrandTable(brands) {
      const tbody = document.getElementById('brandTableBody');

      if (brands.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="empty">暂无品牌数据</td></tr>';
        return;
      }

      tbody.innerHTML = brands.map(brand => `
        <tr>
          <td>
            <div style="font-weight: 600;">${brand.name}</div>
            ${brand.logo ? `<img src="${brand.logo}" alt="Logo" style="width: 20px; height: 20px; margin-top: 5px;">` : ''}
          </td>
          <td>${brand.description || '-'}</td>
          <td><span class="status-badge status-${brand.status}">${brand.status === 'active' ? '启用' : '禁用'}</span></td>
          <td>${formatDate(brand.createdAt)}</td>
          <td>
            <div class="actions">
              <button class="btn btn-sm" onclick="editBrand('${brand.id}')">编辑</button>
              <button class="btn btn-sm btn-danger" onclick="deleteBrand('${brand.id}')">删除</button>
            </div>
          </td>
        </tr>
      `).join('');
    }

    // 显示创建品牌表单
    function showCreateBrandForm() {
      document.getElementById('brandFormModalTitle').textContent = '新建品牌';
      document.getElementById('brandForm').reset();
      document.getElementById('brandFormId').value = '';
      document.getElementById('brandStatus').value = 'active';
      showModal('brandFormModal');
    }

    // 编辑品牌
    async function editBrand(id) {
      try {
        const response = await fetch(`/api/brands/${id}`);
        const data = await response.json();

        if (data.success) {
          const brand = data.data;
          document.getElementById('brandFormModalTitle').textContent = '编辑品牌';
          document.getElementById('brandFormId').value = brand.id;
          document.getElementById('brandName').value = brand.name;
          document.getElementById('brandDescription').value = brand.description || '';
          document.getElementById('brandLogo').value = brand.logo || '';
          document.getElementById('brandStatus').value = brand.status;
          showModal('brandFormModal');
        } else {
          showError(data.message || '获取品牌信息失败');
        }
      } catch (error) {
        console.error('获取品牌信息失败:', error);
        showError('获取品牌信息失败');
      }
    }

    // 保存品牌
    async function saveBrand() {
      try {
        const form = document.getElementById('brandForm');
        if (!form.checkValidity()) {
          form.reportValidity();
          return;
        }

        const brandId = document.getElementById('brandFormId').value;
        const isEdit = !!brandId;

        const brandData = {
          name: document.getElementById('brandName').value,
          description: document.getElementById('brandDescription').value,
          logo: document.getElementById('brandLogo').value,
          status: document.getElementById('brandStatus').value
        };

        if (isEdit) {
          brandData.id = brandId;
        }

        const url = isEdit ? '/api/brands' : '/api/brands';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(brandData)
        });

        const data = await response.json();

        if (data.success) {
          showSuccess(isEdit ? '品牌更新成功' : '品牌创建成功');
          closeModal('brandFormModal');
          loadBrandTable();
          loadBrands(); // 重新加载品牌下拉框
        } else {
          showError(data.message || '保存品牌失败');
        }
      } catch (error) {
        console.error('保存品牌失败:', error);
        showError('保存品牌失败');
      }
    }

    // 删除品牌
    async function deleteBrand(id) {
      if (!confirm('确定要删除这个品牌吗？如果有项目使用此品牌，将无法删除。')) {
        return;
      }

      try {
        const response = await fetch(`/api/brands/${id}`, {
          method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
          showSuccess('品牌删除成功');
          loadBrandTable();
          loadBrands(); // 重新加载品牌下拉框
        } else {
          showError(data.message || '删除品牌失败');
        }
      } catch (error) {
        console.error('删除品牌失败:', error);
        showError('删除品牌失败');
      }
    }

    // 模态框操作
    function showModal(modalId) {
      document.getElementById(modalId).classList.add('show');
    }

    function closeModal(modalId) {
      document.getElementById(modalId).classList.remove('show');
    }

    // 工具函数
    function formatCurrency(amount) {
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      }).format(amount);
    }

    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    }

    function formatDateForInput(dateString) {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    }

    function getContractTypeLabel(type) {
      const labels = {
        'annual_frame': '年框',
        'quarterly_frame': '季框',
        'single': '单次',
        'po_order': 'PO单',
        'jing_task': '京任务'
      };
      return labels[type] || type;
    }

    function getStatusLabel(status) {
      const labels = {
        'draft': '草稿',
        'active': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return labels[status] || status;
    }

    function showSuccess(message) {
      // 简单的成功提示，实际项目中可以使用更好的UI组件
      alert('✅ ' + message);
    }

    function showError(message) {
      // 简单的错误提示，实际项目中可以使用更好的UI组件
      alert('❌ ' + message);
    }

    // 点击模态框外部关闭
    document.addEventListener('click', function (event) {
      if (event.target.classList.contains('modal')) {
        event.target.classList.remove('show');
      }
    });

    // 键盘事件
    document.addEventListener('keydown', function (event) {
      if (event.key === 'Escape') {
        // 关闭所有模态框
        document.querySelectorAll('.modal.show').forEach(modal => {
          modal.classList.remove('show');
        });
      }
    });

    // 搜索框回车事件
    document.getElementById('searchKeyword').addEventListener('keypress', function (event) {
      if (event.key === 'Enter') {
        searchProjects();
      }
    });
  </script>
</body>

</html>