/**
 * 钉钉用户选择器组件
 * 支持单选和多选模式
 */
class UserSelector {
    constructor(inputId, dropdownId, selectedId, multiple = false) {
        this.input = document.getElementById(inputId);
        this.dropdown = document.getElementById(dropdownId);
        this.selectedContainer = document.getElementById(selectedId);
        this.multiple = multiple;
        this.selectedUsers = [];
        this.allUsers = [];
        this.isLoading = false;
        
        this.init();
    }

    init() {
        // 加载所有用户
        this.loadUsers();
        
        // 绑定事件
        this.input.addEventListener('input', this.handleInput.bind(this));
        this.input.addEventListener('focus', this.handleFocus.bind(this));
        document.addEventListener('click', this.handleDocumentClick.bind(this));
    }

    async loadUsers() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();

        try {
            // 获取部门用户列表（这里使用部门ID 1作为示例）
            const headers = {};

            // 尝试从全局变量或URL参数获取认证码
            let authCode = null;
            if (typeof window !== 'undefined' && window.authCode) {
                authCode = window.authCode;
            } else {
                const urlParams = new URLSearchParams(window.location.search);
                authCode = urlParams.get('authCode');
            }

            if (authCode) {
                headers['X-DingTalk-Auth-Code'] = authCode;
            }

            const response = await fetch('/api/departments/1/users', {
                headers: headers
            });
            const data = await response.json();
            
            if (data.success && data.data.list) {
                this.allUsers = data.data.list;
                
                // 获取每个用户的详细信息
                const userDetails = await Promise.all(
                    this.allUsers.map(async (user) => {
                        try {
                            const detailResponse = await fetch(`/api/users/${user.userid}`);
                            const detailData = await detailResponse.json();
                            return detailData.success ? detailData.data : user;
                        } catch (error) {
                            console.error('获取用户详情失败:', error);
                            return user;
                        }
                    })
                );
                
                this.allUsers = userDetails;
                console.log('加载用户成功:', this.allUsers);
            }
        } catch (error) {
            console.error('加载用户失败:', error);
            this.showError('加载用户列表失败');
        } finally {
            this.isLoading = false;
        }
    }

    handleInput(event) {
        const keyword = event.target.value.trim();
        this.filterAndShowUsers(keyword);
    }

    handleFocus() {
        if (!this.isLoading) {
            this.filterAndShowUsers(this.input.value.trim());
        }
    }

    handleDocumentClick(event) {
        if (!event.target.closest('.user-selector')) {
            this.hideDropdown();
        }
    }

    filterAndShowUsers(keyword) {
        let filteredUsers = this.allUsers;
        
        if (keyword) {
            filteredUsers = this.allUsers.filter(user => 
                user.name?.includes(keyword) || 
                user.mobile?.includes(keyword) ||
                user.userid?.includes(keyword)
            );
        }

        this.showUsers(filteredUsers);
    }

    showUsers(users) {
        if (users.length === 0) {
            this.dropdown.innerHTML = '<div class="loading">没有找到匹配的用户</div>';
        } else {
            this.dropdown.innerHTML = users.map(user => `
                <div class="user-item" data-userid="${user.userid}" data-name="${user.name}" data-mobile="${user.mobile || ''}">
                    <div class="user-avatar">${user.name ? user.name.charAt(0) : 'U'}</div>
                    <div class="user-info">
                        <div class="user-name">${user.name || '未知用户'}</div>
                        ${user.mobile ? `<div class="user-mobile">${user.mobile}</div>` : ''}
                    </div>
                </div>
            `).join('');

            // 绑定点击事件
            this.dropdown.querySelectorAll('.user-item').forEach(item => {
                item.addEventListener('click', this.handleUserSelect.bind(this));
            });
        }

        this.showDropdown();
    }

    handleUserSelect(event) {
        const item = event.currentTarget;
        const user = {
            userid: item.dataset.userid,
            name: item.dataset.name,
            mobile: item.dataset.mobile
        };

        if (this.multiple) {
            // 多选模式
            if (!this.selectedUsers.find(u => u.userid === user.userid)) {
                this.selectedUsers.push(user);
                this.updateSelectedDisplay();
                this.input.value = ''; // 清空输入框
            }
        } else {
            // 单选模式
            this.selectedUsers = [user];
            this.input.value = user.name;
            this.updateSelectedDisplay();
        }

        this.hideDropdown();
    }

    updateSelectedDisplay() {
        if (this.multiple && this.selectedUsers.length > 0) {
            this.selectedContainer.innerHTML = this.selectedUsers.map(user => `
                <span class="selected-user">
                    ${user.name}
                    <span class="remove-user" data-userid="${user.userid}">×</span>
                </span>
            `).join('');

            // 绑定删除事件
            this.selectedContainer.querySelectorAll('.remove-user').forEach(btn => {
                btn.addEventListener('click', this.handleUserRemove.bind(this));
            });
        } else {
            this.selectedContainer.innerHTML = '';
        }
    }

    handleUserRemove(event) {
        event.stopPropagation();
        const userid = event.target.dataset.userid;
        this.selectedUsers = this.selectedUsers.filter(u => u.userid !== userid);
        this.updateSelectedDisplay();
    }

    showDropdown() {
        this.dropdown.style.display = 'block';
    }

    hideDropdown() {
        this.dropdown.style.display = 'none';
    }

    showLoading() {
        this.dropdown.innerHTML = '<div class="loading">正在加载用户列表...</div>';
        this.showDropdown();
    }

    showError(message) {
        this.dropdown.innerHTML = `<div class="error">${message}</div>`;
        this.showDropdown();
    }

    getSelectedUsers() {
        return this.selectedUsers;
    }

    getSelectedUserIds() {
        return this.selectedUsers.map(user => user.userid);
    }

    setSelectedUsers(users) {
        this.selectedUsers = users;
        this.updateSelectedDisplay();
        
        if (!this.multiple && users.length > 0) {
            this.input.value = users[0].name;
        }
    }

    reset() {
        this.selectedUsers = [];
        this.input.value = '';
        this.updateSelectedDisplay();
        this.hideDropdown();
    }

    validate() {
        if (this.selectedUsers.length === 0) {
            this.showError('请选择用户');
            return false;
        }
        return true;
    }
}

// 导出给全局使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserSelector;
} else {
    window.UserSelector = UserSelector;
}
