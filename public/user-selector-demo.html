<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉用户选择器演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .user-selector {
            position: relative;
            width: 100%;
        }
        .user-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .user-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        .user-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .user-item {
            padding: 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        .user-item:hover {
            background-color: #f5f5f5;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            font-weight: 500;
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: 500;
            color: #333;
        }
        .user-mobile {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        .selected-users {
            margin-top: 10px;
        }
        .selected-user {
            display: inline-block;
            background-color: #f0f0f0;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
        }
        .remove-user {
            margin-left: 5px;
            color: #999;
            cursor: pointer;
        }
        .remove-user:hover {
            color: #ff4d4f;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .demo-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: #ff4d4f;
            padding: 10px;
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧑‍💼 钉钉用户选择器演示</h1>
        
        <div class="demo-section">
            <div class="demo-title">执行PM选择</div>
            <div class="form-group">
                <label for="executorPM">执行PM *</label>
                <div class="user-selector" id="executorPMSelector">
                    <input type="text" 
                           class="user-input" 
                           id="executorPMInput" 
                           placeholder="搜索用户姓名或手机号..."
                           autocomplete="off">
                    <div class="user-dropdown" id="executorPMDropdown"></div>
                    <div class="selected-users" id="executorPMSelected"></div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">内容媒介选择</div>
            <div class="form-group">
                <label for="contentMedia">内容媒介 *</label>
                <div class="user-selector" id="contentMediaSelector">
                    <input type="text" 
                           class="user-input" 
                           id="contentMediaInput" 
                           placeholder="搜索用户姓名或手机号..."
                           autocomplete="off">
                    <div class="user-dropdown" id="contentMediaDropdown"></div>
                    <div class="selected-users" id="contentMediaSelected"></div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">选择结果</div>
            <div id="result">
                <p><strong>执行PM:</strong> <span id="selectedPM">未选择</span></p>
                <p><strong>内容媒介:</strong> <span id="selectedMedia">未选择</span></p>
            </div>
        </div>
    </div>

    <script>
        class UserSelector {
            constructor(inputId, dropdownId, selectedId, multiple = false) {
                this.input = document.getElementById(inputId);
                this.dropdown = document.getElementById(dropdownId);
                this.selectedContainer = document.getElementById(selectedId);
                this.multiple = multiple;
                this.selectedUsers = [];
                this.allUsers = [];
                
                this.init();
            }

            init() {
                // 加载所有用户
                this.loadUsers();
                
                // 绑定事件
                this.input.addEventListener('input', this.handleInput.bind(this));
                this.input.addEventListener('focus', this.handleFocus.bind(this));
                document.addEventListener('click', this.handleDocumentClick.bind(this));
            }

            async loadUsers() {
                try {
                    // 获取部门用户列表（这里使用部门ID 1作为示例）
                    const response = await fetch('/api/departments/1/users');
                    const data = await response.json();
                    
                    if (data.success && data.data.list) {
                        this.allUsers = data.data.list;
                        
                        // 获取每个用户的详细信息
                        const userDetails = await Promise.all(
                            this.allUsers.map(async (user) => {
                                try {
                                    const detailResponse = await fetch(`/api/users/${user.userid}`);
                                    const detailData = await detailResponse.json();
                                    return detailData.success ? detailData.data : user;
                                } catch (error) {
                                    console.error('获取用户详情失败:', error);
                                    return user;
                                }
                            })
                        );
                        
                        this.allUsers = userDetails;
                    }
                } catch (error) {
                    console.error('加载用户失败:', error);
                    this.showError('加载用户列表失败');
                }
            }

            handleInput(event) {
                const keyword = event.target.value.trim();
                this.filterAndShowUsers(keyword);
            }

            handleFocus() {
                this.filterAndShowUsers(this.input.value.trim());
            }

            handleDocumentClick(event) {
                if (!event.target.closest('.user-selector')) {
                    this.hideDropdown();
                }
            }

            filterAndShowUsers(keyword) {
                let filteredUsers = this.allUsers;
                
                if (keyword) {
                    filteredUsers = this.allUsers.filter(user => 
                        user.name?.includes(keyword) || 
                        user.mobile?.includes(keyword) ||
                        user.userid?.includes(keyword)
                    );
                }

                this.showUsers(filteredUsers);
            }

            showUsers(users) {
                if (users.length === 0) {
                    this.dropdown.innerHTML = '<div class="loading">没有找到匹配的用户</div>';
                } else {
                    this.dropdown.innerHTML = users.map(user => `
                        <div class="user-item" data-userid="${user.userid}" data-name="${user.name}" data-mobile="${user.mobile || ''}">
                            <div class="user-avatar">${user.name ? user.name.charAt(0) : 'U'}</div>
                            <div class="user-info">
                                <div class="user-name">${user.name || '未知用户'}</div>
                                ${user.mobile ? `<div class="user-mobile">${user.mobile}</div>` : ''}
                            </div>
                        </div>
                    `).join('');

                    // 绑定点击事件
                    this.dropdown.querySelectorAll('.user-item').forEach(item => {
                        item.addEventListener('click', this.handleUserSelect.bind(this));
                    });
                }

                this.showDropdown();
            }

            handleUserSelect(event) {
                const item = event.currentTarget;
                const user = {
                    userid: item.dataset.userid,
                    name: item.dataset.name,
                    mobile: item.dataset.mobile
                };

                if (this.multiple) {
                    // 多选模式
                    if (!this.selectedUsers.find(u => u.userid === user.userid)) {
                        this.selectedUsers.push(user);
                        this.updateSelectedDisplay();
                    }
                } else {
                    // 单选模式
                    this.selectedUsers = [user];
                    this.input.value = user.name;
                    this.updateSelectedDisplay();
                }

                this.hideDropdown();
                this.updateResult();
            }

            updateSelectedDisplay() {
                if (this.multiple && this.selectedUsers.length > 0) {
                    this.selectedContainer.innerHTML = this.selectedUsers.map(user => `
                        <span class="selected-user">
                            ${user.name}
                            <span class="remove-user" data-userid="${user.userid}">×</span>
                        </span>
                    `).join('');

                    // 绑定删除事件
                    this.selectedContainer.querySelectorAll('.remove-user').forEach(btn => {
                        btn.addEventListener('click', this.handleUserRemove.bind(this));
                    });
                } else {
                    this.selectedContainer.innerHTML = '';
                }
            }

            handleUserRemove(event) {
                const userid = event.target.dataset.userid;
                this.selectedUsers = this.selectedUsers.filter(u => u.userid !== userid);
                this.updateSelectedDisplay();
                this.updateResult();
            }

            showDropdown() {
                this.dropdown.style.display = 'block';
            }

            hideDropdown() {
                this.dropdown.style.display = 'none';
            }

            showError(message) {
                this.dropdown.innerHTML = `<div class="error">${message}</div>`;
                this.showDropdown();
            }

            updateResult() {
                // 更新结果显示
                if (this.input.id === 'executorPMInput') {
                    const pmSpan = document.getElementById('selectedPM');
                    pmSpan.textContent = this.selectedUsers.length > 0 ? 
                        this.selectedUsers[0].name : '未选择';
                } else if (this.input.id === 'contentMediaInput') {
                    const mediaSpan = document.getElementById('selectedMedia');
                    mediaSpan.textContent = this.selectedUsers.length > 0 ? 
                        this.selectedUsers.map(u => u.name).join(', ') : '未选择';
                }
            }

            getSelectedUsers() {
                return this.selectedUsers;
            }
        }

        // 初始化用户选择器
        document.addEventListener('DOMContentLoaded', function() {
            // 执行PM选择器（单选）
            const pmSelector = new UserSelector(
                'executorPMInput', 
                'executorPMDropdown', 
                'executorPMSelected', 
                false
            );

            // 内容媒介选择器（多选）
            const mediaSelector = new UserSelector(
                'contentMediaInput', 
                'contentMediaDropdown', 
                'contentMediaSelected', 
                true
            );

            // 全局访问
            window.pmSelector = pmSelector;
            window.mediaSelector = mediaSelector;
        });
    </script>
</body>
</html>
