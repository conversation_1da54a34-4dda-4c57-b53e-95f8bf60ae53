# 财务导出报表格式说明

## 项目汇总表新格式

根据您的需求，项目汇总表已经修改为层级结构显示，每个项目包含以下内容：

### 1. 项目主行（总项目行）
- **执行项目**: 显示为"总项目"
- **项目信息**: 包含完整的项目基本信息
  - 下单时间、品牌、品类
  - 项目名称、合同签署情况、合同类型
  - 项目规划金额、不含税金额、项目进度
  - 预计回款时间、回款周期、执行项目周期
  - 已回款额、未回款额、回款状态
  - 采购成本、返点、居间、毛利、毛利率
- **样式特点**: 
  - 浅蓝色背景 (#E8F4FD)
  - 加粗字体
  - 较大行高 (35px)

### 2. 供应商子行（周预算行）
每个项目下面会列出该项目的所有周预算记录，每条记录包含：

- **执行项目**: 显示为"供应商"
- **项目名称**: 与主行相同的项目名称
- **供应商信息**: 
  - 供应商公司名称
  - 服务内容（达人服务/投流服务/其他服务）
  - 供应商采购成本
  - 专票税率（专票1%/3%/6%/普票）
  - 不含税金额
- **付款信息**:
  - 已付金额、未付款金额
  - 6月应付、剩余成本
  - 7月预计付款、转后续月份
- **样式特点**:
  - 白色/浅灰色交替背景
  - 正常字体
  - 标准行高 (30px)
  - 供应商名称加粗显示

### 3. 数据示例结构

```
华帝2501站外推广 (总项目行)
├── 广州知效网络技术有限公司 - 达人抖音 (供应商行)
├── 对私-陈达明 - 其他华帝策略服务费用 (供应商行)  
├── 星立方-支付宝 - 达人抖音-费用可退 (供应商行)
├── 浙江磁力电子商务有限公司 - 投流内容热推 (供应商行)
└── 广州橙子网络科技有限公司 - 投流抖音cid (供应商行)
```

### 4. 字段映射说明

#### 项目主行字段来源：
- **下单时间**: project.createdAt
- **品牌**: brand.name  
- **品类**: brand.name (暂时使用品牌名称)
- **项目规划金额**: project.budget.planningBudget
- **毛利**: project.profit.profit
- **毛利率**: project.profit.grossMargin
- **居间**: project.cost.intermediaryCost

#### 供应商行字段来源：
- **供应商公司名称**: supplier.name
- **服务内容**: weeklyBudget.serviceType (转换为中文)
- **供应商采购成本**: weeklyBudget.contractAmount
- **专票税率**: weeklyBudget.taxRate (转换为中文)
- **已付金额**: weeklyBudget.paidAmount
- **未付款金额**: weeklyBudget.remainingAmount

### 5. 颜色和样式说明

- **项目主行**: 浅蓝色背景，突出显示项目信息
- **供应商行**: 白色/浅灰色交替，便于区分不同供应商
- **金额列**: 右对齐，千分位分隔符，正数绿色，负数红色
- **状态列**: 根据状态显示不同颜色（已完成-绿色，执行中-蓝色，已取消-红色）

### 6. 使用方法

1. 调用财务导出API
2. 指定年份和其他筛选条件
3. 生成的Excel文件包含三个工作表：
   - 项目汇总表（新格式）
   - 各品牌详情表
   - 品牌汇总表

### 7. 注意事项

- 如果项目没有周预算，只显示项目主行
- 供应商信息来自周预算关联的供应商表
- 月度应付金额按简单平均分配计算（可根据实际业务逻辑调整）
- 税率转换：6%税率对应94%不含税比例

这种格式更清晰地展示了项目与供应商的层级关系，便于财务人员查看每个项目的详细成本构成。
