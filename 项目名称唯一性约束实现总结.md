# 项目名称唯一性约束实现总结

## 🎯 问题背景

用户指出项目名称应该是唯一的，避免重复导入相同的项目。这是一个重要的数据完整性要求。

## 🔧 实现步骤

### 1. 数据库Schema更新
- 在 `prisma/schema.prisma` 中为 `projectName` 字段添加 `@unique` 约束
- 修改前：`projectName String @db.VarChar(200)`
- 修改后：`projectName String @unique @db.VarChar(200)`

### 2. 处理现有重复数据
由于数据库中已存在重复的项目名称，需要先清理数据：

#### 创建修复脚本 (`scripts/fix-duplicate-projects.sql`)
```sql
-- 为重复的项目名称添加序号后缀
WITH duplicate_projects AS (
    SELECT 
        id,
        "projectName",
        ROW_NUMBER() OVER (PARTITION BY "projectName" ORDER BY "createdAt") as rn
    FROM projects
    WHERE "projectName" IN (
        SELECT "projectName" 
        FROM projects 
        GROUP BY "projectName" 
        HAVING COUNT(*) > 1
    )
)
UPDATE projects 
SET "projectName" = CASE 
    WHEN dp.rn = 1 THEN dp."projectName"
    ELSE dp."projectName" || ' (' || dp.rn || ')'
END
FROM duplicate_projects dp
WHERE projects.id = dp.id;
```

#### 执行修复
- 执行SQL脚本清理重复数据
- 为重复项目添加序号后缀，如：
  - `松下洗护2505站外推广-小红书` (保持原名)
  - `松下洗护2505站外推广-小红书 (2)` (第二个重复项)
  - `松下洗护2505站外推广-小红书 (3)` (第三个重复项)

### 3. 添加数据库约束
```sql
-- 手动添加唯一性约束
ALTER TABLE projects ADD CONSTRAINT projects_projectName_key UNIQUE ("projectName");
```

### 4. 更新应用代码

#### 项目导入逻辑 (`src/controllers/projectImport.ts`)
在创建项目之前添加重复检查：
```typescript
// 验证项目名称是否重复（项目名称必须唯一）
const existingProject = await this.db.client.project.findFirst({
  where: { projectName: projectData.projectName }
});

if (existingProject) {
  result.errors.push({
    row: rowNumber,
    field: 'projectName',
    message: `项目名称已存在: ${projectData.projectName}，项目名称必须唯一`,
    data: row
  });
  result.failureCount++;
  continue;
}
```

#### 项目更新导入逻辑
优化项目匹配逻辑，优先使用精确匹配：
```typescript
// 查找项目 - 由于项目名称现在是唯一的，优先使用精确匹配
let existingProject = await this.db.client.project.findFirst({
  where: { projectName: row.projectName.trim() }
});

// 如果精确匹配失败，尝试模糊匹配
if (!existingProject) {
  // 模糊匹配逻辑...
}
```

## 📊 影响范围

### 1. 数据库层面
- ✅ 项目表添加了 `projectName` 唯一性约束
- ✅ 清理了现有的重复数据
- ✅ 防止未来插入重复的项目名称

### 2. 应用层面
- ✅ 项目导入时会检查名称重复
- ✅ 项目更新导入优化了匹配逻辑
- ✅ 提供清晰的错误提示

### 3. 用户体验
- ✅ 导入重复项目名称时会收到明确的错误提示
- ✅ 现有重复项目通过添加序号区分
- ✅ 项目更新导入更加精确

## 🔍 验证方法

### 1. 检查数据库约束
```sql
-- 查看约束是否存在
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name = 'projects' AND constraint_type = 'UNIQUE';
```

### 2. 测试重复导入
尝试导入相同项目名称的项目，应该收到错误提示。

### 3. 测试项目更新导入
使用精确的项目名称进行更新导入，应该能够准确匹配。

## 📝 注意事项

1. **现有数据**: 已处理的重复项目名称会有序号后缀
2. **导入验证**: 新的项目导入会严格检查名称唯一性
3. **更新匹配**: 项目更新导入优先使用精确匹配
4. **错误提示**: 提供清晰的重复名称错误信息

## 🎉 完成状态

✅ 数据库Schema已更新
✅ 现有重复数据已清理
✅ 唯一性约束已添加
✅ 应用代码已更新
✅ 导入逻辑已优化
✅ 文档已更新

项目名称唯一性约束现在已完全实现，确保了数据的完整性和一致性。
