# 项目更新导入功能说明

## 功能概述

新增的项目更新导入功能允许通过Excel/CSV文件批量更新现有项目的以下字段：
1. **项目总规划预算** (`planningBudget`)
2. **已回款金额** (通过更新项目收入记录)
3. **达人返点** (`estimatedInfluencerRebate`)

## API接口

### 项目更新导入
```
POST /api/projects/update-import
```

**权限要求**: `project.update`

**请求方式**: `multipart/form-data` (文件上传)

**查询参数**:
- `dryRun`: boolean (可选) - 仅验证，不实际更新，默认为 false

**文件格式**: CSV (暂时只支持CSV格式)

## CSV文件格式

### 必需字段
- **项目名称**: 用于匹配现有项目（项目名称在系统中是唯一的）

### 可选字段
- **品牌名称**: 辅助匹配项目（可选）
- **项目总规划预算**: 如"591.3万"
- **已回款**: 如"59.9万"
- **达人返点**: 如"10万"

### CSV示例
```csv
项目名称,品牌名称,项目总规划预算,已回款,达人返点
华帝2501站外推广,华帝,600万,100万,15万
小米春节营销活动,小米,300万,150万,20万
```

### 字段映射支持
系统支持以下中英文字段名：

| 字段 | 支持的列名 |
|------|-----------|
| 项目名称 | projectName, 项目名称, 项目 |
| 品牌名称 | brandName, 品牌名称, 品牌 |
| 项目总规划预算 | planningBudget, 项目总规划预算, 规划预算, 预算 |
| 已回款 | receivedAmount, 已回款, 已收款, 回款金额 |
| 达人返点 | estimatedInfluencerRebate, 达人返点, 返点 |

## 更新逻辑

### 1. 项目匹配
- 首先通过项目名称进行精确匹配（项目名称是唯一的）
- 如果精确匹配失败，尝试模糊匹配
- 如果提供了品牌名称，会同时匹配品牌
- 如果找不到匹配的项目，会记录错误

### 2. 项目总规划预算更新
- 直接更新项目表中的 `planningBudget` 字段
- 支持"万"单位自动转换

### 3. 达人返点更新
- 直接更新项目表中的 `estimatedInfluencerRebate` 字段
- 支持"万"单位自动转换

### 4. 已回款金额更新
- 更新项目的收入记录，而不是项目表字段
- 根据新的已回款金额调整收入记录的状态：
  - **增加回款**: 将收款中的记录改为已收款，或创建新的已收款记录
  - **减少回款**: 将部分已收款记录改为收款中
  - **清零回款**: 将所有已收款记录改为收款中

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    "success": true,
    "totalRows": 2,
    "successCount": 2,
    "failureCount": 0,
    "errors": [],
    "updatedProjects": [
      {
        "row": 2,
        "projectId": "proj_123",
        "projectName": "华帝2501站外推广",
        "changes": {
          "planningBudget": {
            "old": 5913000,
            "new": 6000000
          },
          "estimatedInfluencerRebate": {
            "old": 57000,
            "new": 150000
          },
          "receivedAmount": {
            "old": 599000,
            "new": 1000000
          }
        }
      }
    ],
    "warnings": [
      {
        "row": 3,
        "message": "项目 小米春节营销活动 没有需要更新的字段"
      }
    ]
  },
  "message": "项目更新导入完成"
}
```

### 错误响应
```json
{
  "success": true,
  "data": {
    "success": false,
    "totalRows": 1,
    "successCount": 0,
    "failureCount": 1,
    "errors": [
      {
        "row": 2,
        "field": "projectName",
        "message": "未找到匹配的项目: 不存在的项目名称",
        "data": {
          "projectName": "不存在的项目名称",
          "planningBudget": "100万"
        }
      }
    ],
    "updatedProjects": [],
    "warnings": []
  },
  "message": "项目更新导入完成"
}
```

## 使用示例

### 1. 试运行（仅验证）
```bash
curl -X POST "http://localhost:3000/api/projects/update-import?dryRun=true" \
  -H "Authorization: Bearer your-jwt-token" \
  -F "file=@project-updates.csv"
```

### 2. 正式更新
```bash
curl -X POST "http://localhost:3000/api/projects/update-import" \
  -H "Authorization: Bearer your-jwt-token" \
  -F "file=@project-updates.csv"
```

## 注意事项

1. **权限要求**: 需要 `project.update` 权限
2. **文件格式**: 暂时只支持CSV格式
3. **编码格式**: 建议使用UTF-8编码
4. **项目匹配**: 项目名称必须能够匹配到现有项目（项目名称在系统中是唯一的）
5. **金额格式**: 支持"万"单位，如"100万"会转换为1000000
6. **试运行**: 建议先使用 `dryRun=true` 验证数据
7. **变更记录**: 只有实际发生变化的字段才会被更新和记录

## 扩展性

如果后续需要添加更多可更新的字段，可以：
1. 在 `ExcelProjectUpdateRow` 接口中添加新字段
2. 在 `parseUpdateCSVFile` 方法的字段映射中添加新字段
3. 在 `processProjectUpdates` 方法中添加新字段的处理逻辑
4. 更新响应接口中的 `changes` 类型定义

这样的设计使得功能具有良好的可扩展性，可以根据需要轻松添加新的可更新字段。
