# 批量创建资金计划 API 文档

## 接口概述

批量创建资金计划接口允许一次性创建多个资金计划，支持事务处理，确保数据一致性和准确性。

## 接口信息

- **端点**: `POST /api/funding-plans/batch`
- **权限**: 需要 `BUDGET_CREATE` 权限
- **认证**: 需要 JWT 认证

## 请求格式

### 请求头
```http
Content-Type: application/json
Authorization: Bearer <your_jwt_token>
```

### 请求体
```json
{
  "plans": [
    {
      "title": "资金计划标题",
      "year": 2024,
      "month": 12,
      "weekOfMonth": 1,
      "plannedAmount": 10000,
      "remarks": "备注信息（可选）",
      "budgetId": "预算ID"
    }
  ]
}
```

### 参数说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| plans | Array | ✅ | 资金计划数组，最少1个，最多50个 |
| plans[].title | String | ✅ | 计划标题，最大200字符 |
| plans[].year | Integer | ✅ | 年份，范围：2020-2050 |
| plans[].month | Integer | ✅ | 月份，范围：1-12 |
| plans[].weekOfMonth | Integer | ✅ | 当月第几周，范围：1-5 |
| plans[].plannedAmount | Number | ✅ | 计划金额，必须大于0 |
| plans[].remarks | String | ❌ | 备注信息 |
| plans[].budgetId | String | ✅ | 关联的预算ID |

## 响应格式

### 成功响应 (200)
```json
{
  "success": true,
  "data": {
    "successCount": 2,
    "failedCount": 1,
    "createdPlans": [
      {
        "id": "funding_plan_id_1",
        "title": "资金计划1",
        "year": 2024,
        "month": 12,
        "weekOfMonth": 1,
        "plannedAmount": 10000,
        "paidAmount": 0,
        "remainingAmount": 10000,
        "status": "draft",
        "approvalStatus": "none",
        "budgetId": "budget_id_1",
        "createdAt": "2024-12-05T10:00:00Z",
        "updatedAt": "2024-12-05T10:00:00Z"
      }
    ],
    "errors": [
      {
        "index": 2,
        "error": "预算余额不足，当前可用金额：5000，需要：15000",
        "plan": {
          "title": "资金计划3",
          "budgetId": "budget_id_2"
        }
      }
    ]
  },
  "message": "成功创建 2 个资金计划"
}
```

### 错误响应

#### 400 - 请求参数验证失败
```json
{
  "error": "请求参数验证失败",
  "details": [
    {
      "code": "invalid_type",
      "expected": "number",
      "received": "string",
      "path": ["plans", 0, "plannedAmount"],
      "message": "Expected number, received string"
    }
  ]
}
```

#### 400 - 预算余额不足
```json
{
  "error": "预算余额不足",
  "message": "预算余额不足，当前可用金额：5000，需要：15000"
}
```

#### 400 - 资金计划冲突
```json
{
  "error": "资金计划冲突",
  "message": "批量创建中存在重复的预算年月周组合"
}
```

#### 401 - 用户未认证
```json
{
  "error": "用户未认证"
}
```

#### 403 - 权限不足
```json
{
  "success": false,
  "message": "权限不足",
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

## 业务规则

### 1. 数据验证
- 每次最多创建50个资金计划
- 同一预算下的年月周组合必须唯一
- 批量创建中不能有重复的年月周组合

### 2. 预算验证
- 验证预算是否存在
- 检查预算可用金额是否足够
- 计算公式：可用金额 = 预算合同金额 - 已有资金计划总额

### 3. 事务处理
- 使用数据库事务确保数据一致性
- 要么全部成功，要么全部失败（针对预算验证）
- 单个计划失败不影响其他计划的创建

### 4. 错误处理
- 提供详细的错误信息和索引位置
- 区分验证错误和业务逻辑错误
- 支持部分成功的场景

## 使用示例

### cURL 示例
```bash
curl -X POST http://localhost:3000/api/funding-plans/batch \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "plans": [
      {
        "title": "Q4营销预算-第1周",
        "year": 2024,
        "month": 12,
        "weekOfMonth": 1,
        "plannedAmount": 50000,
        "remarks": "年末营销活动预算",
        "budgetId": "budget_marketing_q4"
      },
      {
        "title": "Q4营销预算-第2周",
        "year": 2024,
        "month": 12,
        "weekOfMonth": 2,
        "plannedAmount": 60000,
        "remarks": "双十二活动预算",
        "budgetId": "budget_marketing_q4"
      }
    ]
  }'
```

### JavaScript 示例
```javascript
const response = await fetch('/api/funding-plans/batch', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    plans: [
      {
        title: '技术开发预算-第1周',
        year: 2024,
        month: 12,
        weekOfMonth: 1,
        plannedAmount: 80000,
        budgetId: 'budget_tech_dev'
      }
    ]
  })
});

const result = await response.json();
console.log('创建结果:', result);
```

## 注意事项

1. **性能考虑**: 建议单次批量创建不超过20个计划
2. **并发控制**: 避免同时对同一预算进行批量操作
3. **错误恢复**: 失败的计划可以单独重试或修正后重新批量创建
4. **审计日志**: 所有创建操作都会记录审计日志
5. **权限检查**: 确保用户有足够的权限访问相关预算

## 相关接口

- `POST /api/funding-plans` - 单个创建资金计划
- `GET /api/funding-plans` - 获取资金计划列表
- `PUT /api/funding-plans/:id` - 更新资金计划
- `DELETE /api/funding-plans/:id` - 删除资金计划
