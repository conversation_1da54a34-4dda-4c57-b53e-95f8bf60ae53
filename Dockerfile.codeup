# 针对阿里云 Codeup 云效平台优化的 Dockerfile
# -------------------------
# 第一阶段：安装 + 构建
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

WORKDIR /app

# 1) 配置阿里云镜像源和编译环境（云效平台会缓存此层）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update \
    && apk add --no-cache python3 make g++ \
    && npm config set registry=https://registry.npmmirror.com \
    && npm config set disturl=https://npmmirror.com/dist \
    && npm config set electron_mirror=https://npmmirror.com/mirrors/electron/ \
    && npm config set puppeteer_download_host=https://npmmirror.com/mirrors \
    && npm install -g pnpm --registry=https://registry.npmmirror.com \
    && pnpm config set registry https://registry.npmmirror.com

# 2) 复制依赖文件（云效会基于文件内容哈希缓存）
COPY package.json pnpm-lock.yaml ./

# 3) 安装依赖（依赖不变时云效会直接使用缓存层）
RUN pnpm install --frozen-lockfile

# 4) 复制 Prisma schema 并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 5) 复制源码并构建（源码变化最频繁，放最后）
COPY . .
RUN pnpm run build

# -------------------------
# 第二阶段：运行时环境
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

WORKDIR /app

# 1) 配置运行时环境（云效会缓存系统配置）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update \
    && apk add --no-cache tzdata curl dumb-init \
    && npm config set registry=https://registry.npmmirror.com \
    && npm install -g pnpm --registry=https://registry.npmmirror.com \
    && pnpm config set registry https://registry.npmmirror.com

# 2) 复制依赖配置
COPY package.json pnpm-lock.yaml ./

# 3) 安装生产依赖（云效缓存优化）
RUN pnpm install --prod --frozen-lockfile

# 4) 复制 Prisma schema 并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 5) 复制构建产物和运行时文件
COPY --from=builder /app/dist ./dist
COPY start.sh .env .env.production ./

# 6) 创建用户和设置权限
RUN addgroup -S nodejs \
    && adduser -S -G nodejs nodejs \
    && chown -R nodejs:nodejs /app \
    && chmod +x start.sh

USER nodejs

# 7) 运行时配置
EXPOSE 3000
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

ENV NODE_ENV=production \
    TZ=Asia/Shanghai

ENTRYPOINT ["dumb-init", "--"]
CMD ["./start.sh"]