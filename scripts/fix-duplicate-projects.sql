-- 修复重复的项目名称
-- 为重复的项目名称添加序号后缀

-- 首先备份原始数据（可选）
-- CREATE TABLE projects_backup AS SELECT * FROM projects;

-- 为重复的项目名称添加序号
WITH duplicate_projects AS (
    SELECT 
        id,
        "projectName",
        ROW_NUMBER() OVER (PARTITION BY "projectName" ORDER BY "createdAt") as rn
    FROM projects
    WHERE "projectName" IN (
        SELECT "projectName" 
        FROM projects 
        GROUP BY "projectName" 
        HAVING COUNT(*) > 1
    )
)
UPDATE projects 
SET "projectName" = CASE 
    WHEN dp.rn = 1 THEN dp."projectName"
    ELSE dp."projectName" || ' (' || dp.rn || ')'
END
FROM duplicate_projects dp
WHERE projects.id = dp.id;

-- 验证修复结果
SELECT 
    "projectName",
    COUNT(*) as count
FROM projects 
GROUP BY "projectName" 
HAVING COUNT(*) > 1;
