// 测试项目变更记录 API 接口
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// 模拟JWT token（在实际使用中需要真实的认证token）
const MOCK_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyaWQiOiJ0ZXN0LXVzZXIiLCJuYW1lIjoi5rWL6K-V55So5oi3IiwiaWF0IjoxNzM0ODQ5NjAwfQ.mock-signature';

async function testChangeLogAPI() {
  try {
    console.log('🧪 开始测试项目变更记录 API...');
    
    // 1. 测试获取变更记录列表
    console.log('\n📋 测试获取变更记录列表...');
    try {
      const response = await fetch(`${BASE_URL}/change-logs?page=1&pageSize=10`, {
        headers: {
          'Authorization': `Bearer ${MOCK_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 变更记录列表获取成功');
        console.log(`   总记录数: ${data.data?.total || 0}`);
        console.log(`   当前页记录数: ${data.data?.changeLogs?.length || 0}`);
      } else {
        console.log(`❌ 获取变更记录列表失败: ${response.status} ${response.statusText}`);
        const errorData = await response.text();
        console.log(`   错误详情: ${errorData}`);
      }
    } catch (error) {
      console.log('❌ 请求失败:', error.message);
    }
    
    // 2. 测试获取项目变更记录（需要一个真实的项目ID）
    console.log('\n📋 测试获取项目变更记录...');
    try {
      // 先获取一个项目ID
      const projectsResponse = await fetch(`${BASE_URL}/projects?page=1&pageSize=1`, {
        headers: {
          'Authorization': `Bearer ${MOCK_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (projectsResponse.ok) {
        const projectsData = await projectsResponse.json();
        if (projectsData.data?.projects?.length > 0) {
          const projectId = projectsData.data.projects[0].id;
          console.log(`   使用项目ID: ${projectId}`);
          
          const response = await fetch(`${BASE_URL}/projects/${projectId}/change-logs?page=1&pageSize=10`, {
            headers: {
              'Authorization': `Bearer ${MOCK_TOKEN}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (response.ok) {
            const data = await response.json();
            console.log('✅ 项目变更记录获取成功');
            console.log(`   项目变更记录数: ${data.data?.changeLogs?.length || 0}`);
          } else {
            console.log(`❌ 获取项目变更记录失败: ${response.status} ${response.statusText}`);
          }
        } else {
          console.log('⚠️ 没有找到项目，跳过项目变更记录测试');
        }
      } else {
        console.log('⚠️ 无法获取项目列表，跳过项目变更记录测试');
      }
    } catch (error) {
      console.log('❌ 请求失败:', error.message);
    }
    
    // 3. 测试创建项目（这会自动生成变更记录）
    console.log('\n📝 测试创建项目（自动生成变更记录）...');
    try {
      const createProjectData = {
        documentType: 'PROJECT_INITIATION',
        brandId: 'test-brand-001', // 需要一个真实的品牌ID
        projectName: `测试项目-${Date.now()}`,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        planningBudget: 100000,
        influencerBudget: 50000,
        adBudget: 30000,
        otherBudget: 20000,
        influencerCost: 45000,
        adCost: 25000,
        otherCost: 15000,
        estimatedInfluencerRebate: 5000,
        executorPM: 'test-user-001',
        contentMediaIds: ['test-user-002'],
        contractType: 'SINGLE',
        settlementRules: '测试结算规则',
        kpi: '测试KPI'
      };
      
      const response = await fetch(`${BASE_URL}/projects`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${MOCK_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(createProjectData)
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 项目创建成功（应该已自动生成变更记录）');
        console.log(`   项目ID: ${data.data?.id}`);
        
        // 等待一下，然后查询该项目的变更记录
        setTimeout(async () => {
          try {
            const changeLogResponse = await fetch(`${BASE_URL}/projects/${data.data.id}/change-logs`, {
              headers: {
                'Authorization': `Bearer ${MOCK_TOKEN}`,
                'Content-Type': 'application/json'
              }
            });
            
            if (changeLogResponse.ok) {
              const changeLogData = await changeLogResponse.json();
              console.log(`✅ 新项目的变更记录: ${changeLogData.data?.changeLogs?.length || 0} 条`);
              if (changeLogData.data?.changeLogs?.length > 0) {
                const firstLog = changeLogData.data.changeLogs[0];
                console.log(`   最新变更: ${firstLog.changeTitle} (${firstLog.changeType})`);
              }
            }
          } catch (error) {
            console.log('❌ 查询新项目变更记录失败:', error.message);
          }
        }, 1000);
        
      } else {
        const errorData = await response.text();
        console.log(`❌ 项目创建失败: ${response.status} ${response.statusText}`);
        console.log(`   错误详情: ${errorData}`);
      }
    } catch (error) {
      console.log('❌ 请求失败:', error.message);
    }
    
    console.log('\n🎉 API 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testChangeLogAPI();
