/**
 * 数据迁移脚本：将周预算重构为预算和资金计划
 * 
 * 这个脚本将：
 * 1. 将现有的 weekly_budgets 数据迁移到新的 budgets 表
 * 2. 为每个预算创建对应的资金计划记录
 * 3. 更新审批实例的关联关系
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('开始数据迁移...');

  try {
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      console.log('1. 获取现有周预算数据...');
      
      // 获取所有现有的周预算数据
      const weeklyBudgets = await tx.$queryRaw`
        SELECT * FROM weekly_budgets ORDER BY "createdAt"
      `;

      console.log(`找到 ${weeklyBudgets.length} 条周预算记录`);

      if (weeklyBudgets.length === 0) {
        console.log('没有需要迁移的数据');
        return;
      }

      console.log('2. 创建预算记录...');
      
      // 按项目分组周预算，每个项目的同类型服务创建一个预算
      const budgetGroups = new Map();
      
      for (const wb of weeklyBudgets) {
        const key = `${wb.projectId}-${wb.serviceType}-${wb.supplierId || 'null'}`;
        
        if (!budgetGroups.has(key)) {
          budgetGroups.set(key, {
            projectId: wb.projectId,
            serviceType: wb.serviceType,
            supplierId: wb.supplierId,
            weeklyBudgets: []
          });
        }
        
        budgetGroups.get(key).weeklyBudgets.push(wb);
      }

      console.log(`将创建 ${budgetGroups.size} 个预算记录`);

      const budgetMappings = new Map(); // 存储 weeklyBudgetId -> budgetId 的映射

      // 为每个分组创建预算记录
      for (const [key, group] of budgetGroups) {
        const firstWB = group.weeklyBudgets[0];
        
        // 计算总合同金额和总已付金额
        const totalContractAmount = group.weeklyBudgets.reduce(
          (sum, wb) => sum + parseFloat(wb.contractAmount), 0
        );
        const totalPaidAmount = group.weeklyBudgets.reduce(
          (sum, wb) => sum + parseFloat(wb.paidAmount), 0
        );

        // 创建预算记录
        const budget = await tx.budget.create({
          data: {
            title: `${firstWB.title} - 预算`,
            serviceType: firstWB.serviceType,
            serviceContent: firstWB.serviceContent,
            remarks: firstWB.remarks,
            contractAmount: totalContractAmount,
            taxRate: firstWB.taxRate,
            totalPaidAmount: totalPaidAmount,
            remainingAmount: totalContractAmount - totalPaidAmount,
            status: 'CREATED', // 对应新的 BudgetStatus
            projectId: firstWB.projectId,
            supplierId: firstWB.supplierId,
            createdBy: firstWB.createdBy,
            updatedBy: firstWB.updatedBy,
            createdAt: firstWB.createdAt,
            updatedAt: firstWB.updatedAt
          }
        });

        console.log(`创建预算: ${budget.title} (ID: ${budget.id})`);

        // 为每个周预算创建资金计划
        for (const wb of group.weeklyBudgets) {
          // 从周预算的创建时间推算年月周
          const createdDate = new Date(wb.createdAt);
          const year = createdDate.getFullYear();
          const month = createdDate.getMonth() + 1;
          
          // 简单的周计算：按月内的周数
          const dayOfMonth = createdDate.getDate();
          const weekOfMonth = Math.ceil(dayOfMonth / 7);

          const fundingPlan = await tx.fundingPlan.create({
            data: {
              title: wb.title,
              year: year,
              month: month,
              weekOfMonth: weekOfMonth,
              plannedAmount: parseFloat(wb.contractAmount),
              paidAmount: parseFloat(wb.paidAmount),
              remainingAmount: parseFloat(wb.remainingAmount),
              status: mapWeeklyBudgetStatusToFundingPlanStatus(wb.status),
              approvalStatus: wb.approvalStatus,
              approvalAmount: wb.approvalAmount ? parseFloat(wb.approvalAmount) : null,
              approvalReason: wb.approvalReason,
              remarks: wb.remarks,
              budgetId: budget.id,
              createdBy: wb.createdBy,
              updatedBy: wb.updatedBy,
              createdAt: wb.createdAt,
              updatedAt: wb.updatedAt
            }
          });

          // 记录映射关系
          budgetMappings.set(wb.id, {
            budgetId: budget.id,
            fundingPlanId: fundingPlan.id
          });

          console.log(`创建资金计划: ${fundingPlan.title} (${year}年${month}月第${weekOfMonth}周)`);
        }
      }

      console.log('3. 更新审批实例关联关系...');
      
      // 更新审批实例，将 weeklyBudgetId 改为 fundingPlanId
      const approvalInstances = await tx.$queryRaw`
        SELECT * FROM approval_instances WHERE "weeklyBudgetId" IS NOT NULL
      `;

      console.log(`找到 ${approvalInstances.length} 条审批实例需要更新`);

      for (const approval of approvalInstances) {
        const mapping = budgetMappings.get(approval.weeklyBudgetId);
        if (mapping) {
          await tx.$executeRaw`
            UPDATE approval_instances 
            SET "fundingPlanId" = ${mapping.fundingPlanId}
            WHERE id = ${approval.id}
          `;
          console.log(`更新审批实例 ${approval.id} 的关联关系`);
        }
      }

      console.log('4. 数据迁移完成！');
      console.log(`- 创建了 ${budgetGroups.size} 个预算记录`);
      console.log(`- 创建了 ${weeklyBudgets.length} 个资金计划记录`);
      console.log(`- 更新了 ${approvalInstances.length} 个审批实例`);
    });

  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * 将周预算状态映射到资金计划状态
 */
function mapWeeklyBudgetStatusToFundingPlanStatus(weeklyBudgetStatus) {
  const statusMap = {
    'CREATED': 'DRAFT',
    'APPROVED': 'APPROVED', 
    'EXECUTING': 'EXECUTING',
    'COMPLETED': 'COMPLETED',
    'CANCELLED': 'CANCELLED'
  };
  
  return statusMap[weeklyBudgetStatus] || 'DRAFT';
}

// 运行迁移
main()
  .then(() => {
    console.log('✅ 数据迁移成功完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 数据迁移失败:', error);
    process.exit(1);
  });
