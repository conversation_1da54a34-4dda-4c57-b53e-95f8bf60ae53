#!/usr/bin/env node

/**
 * 钉钉消息通知功能测试脚本
 * 用于验证通知功能是否正常工作
 */

import { runAllNotificationTests } from '../src/tests/notification.test.js';

console.log('🔔 钉钉消息通知功能测试');
console.log('========================\n');

// 运行测试
runAllNotificationTests()
  .then(() => {
    console.log('\n✨ 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 测试执行失败:', error);
    process.exit(1);
  });
