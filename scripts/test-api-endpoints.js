#!/usr/bin/env node

/**
 * API接口测试脚本
 * 用于验证所有API接口是否正常工作
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');

// 配置
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_URL = 'https://example.com/test';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求函数
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const req = client.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: data
                    });
                }
            });
        });

        req.on('error', reject);

        if (options.body) {
            req.write(JSON.stringify(options.body));
        }

        req.end();
    });
}

// 测试用例
const testCases = [
    {
        name: '健康检查',
        method: 'GET',
        path: '/api/health',
        expectedStatus: 200
    },
    {
        name: '获取应用配置',
        method: 'GET',
        path: '/api/app/config',
        expectedStatus: 200
    },
    {
        name: '获取JSAPI签名',
        method: 'GET',
        path: `/api/auth/jsapi-signature?url=${encodeURIComponent(TEST_URL)}`,
        expectedStatus: 200
    },
    {
        name: '获取部门列表',
        method: 'GET',
        path: '/api/auth/departments',
        expectedStatus: 200
    },
    {
        name: '获取增强版JSAPI签名',
        method: 'GET',
        path: `/api/app/jsapi-signature/enhanced?url=${encodeURIComponent(TEST_URL)}`,
        expectedStatus: 200
    },
    {
        name: '获取应用统计',
        method: 'GET',
        path: '/api/app/stats',
        expectedStatus: 200
    },
    {
        name: '获取用户信息（需要授权码）',
        method: 'POST',
        path: '/api/auth/user-info',
        body: { authCode: 'test_auth_code' },
        expectedStatus: [200, 400, 500] // 可能因为无效授权码而失败
    }
];

// 运行测试
async function runTests() {
    log('🚀 开始API接口测试...', 'cyan');
    log(`📍 测试服务器: ${BASE_URL}`, 'blue');
    log('', 'reset');

    let passed = 0;
    let failed = 0;

    for (const testCase of testCases) {
        try {
            log(`🧪 测试: ${testCase.name}`, 'yellow');
            
            const url = `${BASE_URL}${testCase.path}`;
            const options = {
                method: testCase.method,
                body: testCase.body
            };

            const startTime = Date.now();
            const response = await makeRequest(url, options);
            const duration = Date.now() - startTime;

            const expectedStatuses = Array.isArray(testCase.expectedStatus) 
                ? testCase.expectedStatus 
                : [testCase.expectedStatus];

            if (expectedStatuses.includes(response.status)) {
                log(`   ✅ 成功 - 状态码: ${response.status}, 耗时: ${duration}ms`, 'green');
                
                if (response.data && typeof response.data === 'object') {
                    if (response.data.success !== undefined) {
                        log(`   📊 响应: success=${response.data.success}, message="${response.data.message || 'N/A'}"`, 'blue');
                    }
                    
                    // 显示关键数据
                    if (testCase.name === '获取应用配置' && response.data.data) {
                        const config = response.data.data;
                        log(`   🔧 配置: AgentId=${config.agentId || 'N/A'}, CorpId=${config.corpId || 'N/A'}`, 'blue');
                    }
                    
                    if (testCase.name.includes('JSAPI签名') && response.data.data) {
                        const sig = response.data.data;
                        log(`   🔐 签名: ${sig.signature ? sig.signature.substring(0, 10) + '...' : 'N/A'}`, 'blue');
                    }
                }
                
                passed++;
            } else {
                log(`   ❌ 失败 - 期望状态码: ${expectedStatuses.join('/')}, 实际: ${response.status}`, 'red');
                if (response.data && response.data.message) {
                    log(`   💬 错误信息: ${response.data.message}`, 'red');
                }
                failed++;
            }
        } catch (error) {
            log(`   ❌ 请求失败: ${error.message}`, 'red');
            failed++;
        }
        
        log('', 'reset');
    }

    // 测试总结
    log('📊 测试总结:', 'cyan');
    log(`   ✅ 通过: ${passed}`, 'green');
    log(`   ❌ 失败: ${failed}`, 'red');
    log(`   📈 成功率: ${((passed / (passed + failed)) * 100).toFixed(1)}%`, 'blue');

    if (failed === 0) {
        log('🎉 所有测试通过！', 'green');
        process.exit(0);
    } else {
        log('⚠️  部分测试失败，请检查服务器状态和配置', 'yellow');
        process.exit(1);
    }
}

// 检查服务器是否运行
async function checkServer() {
    try {
        log('🔍 检查服务器状态...', 'yellow');
        const response = await makeRequest(`${BASE_URL}/api/health`);
        
        if (response.status === 200) {
            log('✅ 服务器运行正常', 'green');
            return true;
        } else {
            log(`❌ 服务器响应异常: ${response.status}`, 'red');
            return false;
        }
    } catch (error) {
        log(`❌ 无法连接到服务器: ${error.message}`, 'red');
        log('💡 请确保服务器已启动并运行在正确的端口', 'yellow');
        return false;
    }
}

// 主函数
async function main() {
    log('🔧 钉钉API接口测试工具', 'magenta');
    log('================================', 'magenta');
    log('', 'reset');

    const serverOk = await checkServer();
    if (!serverOk) {
        process.exit(1);
    }

    log('', 'reset');
    await runTests();
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🔧 钉钉API接口测试工具

用法:
  node test-api-endpoints.js [选项]

选项:
  --help, -h     显示帮助信息
  
环境变量:
  API_BASE_URL   API服务器地址 (默认: http://localhost:3000)

示例:
  node test-api-endpoints.js
  API_BASE_URL=http://localhost:8080 node test-api-endpoints.js
`);
    process.exit(0);
}

// 运行测试
main().catch(error => {
    log(`💥 测试运行失败: ${error.message}`, 'red');
    process.exit(1);
});
