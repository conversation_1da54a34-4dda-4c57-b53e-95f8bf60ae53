// 验证项目变更记录表是否存在的脚本
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyChangeLogTable() {
  try {
    console.log('🔍 检查项目变更记录表...');
    
    // 检查表是否存在
    const result = await prisma.$queryRaw`
      SELECT table_name, column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'project_change_logs'
      ORDER BY ordinal_position;
    `;
    
    if (result.length === 0) {
      console.log('❌ project_change_logs 表不存在');
      return false;
    }
    
    console.log('✅ project_change_logs 表已存在');
    console.log('📋 表结构:');
    console.table(result);
    
    // 检查枚举类型是否存在
    const enumResult = await prisma.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'change_type'
      );
    `;
    
    console.log('🏷️ ChangeType 枚举值:');
    console.table(enumResult);
    
    // 检查索引是否存在
    const indexResult = await prisma.$queryRaw`
      SELECT indexname, indexdef
      FROM pg_indexes 
      WHERE tablename = 'project_change_logs';
    `;
    
    console.log('📊 表索引:');
    console.table(indexResult);
    
    return true;
  } catch (error) {
    console.error('❌ 检查失败:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

verifyChangeLogTable();
