#!/usr/bin/env node

/**
 * 数据库功能测试脚本
 * 测试PostgreSQL数据库的基本功能
 */

import { PrismaClient } from '@prisma/client';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

const prisma = new PrismaClient();

async function testDatabaseConnection() {
    log('🔌 测试数据库连接...', 'yellow');
    
    try {
        await prisma.$connect();
        log('✅ 数据库连接成功', 'green');
        return true;
    } catch (error) {
        log(`❌ 数据库连接失败: ${error.message}`, 'red');
        return false;
    }
}

async function testBrandOperations() {
    log('🏷️  测试品牌操作...', 'yellow');
    
    try {
        // 创建测试品牌
        const brand = await prisma.brand.create({
            data: {
                name: `测试品牌_${Date.now()}`,
                description: '这是一个测试品牌',
                status: 'ACTIVE',
                createdBy: 'test-user'
            }
        });
        
        log(`   ✅ 创建品牌成功: ${brand.name} (ID: ${brand.id})`, 'green');
        
        // 查询品牌
        const foundBrand = await prisma.brand.findUnique({
            where: { id: brand.id }
        });
        
        if (foundBrand) {
            log(`   ✅ 查询品牌成功: ${foundBrand.name}`, 'green');
        } else {
            log('   ❌ 查询品牌失败', 'red');
        }
        
        // 更新品牌
        const updatedBrand = await prisma.brand.update({
            where: { id: brand.id },
            data: {
                description: '更新后的品牌描述'
            }
        });
        
        log(`   ✅ 更新品牌成功: ${updatedBrand.description}`, 'green');
        
        return brand.id;
    } catch (error) {
        log(`   ❌ 品牌操作失败: ${error.message}`, 'red');
        return null;
    }
}

async function testProjectOperations(brandId) {
    log('📊 测试项目操作...', 'yellow');
    
    if (!brandId) {
        log('   ⚠️  跳过项目测试，品牌创建失败', 'yellow');
        return null;
    }
    
    try {
        // 创建测试项目
        const project = await prisma.project.create({
            data: {
                documentType: 'PROJECT_INITIATION',
                brandId: brandId,
                projectName: `测试项目_${Date.now()}`,
                startDate: new Date('2024-02-01'),
                endDate: new Date('2024-02-29'),
                planningBudget: 100000.00,
                influencerBudget: 40000.00,
                adBudget: 30000.00,
                otherBudget: 10000.00,
                influencerCost: 35000.00,
                adCost: 28000.00,
                otherCost: 8000.00,
                estimatedInfluencerRebate: 2000.00,
                executorPM: 'test-user-001',
                contentMediaIds: ['test-user-002', 'test-user-003'],
                contractType: 'SINGLE',
                settlementRules: '测试结算规则',
                kpi: '测试KPI要求',
                status: 'DRAFT',
                createdBy: 'test-user',
                updatedBy: 'test-user'
            },
            include: {
                brand: true
            }
        });
        
        log(`   ✅ 创建项目成功: ${project.projectName} (ID: ${project.id})`, 'green');
        log(`   📈 项目预算: ¥${Number(project.planningBudget).toLocaleString()}`, 'blue');
        log(`   🏷️  关联品牌: ${project.brand.name}`, 'blue');
        
        // 计算利润
        const totalCost = Number(project.influencerCost) + Number(project.adCost) + Number(project.otherCost);
        const profit = Number(project.planningBudget) - totalCost + Number(project.estimatedInfluencerRebate);
        const grossMargin = Number(project.planningBudget) > 0 ? (profit / Number(project.planningBudget)) * 100 : 0;
        
        log(`   💰 计算利润: ¥${profit.toLocaleString()}`, 'cyan');
        log(`   📊 毛利率: ${grossMargin.toFixed(2)}%`, 'cyan');
        
        // 查询项目
        const foundProject = await prisma.project.findUnique({
            where: { id: project.id },
            include: {
                brand: true,
                attachments: true
            }
        });
        
        if (foundProject) {
            log(`   ✅ 查询项目成功: ${foundProject.projectName}`, 'green');
        } else {
            log('   ❌ 查询项目失败', 'red');
        }
        
        // 更新项目
        const updatedProject = await prisma.project.update({
            where: { id: project.id },
            data: {
                status: 'ACTIVE',
                updatedBy: 'test-user'
            }
        });
        
        log(`   ✅ 更新项目状态: ${updatedProject.status}`, 'green');
        
        return project.id;
    } catch (error) {
        log(`   ❌ 项目操作失败: ${error.message}`, 'red');
        return null;
    }
}

async function testStatistics() {
    log('📈 测试统计查询...', 'yellow');
    
    try {
        // 项目总数统计
        const totalProjects = await prisma.project.count();
        log(`   📊 项目总数: ${totalProjects}`, 'blue');
        
        // 按状态统计
        const activeProjects = await prisma.project.count({
            where: { status: 'ACTIVE' }
        });
        log(`   🟢 活跃项目: ${activeProjects}`, 'blue');
        
        // 预算统计
        const budgetSum = await prisma.project.aggregate({
            _sum: {
                planningBudget: true
            }
        });
        log(`   💰 总预算: ¥${Number(budgetSum._sum.planningBudget || 0).toLocaleString()}`, 'blue');
        
        // 按品牌分组统计
        const brandStats = await prisma.project.groupBy({
            by: ['brandId'],
            _count: true,
            _sum: {
                planningBudget: true
            }
        });
        
        log(`   🏷️  品牌统计: ${brandStats.length} 个品牌有项目`, 'blue');
        
        // 按合同类型分组统计
        const contractStats = await prisma.project.groupBy({
            by: ['contractType'],
            _count: true,
            _sum: {
                planningBudget: true
            }
        });
        
        log(`   📋 合同类型统计:`, 'blue');
        contractStats.forEach(stat => {
            log(`     ${stat.contractType}: ${stat._count} 个项目, ¥${Number(stat._sum.planningBudget || 0).toLocaleString()}`, 'cyan');
        });
        
        log('   ✅ 统计查询成功', 'green');
        return true;
    } catch (error) {
        log(`   ❌ 统计查询失败: ${error.message}`, 'red');
        return false;
    }
}

async function testComplexQueries() {
    log('🔍 测试复杂查询...', 'yellow');
    
    try {
        // 查询最近的项目
        const recentProjects = await prisma.project.findMany({
            take: 5,
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                brand: {
                    select: {
                        name: true,
                        status: true
                    }
                }
            }
        });
        
        log(`   📅 最近的 ${recentProjects.length} 个项目:`, 'blue');
        recentProjects.forEach(project => {
            log(`     - ${project.projectName} (${project.brand.name})`, 'cyan');
        });
        
        // 查询高预算项目
        const highBudgetProjects = await prisma.project.findMany({
            where: {
                planningBudget: {
                    gte: 50000
                }
            },
            select: {
                projectName: true,
                planningBudget: true,
                brand: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                planningBudget: 'desc'
            }
        });
        
        log(`   💎 高预算项目 (≥¥50,000): ${highBudgetProjects.length} 个`, 'blue');
        highBudgetProjects.forEach(project => {
            log(`     - ${project.projectName}: ¥${Number(project.planningBudget).toLocaleString()}`, 'cyan');
        });
        
        log('   ✅ 复杂查询成功', 'green');
        return true;
    } catch (error) {
        log(`   ❌ 复杂查询失败: ${error.message}`, 'red');
        return false;
    }
}

async function cleanupTestData() {
    log('🧹 清理测试数据...', 'yellow');
    
    try {
        // 删除测试项目
        const deletedProjects = await prisma.project.deleteMany({
            where: {
                projectName: {
                    contains: '测试项目_'
                }
            }
        });
        
        log(`   🗑️  删除了 ${deletedProjects.count} 个测试项目`, 'blue');
        
        // 删除测试品牌
        const deletedBrands = await prisma.brand.deleteMany({
            where: {
                name: {
                    contains: '测试品牌_'
                }
            }
        });
        
        log(`   🗑️  删除了 ${deletedBrands.count} 个测试品牌`, 'blue');
        
        log('   ✅ 测试数据清理完成', 'green');
        return true;
    } catch (error) {
        log(`   ❌ 清理测试数据失败: ${error.message}`, 'red');
        return false;
    }
}

async function main() {
    log('🚀 PostgreSQL数据库功能测试', 'magenta');
    log('=====================================', 'magenta');
    log('', 'reset');
    
    try {
        // 1. 测试数据库连接
        const connectionOk = await testDatabaseConnection();
        if (!connectionOk) {
            process.exit(1);
        }
        log('', 'reset');
        
        // 2. 测试品牌操作
        const brandId = await testBrandOperations();
        log('', 'reset');
        
        // 3. 测试项目操作
        const projectId = await testProjectOperations(brandId);
        log('', 'reset');
        
        // 4. 测试统计查询
        await testStatistics();
        log('', 'reset');
        
        // 5. 测试复杂查询
        await testComplexQueries();
        log('', 'reset');
        
        // 6. 清理测试数据
        await cleanupTestData();
        log('', 'reset');
        
        log('🎉 所有数据库测试通过！', 'green');
        log('', 'reset');
        log('💡 提示:', 'cyan');
        log('   - 数据库连接正常', 'blue');
        log('   - CRUD操作功能正常', 'blue');
        log('   - 统计查询功能正常', 'blue');
        log('   - 复杂查询功能正常', 'blue');
        log('   - 数据完整性保持良好', 'blue');
        
    } catch (error) {
        log(`💥 测试失败: ${error.message}`, 'red');
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// 运行测试
main().catch(error => {
    log(`💥 测试运行失败: ${error.message}`, 'red');
    process.exit(1);
});
