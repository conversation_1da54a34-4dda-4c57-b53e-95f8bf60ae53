#!/usr/bin/env node

/**
 * 测试层级部门功能
 * 验证修复后的部门同步是否能正确处理层级结构
 */

import { DingTalkService } from '../dist/services/dingtalk.js';
import { DatabaseService } from '../dist/services/database.js';
import { DepartmentSyncService } from '../dist/services/departmentSync.js';

async function testHierarchicalDepartments() {
  console.log('🚀 开始测试层级部门功能...');
  
  const databaseService = new DatabaseService();
  const dingTalkService = new DingTalkService();
  const departmentSyncService = new DepartmentSyncService(databaseService, dingTalkService);
  
  try {
    // 1. 对比第一层部门和所有层级部门的数量差异
    console.log('\n📊 步骤1: 对比部门获取方法的差异...');
    
    let firstLevelCount = 0;
    let allLevelsCount = 0;
    
    try {
      const firstLevelDepts = await dingTalkService.getDepartmentList();
      firstLevelCount = firstLevelDepts?.length || 0;
      console.log(`📋 第一层部门数量: ${firstLevelCount}`);
    } catch (error) {
      console.error('❌ 获取第一层部门失败:', error.message);
    }
    
    try {
      const allLevelsDepts = await dingTalkService.getAllDepartments();
      allLevelsCount = allLevelsDepts?.length || 0;
      console.log(`🌳 所有层级部门数量: ${allLevelsCount}`);
    } catch (error) {
      console.error('❌ 获取所有层级部门失败:', error.message);
    }
    
    if (allLevelsCount > firstLevelCount) {
      console.log(`✅ 检测到层级结构: 所有层级部门(${allLevelsCount}) > 第一层部门(${firstLevelCount})`);
      console.log(`📈 层级部门增加了 ${allLevelsCount - firstLevelCount} 个部门`);
    } else if (allLevelsCount === firstLevelCount && firstLevelCount > 0) {
      console.log(`ℹ️ 当前组织结构为扁平结构，没有子部门`);
    } else {
      console.log(`⚠️ 未检测到部门数据或存在问题`);
    }
    
    // 2. 测试修复后的部门同步
    console.log('\n🔄 步骤2: 测试修复后的部门同步...');
    try {
      const syncResult = await departmentSyncService.syncAllDepartments();
      console.log(`✅ 部门同步完成:`);
      console.log(`  - 成功同步: ${syncResult.success} 个部门`);
      console.log(`  - 同步失败: ${syncResult.failed} 个部门`);
      
      if (syncResult.errors.length > 0) {
        console.log('⚠️ 同步错误详情:');
        syncResult.errors.slice(0, 3).forEach(error => {
          console.log(`  - 部门 ${error.deptId}: ${error.error}`);
        });
      }
    } catch (error) {
      console.error('❌ 部门同步失败:', error.message);
    }
    
    // 3. 验证数据库中的部门层级结构
    console.log('\n🗄️ 步骤3: 验证数据库中的部门层级结构...');
    try {
      const allDepartments = await departmentSyncService.getAllDepartments();
      console.log(`📊 数据库中共有 ${allDepartments.length} 个部门`);
      
      if (allDepartments.length > 0) {
        // 分析层级结构
        const rootDepts = allDepartments.filter(dept => dept.parentId === 1 || dept.parentId === 0);
        const subDepts = allDepartments.filter(dept => dept.parentId !== 1 && dept.parentId !== 0);
        
        console.log(`🌲 层级结构分析:`);
        console.log(`  - 根部门: ${rootDepts.length} 个`);
        console.log(`  - 子部门: ${subDepts.length} 个`);
        
        if (rootDepts.length > 0) {
          console.log(`📋 根部门列表:`);
          rootDepts.slice(0, 5).forEach(dept => {
            console.log(`  - ${dept.name} (ID: ${dept.deptId})`);
          });
        }
        
        if (subDepts.length > 0) {
          console.log(`📂 子部门示例 (含路径):`);
          subDepts.slice(0, 5).forEach(dept => {
            console.log(`  - ${dept.name} (ID: ${dept.deptId}, 路径: ${dept.path})`);
          });
        }
      }
    } catch (error) {
      console.error('❌ 验证数据库部门结构失败:', error.message);
    }
    
    // 4. 测试部门路径功能
    console.log('\n🛤️ 步骤4: 测试部门路径功能...');
    try {
      const allDepartments = await departmentSyncService.getAllDepartments();
      const deptsWithPath = allDepartments.filter(dept => dept.path && dept.path.includes(' / '));
      
      if (deptsWithPath.length > 0) {
        console.log(`✅ 发现 ${deptsWithPath.length} 个具有层级路径的部门:`);
        deptsWithPath.slice(0, 3).forEach(dept => {
          console.log(`  - ${dept.name}: ${dept.path}`);
        });
      } else {
        console.log(`ℹ️ 未发现具有层级路径的部门，可能为扁平结构`);
      }
    } catch (error) {
      console.error('❌ 测试部门路径功能失败:', error.message);
    }
    
    console.log('\n✅ 层级部门功能测试完成');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在停止测试...');
  process.exit(0);
});

// 运行测试
testHierarchicalDepartments()
  .then(() => {
    console.log('\n🎉 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  });
