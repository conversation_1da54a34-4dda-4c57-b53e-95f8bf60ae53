#!/usr/bin/env node

/**
 * 钉钉JSAPI签名问题诊断工具
 * 帮助排查和解决JSAPI ticket读取失败等问题
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');
require('dotenv').config();

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 配置
const config = {
    appKey: process.env.DINGTALK_APP_KEY,
    appSecret: process.env.DINGTALK_APP_SECRET,
    corpId: process.env.DINGTALK_CORP_ID,
    agentId: process.env.DINGTALK_AGENT_ID,
    baseUrl: 'https://oapi.dingtalk.com'
};

// HTTP请求函数
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'DingTalk-Diagnosis-Tool/1.0',
                ...options.headers
            }
        };

        const req = client.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: data
                    });
                }
            });
        });

        req.on('error', reject);

        if (options.body) {
            req.write(JSON.stringify(options.body));
        }

        req.end();
    });
}

// 检查环境配置
function checkEnvironment() {
    log('🔍 检查环境配置...', 'yellow');
    
    const requiredVars = {
        'DINGTALK_APP_KEY': config.appKey,
        'DINGTALK_APP_SECRET': config.appSecret,
        'DINGTALK_CORP_ID': config.corpId,
        'DINGTALK_AGENT_ID': config.agentId
    };
    
    let allConfigured = true;
    
    for (const [name, value] of Object.entries(requiredVars)) {
        if (!value) {
            log(`   ❌ ${name}: 未配置`, 'red');
            allConfigured = false;
        } else {
            const maskedValue = name.includes('SECRET') 
                ? value.substring(0, 8) + '...' 
                : value;
            log(`   ✅ ${name}: ${maskedValue}`, 'green');
        }
    }
    
    if (!allConfigured) {
        log('', 'reset');
        log('💡 请在 .env 文件中配置所有必需的环境变量', 'yellow');
        return false;
    }
    
    log('✅ 环境配置检查通过', 'green');
    return true;
}

// 获取访问令牌
async function getAccessToken() {
    log('🔑 获取访问令牌...', 'yellow');
    
    try {
        const url = `${config.baseUrl}/gettoken?appkey=${config.appKey}&appsecret=${config.appSecret}`;
        const response = await makeRequest(url);
        
        log(`   状态码: ${response.status}`, 'blue');
        log(`   响应: ${JSON.stringify(response.data, null, 2)}`, 'blue');
        
        if (response.data.errcode !== 0) {
            throw new Error(`获取访问令牌失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
        }
        
        if (!response.data.access_token) {
            throw new Error('访问令牌为空');
        }
        
        log('✅ 访问令牌获取成功', 'green');
        return response.data.access_token;
    } catch (error) {
        log(`❌ 获取访问令牌失败: ${error.message}`, 'red');
        throw error;
    }
}

// 获取JSAPI票据
async function getJSAPITicket(accessToken) {
    log('🎫 获取JSAPI票据...', 'yellow');
    
    try {
        const url = `${config.baseUrl}/get_jsapi_ticket?access_token=${accessToken}`;
        const response = await makeRequest(url);
        
        log(`   状态码: ${response.status}`, 'blue');
        log(`   响应: ${JSON.stringify(response.data, null, 2)}`, 'blue');
        
        if (response.data.errcode !== 0) {
            throw new Error(`获取JSAPI票据失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
        }
        
        if (!response.data.ticket) {
            throw new Error('JSAPI票据为空');
        }
        
        log('✅ JSAPI票据获取成功', 'green');
        log(`   票据: ${response.data.ticket.substring(0, 20)}...`, 'blue');
        log(`   过期时间: ${response.data.expires_in}秒`, 'blue');
        
        return response.data.ticket;
    } catch (error) {
        log(`❌ 获取JSAPI票据失败: ${error.message}`, 'red');
        throw error;
    }
}

// 测试签名生成
function testSignatureGeneration(ticket) {
    log('🔐 测试签名生成...', 'yellow');
    
    const crypto = require('crypto');
    const testUrl = 'https://example.com/test';
    const timestamp = Date.now();
    const nonceStr = crypto.randomBytes(16).toString('hex');
    
    // 按照钉钉官方文档的签名算法
    const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${testUrl}`;
    const signature = crypto.createHash('sha1').update(string1, 'utf8').digest('hex');
    
    log('   签名参数:', 'blue');
    log(`     URL: ${testUrl}`, 'blue');
    log(`     时间戳: ${timestamp}`, 'blue');
    log(`     随机字符串: ${nonceStr}`, 'blue');
    log(`     票据: ${ticket.substring(0, 20)}...`, 'blue');
    log('', 'reset');
    log(`   签名字符串: ${string1}`, 'cyan');
    log(`   生成的签名: ${signature}`, 'green');
    
    return {
        agentId: config.agentId,
        corpId: config.corpId,
        timeStamp: timestamp,
        nonceStr,
        signature
    };
}

// 检查应用权限
async function checkAppPermissions(accessToken) {
    log('🔒 检查应用权限...', 'yellow');
    
    try {
        // 检查应用信息
        const url = `${config.baseUrl}/topapi/v2/app/get?access_token=${accessToken}`;
        const response = await makeRequest(url, {
            method: 'POST',
            body: {
                agentid: config.agentId
            }
        });
        
        log(`   状态码: ${response.status}`, 'blue');
        log(`   响应: ${JSON.stringify(response.data, null, 2)}`, 'blue');
        
        if (response.data.errcode !== 0) {
            log(`⚠️  获取应用信息失败: ${response.data.errmsg}`, 'yellow');
        } else {
            const appInfo = response.data.result;
            log('✅ 应用信息获取成功', 'green');
            log(`   应用名称: ${appInfo.name}`, 'blue');
            log(`   应用状态: ${appInfo.app_status === 1 ? '启用' : '禁用'}`, 'blue');
        }
    } catch (error) {
        log(`⚠️  检查应用权限失败: ${error.message}`, 'yellow');
    }
}

// 网络连接测试
async function testNetworkConnectivity() {
    log('🌐 测试网络连接...', 'yellow');
    
    const testUrls = [
        'https://oapi.dingtalk.com',
        'https://www.dingtalk.com',
        'https://open.dingtalk.com'
    ];
    
    for (const testUrl of testUrls) {
        try {
            const start = Date.now();
            const response = await makeRequest(testUrl);
            const duration = Date.now() - start;
            
            log(`   ✅ ${testUrl}: ${response.status} (${duration}ms)`, 'green');
        } catch (error) {
            log(`   ❌ ${testUrl}: ${error.message}`, 'red');
        }
    }
}

// 提供解决方案
function provideSolutions() {
    log('', 'reset');
    log('💡 常见问题解决方案:', 'cyan');
    log('', 'reset');
    
    log('1. 错误码 9 - jsapi ticket 读取失败:', 'yellow');
    log('   - 检查应用配置是否正确', 'blue');
    log('   - 确认AppKey和AppSecret有效', 'blue');
    log('   - 验证应用是否已启用', 'blue');
    log('   - 检查网络连接是否正常', 'blue');
    log('', 'reset');
    
    log('2. 错误码 40001 - access_token无效:', 'yellow');
    log('   - 重新获取访问令牌', 'blue');
    log('   - 检查应用密钥是否正确', 'blue');
    log('   - 确认应用未被禁用', 'blue');
    log('', 'reset');
    
    log('3. 错误码 40014 - 不合法的access_token:', 'yellow');
    log('   - 检查AppKey和AppSecret配置', 'blue');
    log('   - 确认应用类型正确（企业内部应用）', 'blue');
    log('   - 验证应用权限设置', 'blue');
    log('', 'reset');
    
    log('4. 网络连接问题:', 'yellow');
    log('   - 检查防火墙设置', 'blue');
    log('   - 确认服务器出口IP已配置', 'blue');
    log('   - 验证DNS解析正常', 'blue');
    log('', 'reset');
    
    log('5. 签名验证失败:', 'yellow');
    log('   - 确认URL格式正确', 'blue');
    log('   - 检查时间戳和随机字符串', 'blue');
    log('   - 验证签名算法实现', 'blue');
    log('   - 清理URL中的调试参数', 'blue');
}

// 主函数
async function main() {
    log('🔧 钉钉JSAPI签名问题诊断工具', 'magenta');
    log('=====================================', 'magenta');
    log('', 'reset');
    
    try {
        // 1. 检查环境配置
        if (!checkEnvironment()) {
            process.exit(1);
        }
        log('', 'reset');
        
        // 2. 测试网络连接
        await testNetworkConnectivity();
        log('', 'reset');
        
        // 3. 获取访问令牌
        const accessToken = await getAccessToken();
        log('', 'reset');
        
        // 4. 检查应用权限
        await checkAppPermissions(accessToken);
        log('', 'reset');
        
        // 5. 获取JSAPI票据
        const ticket = await getJSAPITicket(accessToken);
        log('', 'reset');
        
        // 6. 测试签名生成
        const signature = testSignatureGeneration(ticket);
        log('', 'reset');
        
        log('🎉 所有测试通过！JSAPI配置应该可以正常工作', 'green');
        log('', 'reset');
        log('📋 生成的签名信息:', 'cyan');
        log(JSON.stringify(signature, null, 2), 'blue');
        
    } catch (error) {
        log('', 'reset');
        log(`💥 诊断失败: ${error.message}`, 'red');
        log('', 'reset');
        provideSolutions();
        process.exit(1);
    }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🔧 钉钉JSAPI签名问题诊断工具

功能:
  - 检查环境配置
  - 测试网络连接
  - 验证访问令牌获取
  - 检查应用权限
  - 测试JSAPI票据获取
  - 验证签名生成算法

用法:
  node scripts/diagnose-jsapi.js [选项]

选项:
  --help, -h     显示帮助信息

环境变量:
  DINGTALK_APP_KEY      钉钉应用AppKey
  DINGTALK_APP_SECRET   钉钉应用AppSecret
  DINGTALK_CORP_ID      企业CorpId
  DINGTALK_AGENT_ID     应用AgentId

示例:
  node scripts/diagnose-jsapi.js
`);
    process.exit(0);
}

// 运行诊断
main().catch(error => {
    log(`💥 诊断工具运行失败: ${error.message}`, 'red');
    process.exit(1);
});
