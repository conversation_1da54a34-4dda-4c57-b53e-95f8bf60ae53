/**
 * 回滚脚本：撤销预算重构迁移
 * 
 * 这个脚本将：
 * 1. 将新的 budgets 和 funding_plans 数据迁移回 weekly_budgets 表
 * 2. 恢复审批实例的关联关系
 * 3. 清理新表中的数据
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('开始回滚数据迁移...');

  try {
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      console.log('1. 获取预算和资金计划数据...');
      
      // 获取所有预算和对应的资金计划
      const budgets = await tx.budget.findMany({
        include: {
          fundingPlans: true
        }
      });

      console.log(`找到 ${budgets.length} 个预算记录`);

      if (budgets.length === 0) {
        console.log('没有需要回滚的数据');
        return;
      }

      const fundingPlanMappings = new Map(); // 存储 fundingPlanId -> weeklyBudgetId 的映射

      console.log('2. 重新创建周预算记录...');
      
      for (const budget of budgets) {
        console.log(`处理预算: ${budget.title}`);
        
        for (const fundingPlan of budget.fundingPlans) {
          // 重新创建周预算记录
          const weeklyBudget = await tx.$executeRaw`
            INSERT INTO weekly_budgets (
              id, title, "serviceType", "serviceContent", remarks,
              "contractAmount", "taxRate", "paidAmount", "remainingAmount",
              status, "approvalStatus", "approvalAmount", "approvalReason",
              "projectId", "supplierId", "createdBy", "updatedBy", 
              "createdAt", "updatedAt"
            ) VALUES (
              ${fundingPlan.id}, ${fundingPlan.title}, ${budget.serviceType}, ${budget.serviceContent}, ${fundingPlan.remarks},
              ${fundingPlan.plannedAmount}, ${budget.taxRate}, ${fundingPlan.paidAmount}, ${fundingPlan.remainingAmount},
              ${mapFundingPlanStatusToWeeklyBudgetStatus(fundingPlan.status)}, ${fundingPlan.approvalStatus}, 
              ${fundingPlan.approvalAmount}, ${fundingPlan.approvalReason},
              ${budget.projectId}, ${budget.supplierId}, ${fundingPlan.createdBy}, ${fundingPlan.updatedBy},
              ${fundingPlan.createdAt}, ${fundingPlan.updatedAt}
            )
          `;

          // 记录映射关系
          fundingPlanMappings.set(fundingPlan.id, fundingPlan.id);

          console.log(`恢复周预算: ${fundingPlan.title}`);
        }
      }

      console.log('3. 恢复审批实例关联关系...');
      
      // 更新审批实例，将 fundingPlanId 改回 weeklyBudgetId
      const approvalInstances = await tx.approvalInstance.findMany({
        where: {
          fundingPlanId: {
            not: null
          }
        }
      });

      console.log(`找到 ${approvalInstances.length} 条审批实例需要更新`);

      for (const approval of approvalInstances) {
        const weeklyBudgetId = fundingPlanMappings.get(approval.fundingPlanId);
        if (weeklyBudgetId) {
          await tx.$executeRaw`
            UPDATE approval_instances 
            SET "weeklyBudgetId" = ${weeklyBudgetId}, "fundingPlanId" = NULL
            WHERE id = ${approval.id}
          `;
          console.log(`恢复审批实例 ${approval.id} 的关联关系`);
        }
      }

      console.log('4. 清理新表数据...');
      
      // 删除资金计划数据
      const deletedFundingPlans = await tx.fundingPlan.deleteMany({});
      console.log(`删除了 ${deletedFundingPlans.count} 条资金计划记录`);

      // 删除预算数据
      const deletedBudgets = await tx.budget.deleteMany({});
      console.log(`删除了 ${deletedBudgets.count} 条预算记录`);

      console.log('5. 回滚完成！');
    });

  } catch (error) {
    console.error('回滚失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * 将资金计划状态映射回周预算状态
 */
function mapFundingPlanStatusToWeeklyBudgetStatus(fundingPlanStatus) {
  const statusMap = {
    'DRAFT': 'CREATED',
    'SUBMITTED': 'CREATED',
    'APPROVED': 'APPROVED',
    'EXECUTING': 'EXECUTING', 
    'COMPLETED': 'COMPLETED',
    'CANCELLED': 'CANCELLED'
  };
  
  return statusMap[fundingPlanStatus] || 'CREATED';
}

// 运行回滚
main()
  .then(() => {
    console.log('✅ 数据回滚成功完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 数据回滚失败:', error);
    process.exit(1);
  });
