// 测试 changedFields 字段记录的准确性
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testChangedFields() {
  try {
    console.log('🧪 测试 changedFields 字段记录准确性...');
    
    // 1. 测试创建项目的 changedFields
    console.log('\n📝 测试项目创建的 changedFields...');
    
    // 先获取一个现有品牌ID
    const brands = await prisma.brand.findMany({ take: 1 });
    if (brands.length === 0) {
      console.log('❌ 没有找到品牌，无法测试项目创建');
      return;
    }
    
    const brandId = brands[0].id;
    console.log(`使用品牌ID: ${brandId}`);
    
    // 创建一个测试项目
    const testProject = await prisma.project.create({
      data: {
        documentType: 'PROJECT_INITIATION',
        brandId: brandId,
        projectName: `测试项目-${Date.now()}`,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        planningBudget: 100000,
        influencerBudget: 50000,
        adBudget: 30000,
        otherBudget: 20000,
        influencerCost: 45000,
        adCost: 25000,
        otherCost: 15000,
        estimatedInfluencerRebate: 5000,
        executorPM: 'test-user-001',
        contentMediaIds: ['test-user-002'],
        contractType: 'SINGLE',
        settlementRules: '测试结算规则',
        kpi: '测试KPI',
        createdBy: 'test-user-001',
        updatedBy: 'test-user-001'
      }
    });
    
    console.log(`✅ 测试项目创建成功: ${testProject.id}`);
    
    // 查询创建时的变更记录
    const createChangeLogs = await prisma.projectChangeLog.findMany({
      where: {
        projectId: testProject.id,
        changeType: 'CREATE'
      },
      orderBy: { createdAt: 'desc' },
      take: 1
    });
    
    if (createChangeLogs.length > 0) {
      const createLog = createChangeLogs[0];
      console.log(`✅ 找到创建变更记录`);
      console.log(`   变更字段数量: ${createLog.changedFields.length}`);
      console.log(`   变更字段: ${createLog.changedFields.join(', ')}`);
      
      // 验证是否包含了主要字段
      const expectedFields = ['projectName', 'brandId', 'planningBudget', 'executorPM'];
      const missingFields = expectedFields.filter(field => !createLog.changedFields.includes(field));
      if (missingFields.length === 0) {
        console.log('✅ 创建记录包含了所有预期字段');
      } else {
        console.log(`❌ 创建记录缺少字段: ${missingFields.join(', ')}`);
      }
    } else {
      console.log('❌ 没有找到创建变更记录');
    }
    
    // 2. 测试项目更新的 changedFields
    console.log('\n📝 测试项目更新的 changedFields...');
    
    // 更新项目的部分字段
    const updatedProject = await prisma.project.update({
      where: { id: testProject.id },
      data: {
        projectName: `更新后的项目名称-${Date.now()}`,
        planningBudget: 150000,
        updatedBy: 'test-user-002'
      }
    });
    
    console.log(`✅ 项目更新成功`);
    
    // 手动创建更新变更记录来测试字段对比
    const beforeData = {
      projectName: testProject.projectName,
      planningBudget: testProject.planningBudget,
      brandId: testProject.brandId,
      executorPM: testProject.executorPM,
      createdAt: testProject.createdAt, // 这个字段应该被排除
      updatedAt: testProject.updatedAt  // 这个字段应该被排除
    };
    
    const afterData = {
      projectName: updatedProject.projectName,
      planningBudget: updatedProject.planningBudget,
      brandId: updatedProject.brandId,
      executorPM: updatedProject.executorPM,
      createdAt: updatedProject.createdAt,
      updatedAt: updatedProject.updatedAt
    };
    
    // 模拟字段对比逻辑
    const excludeFields = new Set(['id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy']);
    const changedFields = [];
    const allKeys = new Set([...Object.keys(beforeData), ...Object.keys(afterData)]);
    
    for (const key of allKeys) {
      if (excludeFields.has(key)) continue;
      if (JSON.stringify(beforeData[key]) !== JSON.stringify(afterData[key])) {
        changedFields.push(key);
      }
    }
    
    console.log(`✅ 字段对比完成`);
    console.log(`   实际变更字段: ${changedFields.join(', ')}`);
    console.log(`   预期变更字段: projectName, planningBudget`);
    
    // 验证字段对比的准确性
    const expectedChangedFields = ['projectName', 'planningBudget'];
    const isCorrect = expectedChangedFields.every(field => changedFields.includes(field)) &&
                     changedFields.every(field => expectedChangedFields.includes(field));
    
    if (isCorrect) {
      console.log('✅ 字段对比逻辑正确');
    } else {
      console.log('❌ 字段对比逻辑有误');
    }
    
    // 验证排除字段是否正确工作
    const excludedFieldsInResult = changedFields.filter(field => excludeFields.has(field));
    if (excludedFieldsInResult.length === 0) {
      console.log('✅ 排除字段逻辑正确工作');
    } else {
      console.log(`❌ 排除字段逻辑有误，包含了: ${excludedFieldsInResult.join(', ')}`);
    }
    
    // 3. 清理测试数据
    console.log('\n🧹 清理测试数据...');
    await prisma.projectChangeLog.deleteMany({
      where: { projectId: testProject.id }
    });
    await prisma.project.delete({
      where: { id: testProject.id }
    });
    console.log('✅ 测试数据清理完成');
    
    console.log('\n🎉 changedFields 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testChangedFields();
