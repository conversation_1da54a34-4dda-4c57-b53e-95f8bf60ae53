#!/bin/bash

# 云效专用部署脚本
# 在云效流水线中使用

set -e

# 环境变量检查
check_env_vars() {
    local required_vars=(
        "ENVIRONMENT"
        "IMAGE_TAG"
        "DOCKER_REGISTRY"
        "DOCKER_USERNAME"
        "DOCKER_PASSWORD"
        "KUBECONFIG_CONTENT"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo "错误: 环境变量 $var 未设置"
            exit 1
        fi
    done
}

# 设置kubectl配置
setup_kubectl() {
    echo "设置kubectl配置..."
    echo "$KUBECONFIG_CONTENT" | base64 -d > /tmp/kubeconfig
    export KUBECONFIG=/tmp/kubeconfig
    
    # 验证连接
    kubectl cluster-info
}

# 登录Docker Registry
docker_login() {
    echo "登录Docker Registry..."
    echo "$DOCKER_PASSWORD" | docker login "$DOCKER_REGISTRY" -u "$DOCKER_USERNAME" --password-stdin
}

# 部署应用
deploy_app() {
    local env=$1
    local image_tag=$2
    
    echo "开始部署到 $env 环境，镜像版本: $image_tag"
    
    # 设置命名空间
    if [ "$env" = "dev" ]; then
        NAMESPACE="cantv-ding-dev"
        DEPLOYMENT_FILE="k8s/deployment-dev.yaml"
    else
        NAMESPACE="cantv-ding-prod"
        DEPLOYMENT_FILE="k8s/deployment.yaml"
    fi
    
    # 创建命名空间
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建镜像拉取密钥
    kubectl create secret docker-registry aliyun-registry-secret \
        --docker-server="$DOCKER_REGISTRY" \
        --docker-username="$DOCKER_USERNAME" \
        --docker-password="$DOCKER_PASSWORD" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 更新镜像标签
    sed -i "s|registry.cn-hangzhou.aliyuncs.com/cantv-ding/cantv-ding-backend:latest|$DOCKER_REGISTRY/cantv-ding/cantv-ding-backend:$image_tag|g" $DEPLOYMENT_FILE
    
    # 应用配置
    kubectl apply -f $DEPLOYMENT_FILE
    
    # 等待部署完成
    if [ "$env" = "dev" ]; then
        kubectl rollout status deployment/cantv-ding-backend-dev -n $NAMESPACE --timeout=300s
    else
        kubectl rollout status deployment/cantv-ding-backend -n $NAMESPACE --timeout=300s
    fi
    
    echo "部署完成!"
}

# 健康检查
health_check() {
    local env=$1
    
    if [ "$env" = "dev" ]; then
        NAMESPACE="cantv-ding-dev"
        SERVICE_NAME="cantv-ding-backend-service-dev"
    else
        NAMESPACE="cantv-ding-prod"
        SERVICE_NAME="cantv-ding-backend-service"
    fi
    
    echo "执行健康检查..."
    
    # 等待服务就绪
    sleep 30
    
    # 端口转发进行健康检查
    kubectl port-forward service/$SERVICE_NAME 8080:80 -n $NAMESPACE &
    PORT_FORWARD_PID=$!
    sleep 10
    
    # 健康检查
    if curl -f http://localhost:8080/api/health; then
        echo "健康检查通过"
        kill $PORT_FORWARD_PID 2>/dev/null || true
        return 0
    else
        echo "健康检查失败"
        kill $PORT_FORWARD_PID 2>/dev/null || true
        return 1
    fi
}

# 发送钉钉通知
send_dingtalk_notification() {
    local status=$1
    local env=$2
    local image_tag=$3
    
    if [ -n "$DINGTALK_WEBHOOK" ]; then
        local color="green"
        local title="✅ 部署成功"
        
        if [ "$status" != "success" ]; then
            color="red"
            title="❌ 部署失败"
        fi
        
        local message="{
            \"msgtype\": \"markdown\",
            \"markdown\": {
                \"title\": \"$title\",
                \"text\": \"## $title\\n\\n**应用**: CanTV钉钉后端\\n**环境**: $env\\n**版本**: $image_tag\\n**时间**: $(date)\\n**流水线**: $PIPELINE_ID\"
            }
        }"
        
        curl -X POST "$DINGTALK_WEBHOOK" \
            -H 'Content-Type: application/json' \
            -d "$message"
    fi
}

# 主函数
main() {
    echo "=== 云效部署脚本开始执行 ==="
    
    # 检查环境变量
    check_env_vars
    
    # 设置kubectl
    setup_kubectl
    
    # 登录Docker Registry
    docker_login
    
    # 部署应用
    if deploy_app "$ENVIRONMENT" "$IMAGE_TAG"; then
        echo "部署成功"
        
        # 健康检查
        if health_check "$ENVIRONMENT"; then
            echo "健康检查通过"
            send_dingtalk_notification "success" "$ENVIRONMENT" "$IMAGE_TAG"
        else
            echo "健康检查失败"
            send_dingtalk_notification "health_check_failed" "$ENVIRONMENT" "$IMAGE_TAG"
            exit 1
        fi
    else
        echo "部署失败"
        send_dingtalk_notification "failed" "$ENVIRONMENT" "$IMAGE_TAG"
        exit 1
    fi
    
    echo "=== 云效部署脚本执行完成 ==="
}

# 执行主函数
main "$@"
