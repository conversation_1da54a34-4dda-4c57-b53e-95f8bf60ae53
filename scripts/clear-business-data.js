#!/usr/bin/env node

/**
 * 清空业务数据脚本 (Node.js + Prisma版本)
 *
 * 此脚本会清空所有业务相关数据，但保留系统数据（用户、权限、角色等）
 *
 * 警告：此操作不可逆，请在执行前确保已备份重要数据！
 *
 * 使用方法：
 * 1. 确保已安装依赖：npm install
 * 2. 设置环境变量：DATABASE_URL
 * 3. 执行脚本：node scripts/clear-business-data.js
 *
 * 作者：系统管理员
 * 创建时间：2025-01-22
 */

import { PrismaClient } from '@prisma/client';
import readline from 'readline';

const prisma = new PrismaClient();

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 询问用户确认
function askConfirmation() {
  return new Promise((resolve) => {
    console.log('\n⚠️  警告：此操作将清空所有业务数据，包括：');
    console.log('   - 品牌数据');
    console.log('   - 项目数据');
    console.log('   - 项目收入数据');
    console.log('   - 周预算数据');
    console.log('   - 供应商数据');
    console.log('   - 附件数据');
    console.log('   - 项目变更日志');
    console.log('   - 审批实例数据');
    console.log('\n✅ 以下系统数据将被保留：');
    console.log('   - 用户数据');
    console.log('   - 部门数据');
    console.log('   - 角色权限数据');
    console.log('\n此操作不可逆！请确保已备份重要数据。\n');
    
    rl.question('确定要继续吗？请输入 "YES" 确认：', (answer) => {
      resolve(answer === 'YES');
    });
  });
}

// 显示当前数据统计
async function showCurrentStats() {
  console.log('\n📊 当前数据统计：');
  
  try {
    const stats = {
      brands: await prisma.brand.count(),
      projects: await prisma.project.count(),
      revenues: await prisma.projectRevenue.count(),
      weeklyBudgets: await prisma.weeklyBudget.count(),
      suppliers: await prisma.supplier.count(),
      attachments: await prisma.attachment.count(),
      changeLogs: await prisma.projectChangeLog.count(),
      approvals: await prisma.approvalInstance.count(),
      // 系统数据
      users: await prisma.user.count(),
      departments: await prisma.department.count(),
      roles: await prisma.role.count(),
      permissions: await prisma.permission.count()
    };

    console.log('业务数据：');
    console.log(`   品牌：${stats.brands} 条`);
    console.log(`   项目：${stats.projects} 条`);
    console.log(`   项目收入：${stats.revenues} 条`);
    console.log(`   周预算：${stats.weeklyBudgets} 条`);
    console.log(`   供应商：${stats.suppliers} 条`);
    console.log(`   附件：${stats.attachments} 条`);
    console.log(`   变更日志：${stats.changeLogs} 条`);
    console.log(`   审批实例：${stats.approvals} 条`);
    
    console.log('\n系统数据（将保留）：');
    console.log(`   用户：${stats.users} 条`);
    console.log(`   部门：${stats.departments} 条`);
    console.log(`   角色：${stats.roles} 条`);
    console.log(`   权限：${stats.permissions} 条`);
    
    return stats;
  } catch (error) {
    console.error('❌ 获取数据统计失败：', error.message);
    throw error;
  }
}

// 清空业务数据
async function clearBusinessData() {
  console.log('\n🚀 开始清空业务数据...');
  
  try {
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 1. 清空审批实例表（有外键依赖，需要先清空）
      const deletedApprovals = await tx.approvalInstance.deleteMany();
      console.log(`✓ 已清空审批实例数据：${deletedApprovals.count} 条`);

      // 2. 清空项目变更日志表（依赖项目表）
      const deletedChangeLogs = await tx.projectChangeLog.deleteMany();
      console.log(`✓ 已清空项目变更日志数据：${deletedChangeLogs.count} 条`);

      // 3. 清空项目收入表（依赖项目表）
      const deletedRevenues = await tx.projectRevenue.deleteMany();
      console.log(`✓ 已清空项目收入数据：${deletedRevenues.count} 条`);

      // 4. 清空周预算表（依赖项目和供应商表）
      const deletedWeeklyBudgets = await tx.weeklyBudget.deleteMany();
      console.log(`✓ 已清空周预算数据：${deletedWeeklyBudgets.count} 条`);

      // 5. 清空附件表（依赖项目表）
      const deletedAttachments = await tx.attachment.deleteMany();
      console.log(`✓ 已清空附件数据：${deletedAttachments.count} 条`);

      // 6. 清空项目表
      const deletedProjects = await tx.project.deleteMany();
      console.log(`✓ 已清空项目数据：${deletedProjects.count} 条`);

      // 7. 清空品牌表
      const deletedBrands = await tx.brand.deleteMany();
      console.log(`✓ 已清空品牌数据：${deletedBrands.count} 条`);

      // 8. 清空供应商表
      const deletedSuppliers = await tx.supplier.deleteMany();
      console.log(`✓ 已清空供应商数据：${deletedSuppliers.count} 条`);
    });

    console.log('\n🎉 业务数据清空完成！');
    
  } catch (error) {
    console.error('❌ 清空数据失败：', error.message);
    throw error;
  }
}

// 显示最终统计
async function showFinalStats() {
  console.log('\n📊 清空后数据统计：');
  
  try {
    const stats = {
      users: await prisma.user.count(),
      departments: await prisma.department.count(),
      roles: await prisma.role.count(),
      permissions: await prisma.permission.count(),
      rolePermissions: await prisma.rolePermission.count(),
      userRoles: await prisma.userRole.count(),
      departmentRoles: await prisma.departmentRole.count()
    };

    console.log('保留的系统数据：');
    console.log(`   用户：${stats.users} 条`);
    console.log(`   部门：${stats.departments} 条`);
    console.log(`   角色：${stats.roles} 条`);
    console.log(`   权限：${stats.permissions} 条`);
    console.log(`   角色权限关联：${stats.rolePermissions} 条`);
    console.log(`   用户角色关联：${stats.userRoles} 条`);
    console.log(`   部门角色关联：${stats.departmentRoles} 条`);
    
  } catch (error) {
    console.error('❌ 获取最终统计失败：', error.message);
  }
}

// 主函数
async function main() {
  try {
    console.log('🔧 清空业务数据工具');
    console.log('='.repeat(50));

    // 测试数据库连接
    console.log('🔗 测试数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功');

    // 显示当前数据统计
    await showCurrentStats();

    // 询问用户确认
    const confirmed = await askConfirmation();
    
    if (!confirmed) {
      console.log('\n❌ 操作已取消');
      return;
    }

    // 清空业务数据
    await clearBusinessData();

    // 显示最终统计
    await showFinalStats();

    console.log('\n✅ 所有操作完成！');
    
  } catch (error) {
    console.error('\n💥 执行过程中发生错误：', error);
    process.exit(1);
  } finally {
    rl.close();
    await prisma.$disconnect();
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (error) => {
  console.error('💥 未处理的Promise拒绝：', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n\n👋 收到中断信号，正在退出...');
  rl.close();
  prisma.$disconnect().then(() => {
    process.exit(0);
  });
});

// 运行主函数 - 只有在直接调用时才运行
if (process.argv[1] && process.argv[1].includes('clear-business-data.js')) {
  main();
}

export { clearBusinessData, showCurrentStats, showFinalStats };

