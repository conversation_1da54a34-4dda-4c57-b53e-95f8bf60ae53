-- 更新收入类型枚举脚本
-- 将原有的收入类型枚举更新为新的业务分类

-- 1. 先重命名现有的枚举类型
ALTER TYPE revenue_type RENAME TO revenue_type_old;

-- 2. 创建新的收入类型枚举
CREATE TYPE revenue_type AS ENUM (
  'INFLUENCER_INCOME', -- 达人收入
  'PROJECT_INCOME',    -- 项目收入
  'OTHER'              -- 其他收入
);

-- 3. 添加临时列
ALTER TABLE project_revenues
ADD COLUMN revenue_type_new revenue_type;

-- 4. 数据迁移 - 将旧值映射到新值
UPDATE project_revenues
SET revenue_type_new = CASE
  WHEN "revenueType" = 'MILESTONE' THEN 'PROJECT_INCOME'::revenue_type
  WHEN "revenueType" = 'MONTHLY' THEN 'PROJECT_INCOME'::revenue_type
  WHEN "revenueType" = 'QUARTERLY' THEN 'PROJECT_INCOME'::revenue_type
  WHEN "revenueType" = 'FINAL' THEN 'PROJECT_INCOME'::revenue_type
  WHEN "revenueType" = 'BONUS' THEN 'INFLUENCER_INCOME'::revenue_type
  WHEN "revenueType" = 'OTHER' THEN 'OTHER'::revenue_type
  ELSE 'PROJECT_INCOME'::revenue_type
END;

-- 5. 删除旧列
ALTER TABLE project_revenues DROP COLUMN "revenueType";

-- 6. 重命名新列
ALTER TABLE project_revenues RENAME COLUMN revenue_type_new TO "revenueType";

-- 7. 设置默认值
ALTER TABLE project_revenues
ALTER COLUMN "revenueType" SET DEFAULT 'PROJECT_INCOME';

-- 8. 删除旧的枚举类型
DROP TYPE revenue_type_old;
