#!/usr/bin/env node

/**
 * 完整API接口测试脚本
 * 测试所有项目管理相关的API接口
 */

import http from 'http';
import { URL } from 'url';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';
const TEST_TIMEOUT = 10000; // 10秒超时

// 测试结果统计
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

// HTTP请求函数
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: data
                    });
                }
            });
        });

        req.on('error', reject);
        req.setTimeout(TEST_TIMEOUT, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });

        if (options.body) {
            req.write(options.body);
        }

        req.end();
    });
}

// 测试函数
async function runTest(name, testFn) {
    totalTests++;
    const startTime = Date.now();
    
    try {
        await testFn();
        const duration = Date.now() - startTime;
        log(`  ✅ ${name} (${duration}ms)`, 'green');
        passedTests++;
        return true;
    } catch (error) {
        const duration = Date.now() - startTime;
        log(`  ❌ ${name} (${duration}ms): ${error.message}`, 'red');
        failedTests++;
        return false;
    }
}

// 测试品牌管理API
async function testBrandAPIs() {
    log('\n🏷️  测试品牌管理API', 'yellow');
    
    let createdBrandId = null;
    
    // 测试获取品牌列表
    await runTest('获取品牌列表', async () => {
        const response = await makeRequest(`${API_BASE_URL}/brands?page=1&pageSize=10`);
        if (response.status !== 200) {
            throw new Error(`HTTP ${response.status}`);
        }
        if (!response.data.success) {
            throw new Error(response.data.message || '请求失败');
        }
        if (!Array.isArray(response.data.data.brands)) {
            throw new Error('响应数据格式错误');
        }
    });
    
    // 测试创建品牌
    await runTest('创建品牌', async () => {
        const brandData = {
            name: `测试品牌_${Date.now()}`,
            description: '这是一个API测试品牌',
            logo: 'https://example.com/test-logo.png'
        };
        
        const response = await makeRequest(`${API_BASE_URL}/brands`, {
            method: 'POST',
            body: JSON.stringify(brandData)
        });
        
        if (response.status !== 200) {
            throw new Error(`HTTP ${response.status}`);
        }
        if (!response.data.success) {
            throw new Error(response.data.message || '创建失败');
        }
        
        createdBrandId = response.data.data?.id;
        if (!createdBrandId) {
            console.log('创建品牌响应:', JSON.stringify(response.data, null, 2));
            throw new Error('未返回品牌ID');
        }
    });
    
    // 测试获取单个品牌
    if (createdBrandId) {
        await runTest('获取单个品牌', async () => {
            const response = await makeRequest(`${API_BASE_URL}/brands/${createdBrandId}`);
            if (response.status !== 200) {
                throw new Error(`HTTP ${response.status}`);
            }
            if (!response.data.success) {
                throw new Error(response.data.message || '获取失败');
            }
            if (response.data.data.id !== createdBrandId) {
                throw new Error('返回的品牌ID不匹配');
            }
        });
        
        // 测试更新品牌
        await runTest('更新品牌', async () => {
            const updateData = {
                id: createdBrandId,
                name: `更新后的品牌_${Date.now()}`,
                description: '更新后的描述'
            };
            
            const response = await makeRequest(`${API_BASE_URL}/brands`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
            
            if (response.status !== 200) {
                throw new Error(`HTTP ${response.status}`);
            }
            if (!response.data.success) {
                throw new Error(response.data.message || '更新失败');
            }
        });
    }
    
    return createdBrandId;
}

// 测试项目管理API
async function testProjectAPIs(brandId) {
    log('\n📊 测试项目管理API', 'yellow');
    
    let createdProjectId = null;
    
    // 测试获取项目列表
    await runTest('获取项目列表', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects?page=1&pageSize=10`);
        if (response.status !== 200) {
            throw new Error(`HTTP ${response.status}`);
        }
        if (!response.data.success) {
            throw new Error(response.data.message || '请求失败');
        }
        if (!Array.isArray(response.data.data.projects)) {
            throw new Error('响应数据格式错误');
        }
    });
    
    // 测试创建项目（如果有品牌ID）
    if (brandId) {
        await runTest('创建项目', async () => {
            const projectData = {
                documentType: 'project_initiation',
                brandId: brandId,
                projectName: `测试项目_${Date.now()}`,
                period: {
                    startDate: '2024-02-01',
                    endDate: '2024-02-29'
                },
                budget: {
                    planningBudget: 100000,
                    influencerBudget: 40000,
                    adBudget: 30000,
                    otherBudget: 10000
                },
                cost: {
                    influencerCost: 35000,
                    adCost: 28000,
                    otherCost: 8000,
                    estimatedInfluencerRebate: 2000
                },
                executorPM: 'test-user-001',
                contentMediaIds: ['test-user-002'],
                contractType: 'single',
                settlementRules: '测试结算规则',
                kpi: '测试KPI要求'
            };
            
            const response = await makeRequest(`${API_BASE_URL}/projects`, {
                method: 'POST',
                body: JSON.stringify(projectData)
            });
            
            if (response.status !== 200) {
                throw new Error(`HTTP ${response.status}`);
            }
            if (!response.data.success) {
                throw new Error(response.data.message || '创建失败');
            }
            
            createdProjectId = response.data.data.id;
            if (!createdProjectId) {
                throw new Error('未返回项目ID');
            }
        });
        
        // 测试获取单个项目
        if (createdProjectId) {
            await runTest('获取单个项目', async () => {
                const response = await makeRequest(`${API_BASE_URL}/projects/${createdProjectId}`);
                if (response.status !== 200) {
                    throw new Error(`HTTP ${response.status}`);
                }
                if (!response.data.success) {
                    throw new Error(response.data.message || '获取失败');
                }
                if (response.data.data.id !== createdProjectId) {
                    throw new Error('返回的项目ID不匹配');
                }
            });
            
            // 测试更新项目
            await runTest('更新项目', async () => {
                const updateData = {
                    id: createdProjectId,
                    projectName: `更新后的项目_${Date.now()}`,
                    status: 'active'
                };
                
                const response = await makeRequest(`${API_BASE_URL}/projects`, {
                    method: 'PUT',
                    body: JSON.stringify(updateData)
                });
                
                if (response.status !== 200) {
                    throw new Error(`HTTP ${response.status}`);
                }
                if (!response.data.success) {
                    throw new Error(response.data.message || '更新失败');
                }
            });
        }
    }
    
    return createdProjectId;
}

// 测试统计API
async function testStatsAPIs() {
    log('\n📈 测试统计分析API', 'yellow');
    
    await runTest('获取项目统计', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects/stats`);
        if (response.status !== 200) {
            throw new Error(`HTTP ${response.status}`);
        }
        if (!response.data.success) {
            throw new Error(response.data.message || '请求失败');
        }
        
        const stats = response.data.data;
        if (!stats || typeof stats.totalProjects !== 'number') {
            console.log('统计响应:', JSON.stringify(response.data, null, 2));
            // 如果没有数据，检查是否至少返回了基本结构
            if (!stats || (!stats.hasOwnProperty('totalProjects'))) {
                throw new Error('统计数据格式错误');
            }
        }
    });
}

// 清理测试数据
async function cleanupTestData(brandId, projectId) {
    log('\n🧹 清理测试数据', 'yellow');
    
    // 删除测试项目
    if (projectId) {
        await runTest('删除测试项目', async () => {
            const response = await makeRequest(`${API_BASE_URL}/projects/${projectId}`, {
                method: 'DELETE'
            });
            if (response.status !== 200) {
                throw new Error(`HTTP ${response.status}`);
            }
            if (!response.data.success) {
                throw new Error(response.data.message || '删除失败');
            }
        });
    }
    
    // 删除测试品牌
    if (brandId) {
        await runTest('删除测试品牌', async () => {
            const response = await makeRequest(`${API_BASE_URL}/brands/${brandId}`, {
                method: 'DELETE'
            });
            if (response.status !== 200) {
                throw new Error(`HTTP ${response.status}`);
            }
            if (!response.data.success) {
                throw new Error(response.data.message || '删除失败');
            }
        });
    }
}

// 主函数
async function main() {
    log('🚀 项目管理系统API完整测试', 'magenta');
    log('=====================================', 'magenta');
    log(`📍 测试地址: ${API_BASE_URL}`, 'cyan');
    log(`⏱️  超时时间: ${TEST_TIMEOUT}ms`, 'cyan');
    log('', 'reset');
    
    const startTime = Date.now();
    let brandId = null;
    let projectId = null;
    
    try {
        // 测试品牌管理API
        brandId = await testBrandAPIs();
        
        // 测试项目管理API
        projectId = await testProjectAPIs(brandId);
        
        // 测试统计API
        await testStatsAPIs();
        
        // 清理测试数据
        await cleanupTestData(brandId, projectId);
        
    } catch (error) {
        log(`\n💥 测试过程中发生错误: ${error.message}`, 'red');
    }
    
    const totalTime = Date.now() - startTime;
    
    // 显示测试结果
    log('\n📊 测试结果统计', 'magenta');
    log('=====================================', 'magenta');
    log(`总测试数: ${totalTests}`, 'blue');
    log(`通过: ${passedTests}`, 'green');
    log(`失败: ${failedTests}`, 'red');
    log(`成功率: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`, 'cyan');
    log(`总耗时: ${totalTime}ms`, 'cyan');
    log('', 'reset');
    
    if (failedTests === 0) {
        log('🎉 所有API测试通过！', 'green');
        process.exit(0);
    } else {
        log('⚠️  部分API测试失败，请检查服务器状态', 'yellow');
        process.exit(1);
    }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🚀 API完整测试工具

功能:
  - 测试所有项目管理API接口
  - 验证CRUD操作完整性
  - 自动清理测试数据
  - 生成详细测试报告

用法:
  node scripts/test-all-apis.js [选项]

选项:
  --help, -h     显示帮助信息

环境变量:
  API_BASE_URL   API基础地址 (默认: http://localhost:3000/api)

示例:
  node scripts/test-all-apis.js
  API_BASE_URL=http://localhost:8080/api node scripts/test-all-apis.js
`);
    process.exit(0);
}

// 运行测试
main().catch(error => {
    log(`💥 测试工具运行失败: ${error.message}`, 'red');
    process.exit(1);
});
