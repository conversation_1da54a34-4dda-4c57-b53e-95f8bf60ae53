/**
 * 清理测试数据脚本
 * 
 * 这个脚本将清理测试过程中创建的数据：
 * 1. 删除测试审批实例
 * 2. 删除测试资金计划
 * 3. 删除测试预算
 * 4. 删除测试项目
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🧹 开始清理测试数据...\n');

  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功\n');

    // 1. 删除测试审批实例
    console.log('1️⃣ 删除测试审批实例...');
    const deletedApprovals = await prisma.approvalInstance.deleteMany({
      where: {
        OR: [
          { title: { contains: '测试审批' } },
          { processCode: 'TEST_APPROVAL' }
        ]
      }
    });
    console.log(`✅ 删除了 ${deletedApprovals.count} 个测试审批实例\n`);

    // 2. 删除测试资金计划
    console.log('2️⃣ 删除测试资金计划...');
    const deletedFundingPlans = await prisma.fundingPlan.deleteMany({
      where: {
        title: { contains: '测试资金计划' }
      }
    });
    console.log(`✅ 删除了 ${deletedFundingPlans.count} 个测试资金计划\n`);

    // 3. 删除测试预算
    console.log('3️⃣ 删除测试预算...');
    const deletedBudgets = await prisma.budget.deleteMany({
      where: {
        title: { contains: '测试预算' }
      }
    });
    console.log(`✅ 删除了 ${deletedBudgets.count} 个测试预算\n`);

    // 4. 删除测试项目
    console.log('4️⃣ 删除测试项目...');
    const deletedProjects = await prisma.project.deleteMany({
      where: {
        projectName: { contains: '测试项目-预算重构' }
      }
    });
    console.log(`✅ 删除了 ${deletedProjects.count} 个测试项目\n`);

    // 5. 删除测试品牌
    console.log('5️⃣ 删除测试品牌...');
    const deletedBrands = await prisma.brand.deleteMany({
      where: {
        name: { contains: '测试品牌-预算重构' }
      }
    });
    console.log(`✅ 删除了 ${deletedBrands.count} 个测试品牌\n`);

    console.log('🎉 测试数据清理完成！');

  } catch (error) {
    console.error('❌ 清理失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行清理
main()
  .then(() => {
    console.log('✅ 清理完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 清理失败:', error);
    process.exit(1);
  });
