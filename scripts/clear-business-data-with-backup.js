#!/usr/bin/env node

/**
 * 清空业务数据脚本 (带备份功能)
 * 
 * 此脚本会先备份业务数据，然后清空所有业务相关数据，但保留系统数据
 * 
 * 功能特性：
 * - 自动备份业务数据到JSON文件
 * - 支持从备份文件恢复数据
 * - 事务操作确保数据一致性
 * - 详细的操作日志
 * 
 * 使用方法：
 * 1. 清空数据：node scripts/clear-business-data-with-backup.js clear
 * 2. 仅备份：node scripts/clear-business-data-with-backup.js backup
 * 3. 恢复数据：node scripts/clear-business-data-with-backup.js restore <backup-file>
 * 
 * 作者：系统管理员
 * 创建时间：2025-01-22
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';
import readline from 'readline';

const prisma = new PrismaClient();

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 生成备份文件名
function generateBackupFileName() {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
  return `business-data-backup-${timestamp}.json`;
}

// 备份业务数据
async function backupBusinessData(backupFile) {
  console.log('📦 开始备份业务数据...');
  
  try {
    const backupData = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      data: {}
    };

    // 备份各个业务表的数据
    console.log('   正在备份品牌数据...');
    backupData.data.brands = await prisma.brand.findMany();
    
    console.log('   正在备份供应商数据...');
    backupData.data.suppliers = await prisma.supplier.findMany();
    
    console.log('   正在备份项目数据...');
    backupData.data.projects = await prisma.project.findMany();
    
    console.log('   正在备份项目收入数据...');
    backupData.data.projectRevenues = await prisma.projectRevenue.findMany();
    
    console.log('   正在备份周预算数据...');
    backupData.data.weeklyBudgets = await prisma.weeklyBudget.findMany();
    
    console.log('   正在备份附件数据...');
    backupData.data.attachments = await prisma.attachment.findMany();
    
    console.log('   正在备份项目变更日志...');
    backupData.data.projectChangeLogs = await prisma.projectChangeLog.findMany();
    
    console.log('   正在备份审批实例数据...');
    backupData.data.approvalInstances = await prisma.approvalInstance.findMany();

    // 写入备份文件
    const backupDir = path.join(process.cwd(), 'backups');
    await fs.mkdir(backupDir, { recursive: true });
    
    const backupPath = path.join(backupDir, backupFile);
    await fs.writeFile(backupPath, JSON.stringify(backupData, null, 2), 'utf8');
    
    console.log(`✅ 备份完成！文件保存至：${backupPath}`);
    
    // 显示备份统计
    const stats = {
      brands: backupData.data.brands.length,
      suppliers: backupData.data.suppliers.length,
      projects: backupData.data.projects.length,
      revenues: backupData.data.projectRevenues.length,
      weeklyBudgets: backupData.data.weeklyBudgets.length,
      attachments: backupData.data.attachments.length,
      changeLogs: backupData.data.projectChangeLogs.length,
      approvals: backupData.data.approvalInstances.length
    };
    
    console.log('\n📊 备份数据统计：');
    console.log(`   品牌：${stats.brands} 条`);
    console.log(`   供应商：${stats.suppliers} 条`);
    console.log(`   项目：${stats.projects} 条`);
    console.log(`   项目收入：${stats.revenues} 条`);
    console.log(`   周预算：${stats.weeklyBudgets} 条`);
    console.log(`   附件：${stats.attachments} 条`);
    console.log(`   变更日志：${stats.changeLogs} 条`);
    console.log(`   审批实例：${stats.approvals} 条`);
    
    return backupPath;
    
  } catch (error) {
    console.error('❌ 备份失败：', error.message);
    throw error;
  }
}

// 清空业务数据（复用之前的逻辑）
async function clearBusinessData() {
  console.log('\n🚀 开始清空业务数据...');
  
  try {
    await prisma.$transaction(async (tx) => {
      const deletedApprovals = await tx.approvalInstance.deleteMany();
      console.log(`✓ 已清空审批实例数据：${deletedApprovals.count} 条`);

      const deletedChangeLogs = await tx.projectChangeLog.deleteMany();
      console.log(`✓ 已清空项目变更日志数据：${deletedChangeLogs.count} 条`);

      const deletedRevenues = await tx.projectRevenue.deleteMany();
      console.log(`✓ 已清空项目收入数据：${deletedRevenues.count} 条`);

      const deletedWeeklyBudgets = await tx.weeklyBudget.deleteMany();
      console.log(`✓ 已清空周预算数据：${deletedWeeklyBudgets.count} 条`);

      const deletedAttachments = await tx.attachment.deleteMany();
      console.log(`✓ 已清空附件数据：${deletedAttachments.count} 条`);

      const deletedProjects = await tx.project.deleteMany();
      console.log(`✓ 已清空项目数据：${deletedProjects.count} 条`);

      const deletedBrands = await tx.brand.deleteMany();
      console.log(`✓ 已清空品牌数据：${deletedBrands.count} 条`);

      const deletedSuppliers = await tx.supplier.deleteMany();
      console.log(`✓ 已清空供应商数据：${deletedSuppliers.count} 条`);
    });

    console.log('\n🎉 业务数据清空完成！');
    
  } catch (error) {
    console.error('❌ 清空数据失败：', error.message);
    throw error;
  }
}

// 恢复业务数据
async function restoreBusinessData(backupFile) {
  console.log(`📥 开始从备份文件恢复数据：${backupFile}`);
  
  try {
    // 读取备份文件
    const backupPath = path.isAbsolute(backupFile) ? backupFile : path.join(process.cwd(), 'backups', backupFile);
    const backupContent = await fs.readFile(backupPath, 'utf8');
    const backupData = JSON.parse(backupContent);
    
    console.log(`   备份时间：${backupData.timestamp}`);
    console.log(`   备份版本：${backupData.version}`);
    
    // 使用事务恢复数据
    await prisma.$transaction(async (tx) => {
      // 按依赖关系顺序恢复数据
      
      // 1. 恢复品牌数据
      if (backupData.data.brands?.length > 0) {
        for (const brand of backupData.data.brands) {
          await tx.brand.create({ data: brand });
        }
        console.log(`✓ 已恢复品牌数据：${backupData.data.brands.length} 条`);
      }
      
      // 2. 恢复供应商数据
      if (backupData.data.suppliers?.length > 0) {
        for (const supplier of backupData.data.suppliers) {
          await tx.supplier.create({ data: supplier });
        }
        console.log(`✓ 已恢复供应商数据：${backupData.data.suppliers.length} 条`);
      }
      
      // 3. 恢复项目数据
      if (backupData.data.projects?.length > 0) {
        for (const project of backupData.data.projects) {
          await tx.project.create({ data: project });
        }
        console.log(`✓ 已恢复项目数据：${backupData.data.projects.length} 条`);
      }
      
      // 4. 恢复项目收入数据
      if (backupData.data.projectRevenues?.length > 0) {
        for (const revenue of backupData.data.projectRevenues) {
          await tx.projectRevenue.create({ data: revenue });
        }
        console.log(`✓ 已恢复项目收入数据：${backupData.data.projectRevenues.length} 条`);
      }
      
      // 5. 恢复周预算数据
      if (backupData.data.weeklyBudgets?.length > 0) {
        for (const budget of backupData.data.weeklyBudgets) {
          await tx.weeklyBudget.create({ data: budget });
        }
        console.log(`✓ 已恢复周预算数据：${backupData.data.weeklyBudgets.length} 条`);
      }
      
      // 6. 恢复附件数据
      if (backupData.data.attachments?.length > 0) {
        for (const attachment of backupData.data.attachments) {
          await tx.attachment.create({ data: attachment });
        }
        console.log(`✓ 已恢复附件数据：${backupData.data.attachments.length} 条`);
      }
      
      // 7. 恢复项目变更日志
      if (backupData.data.projectChangeLogs?.length > 0) {
        for (const log of backupData.data.projectChangeLogs) {
          await tx.projectChangeLog.create({ data: log });
        }
        console.log(`✓ 已恢复项目变更日志：${backupData.data.projectChangeLogs.length} 条`);
      }
      
      // 8. 恢复审批实例数据
      if (backupData.data.approvalInstances?.length > 0) {
        for (const approval of backupData.data.approvalInstances) {
          await tx.approvalInstance.create({ data: approval });
        }
        console.log(`✓ 已恢复审批实例数据：${backupData.data.approvalInstances.length} 条`);
      }
    });
    
    console.log('\n🎉 数据恢复完成！');
    
  } catch (error) {
    console.error('❌ 数据恢复失败：', error.message);
    throw error;
  }
}

// 询问用户确认
function askConfirmation(message) {
  return new Promise((resolve) => {
    rl.question(`${message} 请输入 "YES" 确认：`, (answer) => {
      resolve(answer === 'YES');
    });
  });
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    console.log('🔧 业务数据管理工具 (带备份功能)');
    console.log('='.repeat(50));

    // 测试数据库连接
    console.log('🔗 测试数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功');

    switch (command) {
      case 'backup':
        const backupFile = generateBackupFileName();
        await backupBusinessData(backupFile);
        break;
        
      case 'clear':
        const confirmed = await askConfirmation('\n⚠️  确定要清空所有业务数据吗？此操作会先自动备份数据。');
        if (!confirmed) {
          console.log('\n❌ 操作已取消');
          return;
        }
        
        // 先备份再清空
        const autoBackupFile = generateBackupFileName();
        await backupBusinessData(autoBackupFile);
        await clearBusinessData();
        console.log(`\n💾 数据已备份至：${autoBackupFile}`);
        console.log('如需恢复，请使用：node scripts/clear-business-data-with-backup.js restore ' + autoBackupFile);
        break;
        
      case 'restore':
        const restoreFile = args[1];
        if (!restoreFile) {
          console.error('❌ 请指定备份文件名');
          console.log('使用方法：node scripts/clear-business-data-with-backup.js restore <backup-file>');
          return;
        }
        
        const restoreConfirmed = await askConfirmation(`\n⚠️  确定要从备份文件 "${restoreFile}" 恢复数据吗？这会覆盖当前的业务数据。`);
        if (!restoreConfirmed) {
          console.log('\n❌ 操作已取消');
          return;
        }
        
        await restoreBusinessData(restoreFile);
        break;
        
      default:
        console.log('\n使用方法：');
        console.log('  备份数据：node scripts/clear-business-data-with-backup.js backup');
        console.log('  清空数据：node scripts/clear-business-data-with-backup.js clear');
        console.log('  恢复数据：node scripts/clear-business-data-with-backup.js restore <backup-file>');
        break;
    }

    console.log('\n✅ 操作完成！');
    
  } catch (error) {
    console.error('\n💥 执行过程中发生错误：', error);
    process.exit(1);
  } finally {
    rl.close();
    await prisma.$disconnect();
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (error) => {
  console.error('💥 未处理的Promise拒绝：', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n\n👋 收到中断信号，正在退出...');
  rl.close();
  prisma.$disconnect().then(() => {
    process.exit(0);
  });
});

// 运行主函数 - 只有在直接调用时才运行
if (process.argv[1] && process.argv[1].includes('clear-business-data-with-backup.js')) {
  main();
}

export { backupBusinessData, clearBusinessData, restoreBusinessData };

