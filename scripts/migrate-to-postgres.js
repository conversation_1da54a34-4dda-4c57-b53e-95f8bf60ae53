#!/usr/bin/env node

/**
 * 数据迁移脚本：从内存存储迁移到PostgreSQL
 * 将现有的项目和品牌数据迁移到数据库
 */

import { PrismaClient } from '@prisma/client';
import { ProjectService } from '../src/services/project.js';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 初始化数据库连接
const prisma = new PrismaClient();

// 映射枚举值
function mapDocumentType(type) {
    const mapping = {
        'project_initiation': 'PROJECT_INITIATION'
    };
    return mapping[type] || 'PROJECT_INITIATION';
}

function mapContractType(type) {
    const mapping = {
        'annual_frame': 'ANNUAL_FRAME',
        'quarterly_frame': 'QUARTERLY_FRAME',
        'single': 'SINGLE',
        'po_order': 'PO_ORDER',
        'jing_task': 'JING_TASK'
    };
    return mapping[type] || 'SINGLE';
}

function mapProjectStatus(status) {
    const mapping = {
        'draft': 'DRAFT',
        'active': 'ACTIVE',
        'completed': 'COMPLETED',
        'cancelled': 'CANCELLED'
    };
    return mapping[status] || 'DRAFT';
}

function mapBrandStatus(status) {
    const mapping = {
        'active': 'ACTIVE',
        'inactive': 'INACTIVE'
    };
    return mapping[status] || 'ACTIVE';
}

// 迁移品牌数据
async function migrateBrands() {
    log('🏷️  开始迁移品牌数据...', 'yellow');
    
    try {
        const projectService = new ProjectService();
        const brandsResult = await projectService.getBrands({ pageSize: 1000 });
        const brands = brandsResult.brands;
        
        log(`   发现 ${brands.length} 个品牌`, 'blue');
        
        let migratedCount = 0;
        let skippedCount = 0;
        
        for (const brand of brands) {
            try {
                // 检查品牌是否已存在
                const existingBrand = await prisma.brand.findUnique({
                    where: { id: brand.id }
                });
                
                if (existingBrand) {
                    log(`   跳过已存在的品牌: ${brand.name}`, 'yellow');
                    skippedCount++;
                    continue;
                }
                
                await prisma.brand.create({
                    data: {
                        id: brand.id,
                        name: brand.name,
                        description: brand.description || null,
                        logo: brand.logo || null,
                        status: mapBrandStatus(brand.status),
                        createdBy: brand.createdBy || 'migration',
                        createdAt: brand.createdAt ? new Date(brand.createdAt) : new Date(),
                        updatedAt: brand.updatedAt ? new Date(brand.updatedAt) : new Date(),
                    }
                });
                
                log(`   ✅ 迁移品牌: ${brand.name}`, 'green');
                migratedCount++;
                
            } catch (error) {
                log(`   ❌ 迁移品牌失败 ${brand.name}: ${error.message}`, 'red');
            }
        }
        
        log(`✅ 品牌迁移完成: ${migratedCount} 个成功, ${skippedCount} 个跳过`, 'green');
        return { migrated: migratedCount, skipped: skippedCount };
        
    } catch (error) {
        log(`❌ 品牌迁移失败: ${error.message}`, 'red');
        throw error;
    }
}

// 迁移项目数据
async function migrateProjects() {
    log('📊 开始迁移项目数据...', 'yellow');
    
    try {
        const projectService = new ProjectService();
        const projectsResult = await projectService.getProjects({ pageSize: 1000 });
        const projects = projectsResult.projects;
        
        log(`   发现 ${projects.length} 个项目`, 'blue');
        
        let migratedCount = 0;
        let skippedCount = 0;
        let errorCount = 0;
        
        for (const project of projects) {
            try {
                // 检查项目是否已存在
                const existingProject = await prisma.project.findUnique({
                    where: { id: project.id }
                });
                
                if (existingProject) {
                    log(`   跳过已存在的项目: ${project.projectName}`, 'yellow');
                    skippedCount++;
                    continue;
                }
                
                // 检查关联的品牌是否存在
                const brand = await prisma.brand.findUnique({
                    where: { id: project.brandId }
                });
                
                if (!brand) {
                    log(`   ❌ 项目 ${project.projectName} 的品牌 ${project.brandId} 不存在`, 'red');
                    errorCount++;
                    continue;
                }
                
                await prisma.project.create({
                    data: {
                        id: project.id,
                        documentType: mapDocumentType(project.documentType),
                        brandId: project.brandId,
                        projectName: project.projectName,
                        startDate: new Date(project.period.startDate),
                        endDate: new Date(project.period.endDate),
                        planningBudget: project.budget.planningBudget,
                        influencerBudget: project.budget.influencerBudget,
                        adBudget: project.budget.adBudget,
                        otherBudget: project.budget.otherBudget,
                        influencerCost: project.cost.influencerCost,
                        adCost: project.cost.adCost,
                        otherCost: project.cost.otherCost,
                        estimatedInfluencerRebate: project.cost.estimatedInfluencerRebate,
                        executorPM: project.executorPM,
                        contentMediaIds: project.contentMediaIds,
                        contractType: mapContractType(project.contractType),
                        settlementRules: project.settlementRules,
                        kpi: project.kpi,
                        status: mapProjectStatus(project.status),
                        createdBy: project.createdBy || 'migration',
                        updatedBy: project.updatedBy || 'migration',
                        createdAt: project.createdAt ? new Date(project.createdAt) : new Date(),
                        updatedAt: project.updatedAt ? new Date(project.updatedAt) : new Date(),
                    }
                });
                
                log(`   ✅ 迁移项目: ${project.projectName}`, 'green');
                migratedCount++;
                
            } catch (error) {
                log(`   ❌ 迁移项目失败 ${project.projectName}: ${error.message}`, 'red');
                errorCount++;
            }
        }
        
        log(`✅ 项目迁移完成: ${migratedCount} 个成功, ${skippedCount} 个跳过, ${errorCount} 个失败`, 'green');
        return { migrated: migratedCount, skipped: skippedCount, errors: errorCount };
        
    } catch (error) {
        log(`❌ 项目迁移失败: ${error.message}`, 'red');
        throw error;
    }
}

// 验证迁移结果
async function validateMigration() {
    log('🔍 验证迁移结果...', 'yellow');
    
    try {
        const [brandCount, projectCount] = await Promise.all([
            prisma.brand.count(),
            prisma.project.count()
        ]);
        
        log(`   数据库中的品牌数量: ${brandCount}`, 'blue');
        log(`   数据库中的项目数量: ${projectCount}`, 'blue');
        
        // 验证数据完整性
        const projectsWithoutBrand = await prisma.project.count({
            where: {
                brand: null
            }
        });
        
        if (projectsWithoutBrand > 0) {
            log(`   ⚠️  发现 ${projectsWithoutBrand} 个项目没有关联品牌`, 'yellow');
        }
        
        // 验证利润计算
        const sampleProjects = await prisma.project.findMany({
            take: 5,
            include: {
                brand: true
            }
        });
        
        log('   验证利润计算:', 'blue');
        for (const project of sampleProjects) {
            const totalCost = Number(project.influencerCost) + Number(project.adCost) + Number(project.otherCost);
            const profit = Number(project.planningBudget) - totalCost + Number(project.estimatedInfluencerRebate);
            const grossMargin = Number(project.planningBudget) > 0 ? (profit / Number(project.planningBudget)) * 100 : 0;
            
            log(`     ${project.projectName}: 利润=${profit.toFixed(2)}, 毛利率=${grossMargin.toFixed(2)}%`, 'cyan');
        }
        
        log('✅ 迁移验证完成', 'green');
        
    } catch (error) {
        log(`❌ 迁移验证失败: ${error.message}`, 'red');
        throw error;
    }
}

// 清理数据（可选）
async function cleanupData() {
    log('🧹 清理重复数据...', 'yellow');
    
    try {
        // 查找重复的品牌名称
        const duplicateBrands = await prisma.$queryRaw`
            SELECT name, COUNT(*) as count 
            FROM brands 
            GROUP BY name 
            HAVING COUNT(*) > 1
        `;
        
        if (duplicateBrands.length > 0) {
            log(`   发现 ${duplicateBrands.length} 个重复的品牌名称`, 'yellow');
            for (const dup of duplicateBrands) {
                log(`     重复品牌: ${dup.name} (${dup.count} 个)`, 'yellow');
            }
        }
        
        // 查找孤立的项目（没有品牌的项目）
        const orphanProjects = await prisma.project.findMany({
            where: {
                brand: null
            },
            select: {
                id: true,
                projectName: true,
                brandId: true
            }
        });
        
        if (orphanProjects.length > 0) {
            log(`   发现 ${orphanProjects.length} 个孤立项目`, 'yellow');
            for (const project of orphanProjects) {
                log(`     孤立项目: ${project.projectName} (品牌ID: ${project.brandId})`, 'yellow');
            }
        }
        
        log('✅ 数据清理检查完成', 'green');
        
    } catch (error) {
        log(`❌ 数据清理失败: ${error.message}`, 'red');
    }
}

// 主函数
async function main() {
    log('🚀 开始数据迁移到PostgreSQL', 'magenta');
    log('=====================================', 'magenta');
    log('', 'reset');
    
    try {
        // 检查数据库连接
        await prisma.$connect();
        log('✅ 数据库连接成功', 'green');
        log('', 'reset');
        
        // 执行迁移
        const brandResult = await migrateBrands();
        log('', 'reset');
        
        const projectResult = await migrateProjects();
        log('', 'reset');
        
        // 验证迁移结果
        await validateMigration();
        log('', 'reset');
        
        // 清理数据
        await cleanupData();
        log('', 'reset');
        
        // 显示迁移总结
        log('📊 迁移总结:', 'magenta');
        log(`   品牌: ${brandResult.migrated} 个迁移, ${brandResult.skipped} 个跳过`, 'cyan');
        log(`   项目: ${projectResult.migrated} 个迁移, ${projectResult.skipped} 个跳过, ${projectResult.errors} 个失败`, 'cyan');
        log('', 'reset');
        
        if (projectResult.errors > 0) {
            log('⚠️  迁移完成，但有部分数据失败，请检查日志', 'yellow');
        } else {
            log('🎉 数据迁移完全成功！', 'green');
        }
        
    } catch (error) {
        log(`💥 迁移失败: ${error.message}`, 'red');
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🚀 数据迁移工具

功能:
  - 将内存存储的数据迁移到PostgreSQL数据库
  - 验证数据完整性和关联关系
  - 清理重复和孤立数据

用法:
  node scripts/migrate-to-postgres.js [选项]

选项:
  --help, -h     显示帮助信息

环境变量:
  DATABASE_URL   PostgreSQL数据库连接字符串

示例:
  node scripts/migrate-to-postgres.js
`);
    process.exit(0);
}

// 运行迁移
main().catch(error => {
    log(`💥 迁移工具运行失败: ${error.message}`, 'red');
    process.exit(1);
});
