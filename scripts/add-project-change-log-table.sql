-- 添加项目变更记录表的迁移脚本
-- 执行前请确保已备份数据库

-- 1. 创建变更类型枚举
CREATE TYPE change_type AS ENUM (
  'CREATE',
  'UPDATE', 
  'DELETE',
  'STATUS_CHANGE',
  'APPROVAL',
  'ATTACHMENT'
);

-- 2. 创建项目变更记录表
CREATE TABLE project_change_logs (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  
  -- 基本信息
  change_type change_type NOT NULL DEFAULT 'UPDATE',
  change_title VARCHAR(200) NOT NULL,
  change_details JSONB,
  
  -- 变更前后数据对比
  before_data JSONB,
  after_data JSONB,
  
  -- 变更字段信息
  changed_fields TEXT[] NOT NULL DEFAULT '{}',
  
  -- 操作信息
  operator_id VARCHAR(50) NOT NULL,
  operator_name VARCHAR(100) NOT NULL,
  operator_ip VARCHAR(45),
  user_agent VARCHAR(500),
  
  -- 业务信息
  reason TEXT,
  description TEXT,
  
  -- 关联项目
  project_id TEXT NOT NULL,
  
  -- 时间信息
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- 外键约束
  CONSTRAINT fk_project_change_logs_project 
    FOREIGN KEY (project_id) 
    REFERENCES projects(id) 
    ON DELETE CASCADE
);

-- 3. 创建索引
CREATE INDEX idx_project_change_logs_project_created 
  ON project_change_logs(project_id, created_at);

CREATE INDEX idx_project_change_logs_operator 
  ON project_change_logs(operator_id);

CREATE INDEX idx_project_change_logs_change_type 
  ON project_change_logs(change_type);

CREATE INDEX idx_project_change_logs_created_at 
  ON project_change_logs(created_at);

-- 4. 添加表注释
COMMENT ON TABLE project_change_logs IS '项目变更记录表';
COMMENT ON COLUMN project_change_logs.id IS '变更记录ID';
COMMENT ON COLUMN project_change_logs.change_type IS '变更类型';
COMMENT ON COLUMN project_change_logs.change_title IS '变更标题';
COMMENT ON COLUMN project_change_logs.change_details IS '变更详情(JSON格式)';
COMMENT ON COLUMN project_change_logs.before_data IS '变更前的数据';
COMMENT ON COLUMN project_change_logs.after_data IS '变更后的数据';
COMMENT ON COLUMN project_change_logs.changed_fields IS '变更的字段列表';
COMMENT ON COLUMN project_change_logs.operator_id IS '操作人员钉钉用户ID';
COMMENT ON COLUMN project_change_logs.operator_name IS '操作人员姓名';
COMMENT ON COLUMN project_change_logs.operator_ip IS '操作人员IP地址';
COMMENT ON COLUMN project_change_logs.user_agent IS '用户代理信息';
COMMENT ON COLUMN project_change_logs.reason IS '变更原因';
COMMENT ON COLUMN project_change_logs.description IS '变更描述';
COMMENT ON COLUMN project_change_logs.project_id IS '关联项目ID';
COMMENT ON COLUMN project_change_logs.created_at IS '创建时间';

-- 5. 验证表创建
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'project_change_logs'
ORDER BY ordinal_position;

-- 6. 验证索引创建
SELECT 
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename = 'project_change_logs';

COMMIT;
