// 测试字段对比修复：验证关联信息不会影响变更检测
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testFieldComparisonFix() {
  try {
    console.log('🧪 测试字段对比修复...');
    
    // 1. 模拟数据库查询返回的完整项目数据（包含关联信息）
    const beforeProject = {
      "id": "test-project-001",
      "kpi": "测试KPI内容",
      "cost": {
        "adCost": 10,
        "otherCost": 10,
        "influencerCost": 10,
        "estimatedInfluencerRebate": 5
      },
      "brand": {
        "id": "brand-001",
        "logo": "",
        "name": "测试品牌",
        "status": "ACTIVE",
        "createdAt": "2025-06-19T14:38:47.407Z",
        "createdBy": "current-user",
        "updatedAt": "2025-06-19T14:38:47.407Z",
        "description": ""
      },
      "budget": {
        "adBudget": 99,
        "otherBudget": 10,
        "planningBudget": 209,
        "influencerBudget": 100
      },
      "period": {
        "endDate": "2025-09-30T00:00:00.000Z",
        "startDate": "2025-08-01T00:00:00.000Z"
      },
      "profit": {
        "profit": 184,
        "grossMargin": 88.04
      },
      "status": "DRAFT",
      "brandId": "brand-001",
      "createdAt": "2025-06-19T14:44:27.225Z",
      "createdBy": "current-user",
      "updatedAt": "2025-06-20T07:43:00.520Z",
      "updatedBy": "user-001",
      "executorPM": "user-pm-001",
      "attachments": [],
      "projectName": "测试项目",
      "contractType": "JING_TASK",
      "documentType": "PROJECT_INITIATION",
      "executorPMInfo": {
        "name": "项目经理",
        "avatar": "https://example.com/avatar.jpg",
        "userid": "user-pm-001",
        "department": "1, 1000834392"
      },
      "contentMediaIds": ["user-001", "user-002"],
      "paymentTermDays": 30,
      "settlementRules": "测试结算规则",
      "contentMediaInfo": [
        {
          "name": "用户1",
          "avatar": "https://example.com/avatar1.jpg",
          "userid": "user-001",
          "department": "1, 1000834392"
        },
        {
          "name": "用户2",
          "avatar": "https://example.com/avatar2.jpg",
          "userid": "user-002",
          "department": "1"
        }
      ],
      "expectedPaymentMonth": "2026-12",
      "contractSigningStatus": "NO_CONTRACT"
    };

    // 2. 模拟用户提交的更新数据（只包含基本字段，没有关联信息）
    const updateRequest = {
      "id": "test-project-001",
      "kpi": "测试KPI内容", // 相同值
      "cost": {
        "adCost": 10, // 相同值
        "otherCost": 10, // 相同值
        "influencerCost": 10, // 相同值
        "estimatedInfluencerRebate": 5 // 相同值
      },
      "budget": {
        "adBudget": 99, // 相同值
        "otherBudget": 10, // 相同值
        "planningBudget": 209, // 相同值
        "influencerBudget": 100 // 相同值
      },
      "period": {
        "endDate": "2025-09-30T00:00:00.000Z", // 相同值
        "startDate": "2025-08-01T00:00:00.000Z" // 相同值
      },
      "status": "DRAFT", // 相同值
      "brandId": "brand-001", // 相同值
      "executorPM": "user-pm-001", // 相同值
      "projectName": "测试项目", // 相同值
      "contractType": "JING_TASK", // 相同值
      "documentType": "PROJECT_INITIATION", // 相同值
      "contentMediaIds": ["user-001", "user-002"], // 相同值
      "paymentTermDays": 30, // 相同值
      "settlementRules": "测试结算规则", // 相同值
      "expectedPaymentMonth": "2026-12", // 相同值
      "contractSigningStatus": "PENDING" // 这个字段发生了变更
    };

    // 3. 模拟更新后的项目数据（包含关联信息，但只有contractSigningStatus变更）
    const afterProject = {
      ...beforeProject,
      "contractSigningStatus": "PENDING", // 唯一的变更
      "updatedAt": "2025-06-20T07:50:49.351Z", // 系统字段变更（应该被排除）
      "updatedBy": "user-002" // 系统字段变更（应该被排除）
    };

    console.log('\n📊 测试数据准备完成');
    console.log(`   beforeProject 字段数: ${Object.keys(beforeProject).length}`);
    console.log(`   updateRequest 字段数: ${Object.keys(updateRequest).length}`);
    console.log(`   afterProject 字段数: ${Object.keys(afterProject).length}`);

    // 4. 测试修复前的逻辑（不传递updateRequest）
    console.log('\n🔍 测试修复前的逻辑（对比所有字段）...');
    const oldChangedFields = getChangedFieldsOld(beforeProject, afterProject);
    console.log(`   修复前变更字段数: ${oldChangedFields.length}`);
    console.log(`   修复前变更字段: [${oldChangedFields.join(', ')}]`);

    // 5. 测试修复后的逻辑（传递updateRequest）
    console.log('\n✅ 测试修复后的逻辑（只对比提交的字段）...');
    const newChangedFields = getChangedFieldsNew(beforeProject, afterProject, updateRequest);
    console.log(`   修复后变更字段数: ${newChangedFields.length}`);
    console.log(`   修复后变更字段: [${newChangedFields.join(', ')}]`);

    // 6. 验证结果
    console.log('\n📋 结果验证:');
    const expectedChangedFields = ['contractSigningStatus'];
    const unexpectedFields = ['brand', 'executorPMInfo', 'contentMediaInfo', 'attachments', 'updatedAt', 'updatedBy'];

    const isCorrect = expectedChangedFields.every(field => newChangedFields.includes(field)) &&
                     newChangedFields.every(field => expectedChangedFields.includes(field));

    const hasUnexpectedFields = unexpectedFields.some(field => newChangedFields.includes(field));

    if (isCorrect && !hasUnexpectedFields) {
      console.log('✅ 字段对比修复成功！');
      console.log(`   ✅ 正确识别变更字段: ${expectedChangedFields.join(', ')}`);
      console.log(`   ✅ 正确排除关联字段: ${unexpectedFields.join(', ')}`);
      console.log(`   ✅ 避免了"伪变更"问题`);
    } else {
      console.log('❌ 字段对比修复失败');
      if (!isCorrect) {
        console.log(`   ❌ 变更字段不正确，预期: [${expectedChangedFields.join(', ')}]，实际: [${newChangedFields.join(', ')}]`);
      }
      if (hasUnexpectedFields) {
        console.log(`   ❌ 包含了不应该的字段: ${unexpectedFields.filter(f => newChangedFields.includes(f)).join(', ')}`);
      }
    }

    console.log('\n🎉 字段对比修复测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 修复前的字段对比逻辑（对比所有字段）
function getChangedFieldsOld(before, after) {
  const changedFields = [];
  const excludeFields = new Set(['id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy']);
  const allKeys = new Set([...Object.keys(before || {}), ...Object.keys(after || {})]);

  for (const key of allKeys) {
    if (excludeFields.has(key)) continue;
    if (JSON.stringify(before?.[key]) !== JSON.stringify(after?.[key])) {
      changedFields.push(key);
    }
  }
  return changedFields;
}

// 修复后的字段对比逻辑（只对比提交的字段）
function getChangedFieldsNew(before, after, updateRequest) {
  const changedFields = [];
  const excludeFields = new Set([
    'id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy',
    'brand', 'executorPMInfo', 'contentMediaInfo', 'attachments'
  ]);

  const keysToCompare = updateRequest 
    ? new Set(Object.keys(updateRequest).filter(key => !excludeFields.has(key)))
    : new Set([...Object.keys(before || {}), ...Object.keys(after || {})].filter(key => !excludeFields.has(key)));

  for (const key of keysToCompare) {
    if (excludeFields.has(key)) continue;
    if (JSON.stringify(before?.[key]) !== JSON.stringify(after?.[key])) {
      changedFields.push(key);
    }
  }
  return changedFields;
}

testFieldComparisonFix();
