#!/usr/bin/env node

/**
 * API文档验证脚本
 * 验证API文档中的所有接口是否正常工作
 */

import http from 'http';
import { URL } from 'url';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';
const TEST_TIMEOUT = 10000;

// 测试结果统计
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

// HTTP请求函数
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: data
                    });
                }
            });
        });

        req.on('error', reject);
        req.setTimeout(TEST_TIMEOUT, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });

        if (options.body) {
            req.write(options.body);
        }

        req.end();
    });
}

// 测试函数
async function runTest(name, testFn) {
    totalTests++;
    const startTime = Date.now();
    
    try {
        await testFn();
        const duration = Date.now() - startTime;
        log(`  ✅ ${name} (${duration}ms)`, 'green');
        passedTests++;
        return true;
    } catch (error) {
        const duration = Date.now() - startTime;
        log(`  ❌ ${name} (${duration}ms): ${error.message}`, 'red');
        failedTests++;
        return false;
    }
}

// 验证API响应格式
function validateApiResponse(response, expectedFields = []) {
    if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}`);
    }
    
    if (!response.data || typeof response.data !== 'object') {
        throw new Error('响应数据格式错误');
    }
    
    if (!response.data.hasOwnProperty('success')) {
        throw new Error('响应缺少success字段');
    }
    
    if (!response.data.hasOwnProperty('message')) {
        throw new Error('响应缺少message字段');
    }
    
    // 验证特定字段
    for (const field of expectedFields) {
        if (!response.data.data || !response.data.data.hasOwnProperty(field)) {
            throw new Error(`响应数据缺少字段: ${field}`);
        }
    }
}

// 测试品牌管理API
async function testBrandAPIs() {
    log('\n🏷️  验证品牌管理API', 'yellow');
    
    // 测试获取品牌列表
    await runTest('GET /brands - 获取品牌列表', async () => {
        const response = await makeRequest(`${API_BASE_URL}/brands`);
        validateApiResponse(response, ['brands', 'total', 'page']);
        
        if (!Array.isArray(response.data.data.brands)) {
            throw new Error('brands字段应该是数组');
        }
    });
    
    // 测试带参数的品牌列表
    await runTest('GET /brands?page=1&pageSize=5 - 分页查询', async () => {
        const response = await makeRequest(`${API_BASE_URL}/brands?page=1&pageSize=5`);
        validateApiResponse(response, ['brands', 'total', 'page', 'pageSize']);
        
        if (response.data.data.pageSize !== 5) {
            throw new Error('pageSize参数未生效');
        }
    });
    
    // 测试状态筛选
    await runTest('GET /brands?status=active - 状态筛选', async () => {
        const response = await makeRequest(`${API_BASE_URL}/brands?status=active`);
        validateApiResponse(response, ['brands']);
    });
    
    // 测试搜索功能
    await runTest('GET /brands?keyword=test - 关键字搜索', async () => {
        const response = await makeRequest(`${API_BASE_URL}/brands?keyword=test`);
        validateApiResponse(response, ['brands']);
    });
}

// 测试项目管理API
async function testProjectAPIs() {
    log('\n📊 验证项目管理API', 'yellow');
    
    // 测试获取项目列表
    await runTest('GET /projects - 获取项目列表', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects`);
        validateApiResponse(response, ['projects', 'total', 'page']);
        
        if (!Array.isArray(response.data.data.projects)) {
            throw new Error('projects字段应该是数组');
        }
    });
    
    // 测试项目分页
    await runTest('GET /projects?page=1&pageSize=10 - 项目分页', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects?page=1&pageSize=10`);
        validateApiResponse(response, ['projects', 'total', 'page', 'pageSize']);
        
        if (response.data.data.pageSize !== 10) {
            throw new Error('pageSize参数未生效');
        }
    });
    
    // 测试状态筛选
    await runTest('GET /projects?status=active - 状态筛选', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects?status=active`);
        validateApiResponse(response, ['projects']);
    });
    
    // 测试合同类型筛选
    await runTest('GET /projects?contractType=single - 合同类型筛选', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects?contractType=single`);
        validateApiResponse(response, ['projects']);
    });
    
    // 测试关键字搜索
    await runTest('GET /projects?keyword=test - 关键字搜索', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects?keyword=test`);
        validateApiResponse(response, ['projects']);
    });
    
    // 测试日期范围筛选
    await runTest('GET /projects?startDate=2024-01-01&endDate=2024-12-31 - 日期筛选', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects?startDate=2024-01-01&endDate=2024-12-31`);
        validateApiResponse(response, ['projects']);
    });
}

// 测试统计API
async function testStatsAPIs() {
    log('\n📈 验证统计分析API', 'yellow');
    
    await runTest('GET /projects/stats - 获取项目统计', async () => {
        const response = await makeRequest(`${API_BASE_URL}/projects/stats`);
        validateApiResponse(response);
        
        const stats = response.data.data;
        const requiredFields = [
            'totalProjects', 'activeProjects', 'completedProjects',
            'totalBudget', 'totalProfit', 'averageGrossMargin',
            'projectsByBrand', 'projectsByContractType'
        ];
        
        for (const field of requiredFields) {
            if (!stats.hasOwnProperty(field)) {
                throw new Error(`统计数据缺少字段: ${field}`);
            }
        }
        
        // 验证数据类型
        if (typeof stats.totalProjects !== 'number') {
            throw new Error('totalProjects应该是数字类型');
        }
        
        if (!Array.isArray(stats.projectsByBrand)) {
            throw new Error('projectsByBrand应该是数组类型');
        }
        
        if (!Array.isArray(stats.projectsByContractType)) {
            throw new Error('projectsByContractType应该是数组类型');
        }
    });
}

// 测试错误处理
async function testErrorHandling() {
    log('\n❌ 验证错误处理', 'yellow');
    
    // 测试404错误
    await runTest('GET /brands/nonexistent - 404错误', async () => {
        const response = await makeRequest(`${API_BASE_URL}/brands/nonexistent`);
        
        if (response.status !== 404) {
            throw new Error(`期望404状态码，实际收到${response.status}`);
        }
        
        if (response.data.success !== false) {
            throw new Error('错误响应的success字段应该为false');
        }
    });
    
    // 测试无效参数
    await runTest('GET /brands?page=0 - 无效参数', async () => {
        const response = await makeRequest(`${API_BASE_URL}/brands?page=0`);
        
        // 应该返回400或者自动修正为1
        if (response.status === 400) {
            if (response.data.success !== false) {
                throw new Error('错误响应的success字段应该为false');
            }
        } else if (response.status === 200) {
            // 自动修正的情况
            if (response.data.data.page !== 1) {
                throw new Error('无效页码应该被修正为1');
            }
        } else {
            throw new Error(`期望400或200状态码，实际收到${response.status}`);
        }
    });
}

// 测试API响应时间
async function testPerformance() {
    log('\n⚡ 验证API性能', 'yellow');
    
    const performanceTests = [
        { name: '品牌列表', url: `${API_BASE_URL}/brands`, maxTime: 1000 },
        { name: '项目列表', url: `${API_BASE_URL}/projects`, maxTime: 1000 },
        { name: '项目统计', url: `${API_BASE_URL}/projects/stats`, maxTime: 2000 },
    ];
    
    for (const test of performanceTests) {
        await runTest(`${test.name} - 响应时间 < ${test.maxTime}ms`, async () => {
            const startTime = Date.now();
            const response = await makeRequest(test.url);
            const duration = Date.now() - startTime;
            
            validateApiResponse(response);
            
            if (duration > test.maxTime) {
                throw new Error(`响应时间${duration}ms超过限制${test.maxTime}ms`);
            }
        });
    }
}

// 主函数
async function main() {
    log('🔍 API文档验证工具', 'magenta');
    log('=====================================', 'magenta');
    log(`📍 测试地址: ${API_BASE_URL}`, 'cyan');
    log(`⏱️  超时时间: ${TEST_TIMEOUT}ms`, 'cyan');
    log('', 'reset');
    
    const startTime = Date.now();
    
    try {
        // 验证各个API模块
        await testBrandAPIs();
        await testProjectAPIs();
        await testStatsAPIs();
        await testErrorHandling();
        await testPerformance();
        
    } catch (error) {
        log(`\n💥 验证过程中发生错误: ${error.message}`, 'red');
    }
    
    const totalTime = Date.now() - startTime;
    
    // 显示验证结果
    log('\n📊 验证结果统计', 'magenta');
    log('=====================================', 'magenta');
    log(`总验证项: ${totalTests}`, 'blue');
    log(`通过: ${passedTests}`, 'green');
    log(`失败: ${failedTests}`, 'red');
    log(`成功率: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`, 'cyan');
    log(`总耗时: ${totalTime}ms`, 'cyan');
    log('', 'reset');
    
    if (failedTests === 0) {
        log('🎉 所有API文档验证通过！', 'green');
        log('📚 API文档与实际接口完全一致', 'green');
        process.exit(0);
    } else {
        log('⚠️  部分API验证失败，请检查文档或接口实现', 'yellow');
        process.exit(1);
    }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🔍 API文档验证工具

功能:
  - 验证所有API接口是否正常工作
  - 检查响应格式是否符合文档
  - 测试错误处理机制
  - 验证API性能表现
  - 确保文档与实际接口一致

用法:
  node scripts/validate-api-docs.js [选项]

选项:
  --help, -h     显示帮助信息

环境变量:
  API_BASE_URL   API基础地址 (默认: http://localhost:3000/api)

示例:
  node scripts/validate-api-docs.js
  API_BASE_URL=http://localhost:8080/api node scripts/validate-api-docs.js
`);
    process.exit(0);
}

// 运行验证
main().catch(error => {
    log(`💥 验证工具运行失败: ${error.message}`, 'red');
    process.exit(1);
});
