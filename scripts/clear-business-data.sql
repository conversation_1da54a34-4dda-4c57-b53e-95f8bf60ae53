-- 清空业务数据脚本
-- 此脚本会清空所有业务相关数据，但保留系统数据（用户、权限、角色等）
-- 
-- 警告：此操作不可逆，请在执行前确保已备份重要数据！
--
-- 使用方法：
-- 1. 连接到数据库
-- 2. 执行此脚本：psql -d your_database -f clear-business-data.sql
-- 
-- 作者：系统管理员
-- 创建时间：2025-01-22

BEGIN;

-- 显示开始信息
SELECT '开始清空业务数据...' as message;

-- 1. 清空审批实例表（有外键依赖，需要先清空）
DELETE FROM approval_instances;
SELECT '✓ 已清空审批实例数据' as message;

-- 2. 清空项目变更日志表（依赖项目表）
DELETE FROM project_change_logs;
SELECT '✓ 已清空项目变更日志数据' as message;

-- 3. 清空项目收入表（依赖项目表）
DELETE FROM project_revenues;
SELECT '✓ 已清空项目收入数据' as message;

-- 4. 清空周预算表（依赖项目和供应商表）
DELETE FROM weekly_budgets;
SELECT '✓ 已清空周预算数据' as message;

-- 5. 清空附件表（依赖项目表）
DELETE FROM attachments;
SELECT '✓ 已清空附件数据' as message;

-- 6. 清空项目表
DELETE FROM projects;
SELECT '✓ 已清空项目数据' as message;

-- 7. 清空品牌表
DELETE FROM brands;
SELECT '✓ 已清空品牌数据' as message;

-- 8. 清空供应商表
DELETE FROM suppliers;
SELECT '✓ 已清空供应商数据' as message;

-- 显示清空结果统计
SELECT 
  '业务数据清空完成！' as message,
  '已清空的表：' as tables,
  'approval_instances, project_change_logs, project_revenues, weekly_budgets, attachments, projects, brands, suppliers' as cleared_tables;

-- 显示保留的系统数据统计
SELECT 
  '系统数据已保留：' as message,
  (SELECT COUNT(*) FROM users) as user_count,
  (SELECT COUNT(*) FROM departments) as department_count,
  (SELECT COUNT(*) FROM roles) as role_count,
  (SELECT COUNT(*) FROM permissions) as permission_count,
  (SELECT COUNT(*) FROM role_permissions) as role_permission_count,
  (SELECT COUNT(*) FROM user_roles) as user_role_count,
  (SELECT COUNT(*) FROM department_roles) as department_role_count;

COMMIT;

SELECT '业务数据清空操作已完成！' as final_message;
