#!/usr/bin/env node

/**
 * 创建测试数据脚本
 * 为API文档验证创建必要的测试数据
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

async function createTestData() {
    try {
        log('🚀 创建API测试数据', 'magenta');
        log('=====================================', 'magenta');

        // 创建测试品牌
        log('\n🏷️  创建测试品牌...', 'yellow');
        
        const brands = [];
        const brandNames = ['可口可乐', '百事可乐', '星巴克', '麦当劳', '肯德基'];
        
        for (const name of brandNames) {
            const brand = await prisma.brand.create({
                data: {
                    name: name,
                    description: `${name}品牌描述`,
                    logo: `https://example.com/logos/${name.toLowerCase()}.png`,
                    status: 'ACTIVE',
                    createdBy: 'test-user'
                }
            });
            brands.push(brand);
            log(`   ✅ 创建品牌: ${brand.name} (${brand.id})`, 'green');
        }

        // 创建测试项目
        log('\n📊 创建测试项目...', 'yellow');
        
        const projects = [];
        const projectTemplates = [
            {
                name: '春节营销活动',
                contractType: 'ANNUAL_FRAME',
                budget: 1000000,
                status: 'ACTIVE'
            },
            {
                name: '夏季促销活动',
                contractType: 'QUARTERLY_FRAME',
                budget: 500000,
                status: 'ACTIVE'
            },
            {
                name: '双十一大促',
                contractType: 'SINGLE',
                budget: 2000000,
                status: 'COMPLETED'
            },
            {
                name: '品牌升级项目',
                contractType: 'PO_ORDER',
                budget: 800000,
                status: 'ACTIVE'
            },
            {
                name: '新品发布会',
                contractType: 'JING_TASK',
                budget: 300000,
                status: 'DRAFT'
            }
        ];

        for (let i = 0; i < projectTemplates.length; i++) {
            const template = projectTemplates[i];
            const brand = brands[i % brands.length];
            
            const planningBudget = template.budget;
            const influencerBudget = planningBudget * 0.4;
            const adBudget = planningBudget * 0.3;
            const otherBudget = planningBudget * 0.1;
            
            const influencerCost = influencerBudget * 0.85;
            const adCost = adBudget * 0.9;
            const otherCost = otherBudget * 0.8;
            const estimatedInfluencerRebate = influencerCost * 0.05;

            const project = await prisma.project.create({
                data: {
                    documentType: 'PROJECT_INITIATION',
                    brandId: brand.id,
                    projectName: template.name,
                    startDate: new Date('2024-01-01'),
                    endDate: new Date('2024-12-31'),
                    planningBudget: planningBudget,
                    influencerBudget: influencerBudget,
                    adBudget: adBudget,
                    otherBudget: otherBudget,
                    influencerCost: influencerCost,
                    adCost: adCost,
                    otherCost: otherCost,
                    estimatedInfluencerRebate: estimatedInfluencerRebate,
                    executorPM: 'test-pm-001',
                    contentMediaIds: ['test-media-001', 'test-media-002'],
                    contractType: template.contractType,
                    settlementRules: '按月结算',
                    kpi: '提升品牌知名度20%',
                    status: template.status,
                    createdBy: 'test-user',
                    updatedBy: 'test-user'
                }
            });
            
            projects.push(project);
            log(`   ✅ 创建项目: ${project.projectName} (${project.id})`, 'green');
            log(`      💰 预算: ¥${planningBudget.toLocaleString()}`, 'cyan');
            log(`      🏷️  品牌: ${brand.name}`, 'cyan');
            log(`      📋 合同: ${template.contractType}`, 'cyan');
            log(`      📊 状态: ${template.status}`, 'cyan');
        }

        // 验证数据创建
        log('\n📈 验证创建的数据...', 'yellow');
        
        const brandCount = await prisma.brand.count();
        const projectCount = await prisma.project.count();
        
        log(`   📊 品牌总数: ${brandCount}`, 'blue');
        log(`   📊 项目总数: ${projectCount}`, 'blue');

        // 测试统计查询
        const stats = await prisma.project.aggregate({
            _count: { id: true },
            _sum: { planningBudget: true }
        });

        log(`   💰 总预算: ¥${(stats._sum.planningBudget || 0).toLocaleString()}`, 'blue');

        log('\n🎉 测试数据创建完成！', 'green');
        log('=====================================', 'green');
        log('📚 现在可以运行API文档验证:', 'cyan');
        log('   npm run test:docs', 'cyan');
        log('🌐 或访问在线API文档:', 'cyan');
        log('   http://localhost:3000/api-docs.html', 'cyan');

    } catch (error) {
        log(`❌ 创建测试数据失败: ${error.message}`, 'red');
        console.error(error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// 清理测试数据函数
async function cleanTestData() {
    try {
        log('🧹 清理测试数据...', 'yellow');
        
        // 删除测试项目
        const deletedProjects = await prisma.project.deleteMany({
            where: {
                createdBy: 'test-user'
            }
        });
        
        // 删除测试品牌
        const deletedBrands = await prisma.brand.deleteMany({
            where: {
                createdBy: 'test-user'
            }
        });
        
        log(`   🗑️  删除了 ${deletedProjects.count} 个测试项目`, 'green');
        log(`   🗑️  删除了 ${deletedBrands.count} 个测试品牌`, 'green');
        log('✅ 测试数据清理完成', 'green');
        
    } catch (error) {
        log(`❌ 清理测试数据失败: ${error.message}`, 'red');
        console.error(error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// 处理命令行参数
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 API测试数据管理工具

功能:
  - 创建API文档验证所需的测试数据
  - 包含品牌和项目的完整测试数据集
  - 支持清理测试数据

用法:
  node scripts/create-test-data.js [选项]

选项:
  --clean, -c    清理测试数据
  --help, -h     显示帮助信息

示例:
  node scripts/create-test-data.js          # 创建测试数据
  node scripts/create-test-data.js --clean  # 清理测试数据
`);
    process.exit(0);
}

if (args.includes('--clean') || args.includes('-c')) {
    cleanTestData();
} else {
    createTestData();
}
