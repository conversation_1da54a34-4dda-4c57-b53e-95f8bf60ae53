#!/usr/bin/env node

/**
 * 测试钉钉Stream服务
 */

import { DingTalkStreamServiceV2 } from '../dist/services/dingtalk-stream-v2.js';

async function testStreamService() {
  console.log('🧪 开始测试钉钉Stream服务...');
  
  const streamService = new DingTalkStreamServiceV2();
  
  // 设置事件监听器
  streamService.on('message', (data) => {
    console.log('📨 收到消息事件:', data);
  });
  
  streamService.on('error', (error) => {
    console.error('❌ Stream服务错误:', error);
  });
  
  try {
    // 启动服务
    await streamService.start();
    console.log('✅ Stream服务启动成功');
    
    // 显示连接状态
    const status = streamService.getConnectionStatus();
    console.log('📊 连接状态:', status);
    
    // 保持连接一段时间用于测试
    console.log('⏰ 保持连接60秒用于测试...');
    await new Promise(resolve => setTimeout(resolve, 60000));
    
    // 停止服务
    await streamService.stop();
    console.log('✅ Stream服务已停止');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在停止测试...');
  process.exit(0);
});

// 运行测试
testStreamService()
  .then(() => {
    console.log('✅ 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
