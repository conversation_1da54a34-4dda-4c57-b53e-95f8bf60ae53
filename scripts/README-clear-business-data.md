# 业务数据清空工具

本目录包含了用于清空业务数据的脚本工具，支持保留系统数据（用户、权限、角色等）的同时清空所有业务相关数据。

## 📁 文件说明

### 1. `clear-business-data.sql`
- **类型**：SQL脚本
- **功能**：直接通过SQL命令清空业务数据
- **特点**：简单直接，适合数据库管理员使用

### 2. `clear-business-data.js`
- **类型**：Node.js脚本
- **功能**：使用Prisma ORM清空业务数据
- **特点**：交互式确认，详细的操作日志

### 3. `clear-business-data-with-backup.js`
- **类型**：Node.js脚本（推荐）
- **功能**：带备份功能的业务数据管理工具
- **特点**：自动备份、支持恢复、最安全的选择

## 🗂️ 数据分类

### 业务数据（会被清空）
- **品牌数据** (`brands`)
- **项目数据** (`projects`)
- **项目收入数据** (`project_revenues`)
- **周预算数据** (`weekly_budgets`)
- **供应商数据** (`suppliers`)
- **附件数据** (`attachments`)
- **项目变更日志** (`project_change_logs`)
- **审批实例数据** (`approval_instances`)

### 系统数据（会保留）
- **用户数据** (`users`)
- **部门数据** (`departments`)
- **角色数据** (`roles`)
- **权限数据** (`permissions`)
- **角色权限关联** (`role_permissions`)
- **用户角色关联** (`user_roles`)
- **部门角色关联** (`department_roles`)

## 🚀 使用方法

### 快速开始（推荐）

```bash
# 0. 查看演示（安全，不会删除数据）
npm run clear:demo

# 1. 测试环境和权限
npm run clear:test

# 2. 备份数据
npm run clear:backup

# 3. 清空数据（会自动备份）
npm run clear:data

# 4. 恢复数据（如需要）
npm run clear:restore <backup-file-name>
```

### 演示模式

```bash
# 安全演示（不会实际删除数据）
npm run clear:demo

# 实际删除演示（危险！会真的删除数据）
npm run clear:demo-real
```

### 方法一：SQL脚本（适合DBA）

```bash
# 连接到数据库并执行脚本
psql -d your_database_name -f scripts/clear-business-data.sql
```

### 方法二：Node.js脚本（基础版）

```bash
# 确保已安装依赖
npm install

# 设置数据库连接
export DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# 执行清空脚本
npm run clear:simple
# 或者直接运行
node scripts/clear-business-data.js
```

### 方法三：Node.js脚本（带备份，推荐）

```bash
# 1. 仅备份数据
npm run clear:backup
# 或者：node scripts/clear-business-data-with-backup.js backup

# 2. 清空数据（会自动备份）
npm run clear:data
# 或者：node scripts/clear-business-data-with-backup.js clear

# 3. 从备份恢复数据
npm run clear:restore business-data-backup-2025-01-22T10-30-00.json
# 或者：node scripts/clear-business-data-with-backup.js restore business-data-backup-2025-01-22T10-30-00.json
```

## ⚠️ 重要注意事项

### 安全提醒
1. **不可逆操作**：数据清空后无法直接恢复，请务必备份
2. **生产环境**：在生产环境使用前请先在测试环境验证
3. **权限检查**：确保数据库用户有足够的权限执行删除操作
4. **业务影响**：清空数据会影响正在运行的业务功能

### 执行前检查清单
- [ ] 已备份重要数据
- [ ] 已在测试环境验证脚本
- [ ] 已通知相关业务人员
- [ ] 已确认数据库连接信息
- [ ] 已停止相关业务服务（可选）

### 备份建议
1. **数据库级备份**：
   ```bash
   pg_dump -h localhost -U username -d database_name > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **使用脚本备份**：
   ```bash
   node scripts/clear-business-data-with-backup.js backup
   ```

## 📊 执行结果示例

### 清空前数据统计
```
📊 当前数据统计：
业务数据：
   品牌：5 条
   项目：23 条
   项目收入：67 条
   周预算：145 条
   供应商：12 条
   附件：89 条
   变更日志：234 条
   审批实例：56 条

系统数据（将保留）：
   用户：15 条
   部门：8 条
   角色：6 条
   权限：45 条
```

### 清空过程日志
```
🚀 开始清空业务数据...
✓ 已清空审批实例数据：56 条
✓ 已清空项目变更日志数据：234 条
✓ 已清空项目收入数据：67 条
✓ 已清空周预算数据：145 条
✓ 已清空附件数据：89 条
✓ 已清空项目数据：23 条
✓ 已清空品牌数据：5 条
✓ 已清空供应商数据：12 条

🎉 业务数据清空完成！
```

## 🔧 故障排除

### 常见问题

1. **权限不足**
   ```
   错误：permission denied for table xxx
   解决：确保数据库用户有DELETE权限
   ```

2. **外键约束错误**
   ```
   错误：foreign key constraint fails
   解决：脚本已按正确顺序删除，检查是否有自定义外键
   ```

3. **连接失败**
   ```
   错误：database connection failed
   解决：检查DATABASE_URL环境变量和网络连接
   ```

### 恢复数据
如果需要恢复数据，可以：
1. 使用备份脚本恢复：`node scripts/clear-business-data-with-backup.js restore <backup-file>`
2. 从数据库备份恢复：`psql -d database_name < backup.sql`

## 📝 开发说明

### 脚本维护
- 当添加新的业务表时，需要更新脚本中的删除逻辑
- 注意维护正确的删除顺序（先删除子表，再删除父表）
- 系统表的判断标准：与用户权限、系统配置相关的表

### 扩展功能
- 可以添加选择性清空功能（只清空特定类型的数据）
- 可以添加数据导出功能（导出为Excel等格式）
- 可以添加定时清空功能

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看脚本执行日志
3. 联系系统管理员或开发团队

---

**最后更新**：2025-01-22  
**版本**：1.0  
**维护者**：系统管理员
