// 测试通过服务层的 changedFields 记录
import { ProjectService } from '../dist/services/project.js';

async function testServiceChangedFields() {
  try {
    console.log('🧪 测试服务层的 changedFields 记录...');
    
    const projectService = new ProjectService();
    
    // 1. 测试创建项目
    console.log('\n📝 测试通过服务层创建项目...');
    
    const createRequest = {
      documentType: 'PROJECT_INITIATION',
      brandId: 'cmc2xx4u80000krg8mlwx4kf3', // 使用现有品牌ID
      projectName: `服务层测试项目-${Date.now()}`,
      period: {
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      },
      budget: {
        planningBudget: 100000,
        influencerBudget: 50000,
        adBudget: 30000,
        otherBudget: 20000
      },
      cost: {
        influencerCost: 45000,
        adCost: 25000,
        otherCost: 15000,
        estimatedInfluencerRebate: 5000
      },
      executorPM: 'test-user-001',
      contentMediaIds: ['test-user-002'],
      contractType: 'SINGLE',
      settlementRules: '测试结算规则',
      kpi: '测试KPI'
    };
    
    try {
      const createdProject = await projectService.createProject(
        createRequest, 
        'test-user-001',
        { ip: '127.0.0.1', userAgent: 'Test Script' }
      );
      
      console.log(`✅ 项目创建成功: ${createdProject.id}`);
      
      // 等待一下让变更记录写入
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 查询创建的变更记录
      const createChangeLogs = await projectService.getProjectChangeLogsByProjectId(createdProject.id);
      
      if (createChangeLogs.changeLogs.length > 0) {
        const createLog = createChangeLogs.changeLogs.find(log => log.changeType === 'CREATE');
        if (createLog) {
          console.log(`✅ 找到创建变更记录`);
          console.log(`   变更字段数量: ${createLog.changedFields.length}`);
          console.log(`   变更字段: ${createLog.changedFields.join(', ')}`);
          
          // 验证是否包含了主要字段且排除了系统字段
          const hasBusinessFields = createLog.changedFields.some(field => 
            ['projectName', 'brandId'].includes(field)
          );
          const hasSystemFields = createLog.changedFields.some(field => 
            ['id', 'createdAt', 'updatedAt'].includes(field)
          );
          
          if (hasBusinessFields && !hasSystemFields) {
            console.log('✅ 创建记录字段正确：包含业务字段，排除系统字段');
          } else {
            console.log('❌ 创建记录字段有误');
          }
        } else {
          console.log('❌ 没有找到 CREATE 类型的变更记录');
        }
      } else {
        console.log('❌ 没有找到任何变更记录');
      }
      
      // 2. 测试更新项目
      console.log('\n📝 测试通过服务层更新项目...');
      
      const updateRequest = {
        id: createdProject.id,
        projectName: `更新后的项目名称-${Date.now()}`,
        budget: {
          planningBudget: 150000,
          influencerBudget: 60000,
          adBudget: 30000, // 保持不变
          otherBudget: 20000 // 保持不变
        }
      };
      
      const updatedProject = await projectService.updateProject(
        updateRequest,
        'test-user-002',
        { ip: '127.0.0.1', userAgent: 'Test Script' }
      );
      
      console.log(`✅ 项目更新成功`);
      
      // 等待一下让变更记录写入
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 查询更新的变更记录
      const updateChangeLogs = await projectService.getProjectChangeLogsByProjectId(createdProject.id);
      const updateLog = updateChangeLogs.changeLogs.find(log => log.changeType === 'UPDATE');
      
      if (updateLog) {
        console.log(`✅ 找到更新变更记录`);
        console.log(`   变更字段数量: ${updateLog.changedFields.length}`);
        console.log(`   变更字段: ${updateLog.changedFields.join(', ')}`);
        
        // 验证是否只记录了实际变更的字段
        const expectedFields = ['projectName', 'planningBudget', 'influencerBudget'];
        const unexpectedFields = ['adBudget', 'otherBudget', 'createdAt', 'updatedAt'];
        
        const hasExpectedFields = expectedFields.some(field => 
          updateLog.changedFields.includes(field)
        );
        const hasUnexpectedFields = unexpectedFields.some(field => 
          updateLog.changedFields.includes(field)
        );
        
        if (hasExpectedFields && !hasUnexpectedFields) {
          console.log('✅ 更新记录字段正确：只记录实际变更的字段');
        } else {
          console.log('❌ 更新记录字段有误');
          console.log(`   预期包含: ${expectedFields.join(', ')}`);
          console.log(`   不应包含: ${unexpectedFields.join(', ')}`);
        }
      } else {
        console.log('❌ 没有找到 UPDATE 类型的变更记录');
      }
      
      // 3. 清理测试数据
      console.log('\n🧹 清理测试数据...');
      await projectService.deleteProject(
        createdProject.id,
        'test-user-001',
        { ip: '127.0.0.1', userAgent: 'Test Script' }
      );
      console.log('✅ 测试数据清理完成');
      
    } catch (error) {
      console.error('❌ 服务层测试失败:', error.message);
    }
    
    console.log('\n🎉 服务层 changedFields 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testServiceChangedFields();
