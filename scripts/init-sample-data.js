#!/usr/bin/env node

/**
 * 初始化示例数据脚本
 * 用于在数据库中创建示例品牌和项目数据，确保财务统计接口有数据可显示
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 示例品牌数据
const sampleBrands = [
  {
    id: 'brand-001',
    name: '可口可乐',
    description: '全球知名饮料品牌',
    status: 'ACTIVE',
    createdBy: 'admin'
  },
  {
    id: 'brand-002', 
    name: '耐克',
    description: '运动品牌',
    status: 'ACTIVE',
    createdBy: 'admin'
  },
  {
    id: 'brand-003',
    name: '苹果',
    description: '科技品牌', 
    status: 'ACTIVE',
    createdBy: 'admin'
  }
];

// 示例项目数据
const sampleProjects = [
  {
    id: 'project-001',
    documentType: 'PROJECT_INITIATION',
    brandId: 'brand-001',
    projectName: '可口可乐春节营销活动',
    startDate: new Date('2024-02-01'),
    endDate: new Date('2024-02-29'),
    planningBudget: 1000000,
    influencerBudget: 400000,
    adBudget: 300000,
    otherBudget: 100000,
    influencerCost: 350000,
    adCost: 280000,
    otherCost: 80000,
    estimatedInfluencerRebate: 20000,
    executorPM: '6157664557692733', // 使用实际的钉钉用户ID
    contentMediaIds: ['285500612326255579', '461201641036347022'],
    contractType: 'ANNUAL_FRAME',
    settlementRules: '<p>按月结算，每月25日前提交结算单</p>',
    kpi: '<p>目标曝光量：1000万<br>目标转化率：3%</p>',
    status: 'COMPLETED', // 已完成项目
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'project-002',
    documentType: 'PROJECT_INITIATION',
    brandId: 'brand-002',
    projectName: '耐克夏季运动推广',
    startDate: new Date('2024-06-01'),
    endDate: new Date('2024-08-31'),
    planningBudget: 800000,
    influencerBudget: 320000,
    adBudget: 240000,
    otherBudget: 80000,
    influencerCost: 300000,
    adCost: 220000,
    otherCost: 70000,
    estimatedInfluencerRebate: 15000,
    executorPM: '025210045032003179',
    contentMediaIds: ['0108465227956083', '6157664557692733'],
    contractType: 'QUARTERLY_FRAME',
    settlementRules: '<p>按季度结算，每季度末结算</p>',
    kpi: '<p>目标销售额：500万<br>目标ROI：2.5</p>',
    status: 'ACTIVE', // 进行中项目
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'project-003',
    documentType: 'PROJECT_INITIATION',
    brandId: 'brand-003',
    projectName: '苹果新品发布会',
    startDate: new Date('2024-09-01'),
    endDate: new Date('2024-09-30'),
    planningBudget: 1200000,
    influencerBudget: 480000,
    adBudget: 360000,
    otherBudget: 120000,
    influencerCost: 450000,
    adCost: 340000,
    otherCost: 110000,
    estimatedInfluencerRebate: 25000,
    executorPM: '285500612326255579',
    contentMediaIds: ['6157664557692733', '025210045032003179'],
    contractType: 'SINGLE',
    settlementRules: '<p>项目完成后一次性结算</p>',
    kpi: '<p>目标观看量：2000万<br>目标互动率：5%</p>',
    status: 'ACTIVE', // 进行中项目
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'project-004',
    documentType: 'PROJECT_INITIATION',
    brandId: 'brand-001',
    projectName: '可口可乐夏日清爽活动',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    planningBudget: 600000,
    influencerBudget: 240000,
    adBudget: 180000,
    otherBudget: 60000,
    influencerCost: 220000,
    adCost: 170000,
    otherCost: 55000,
    estimatedInfluencerRebate: 12000,
    executorPM: '0108465227956083',
    contentMediaIds: ['6157664557692733', '285500612326255579'],
    contractType: 'SINGLE',
    settlementRules: '<p>项目完成后结算</p>',
    kpi: '<p>目标销量：100万瓶<br>目标覆盖：500万人</p>',
    status: 'COMPLETED', // 已完成项目
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'project-005',
    documentType: 'PROJECT_INITIATION',
    brandId: 'brand-002',
    projectName: '耐克秋季新品上市',
    startDate: new Date('2024-10-01'),
    endDate: new Date('2024-11-30'),
    planningBudget: 900000,
    influencerBudget: 360000,
    adBudget: 270000,
    otherBudget: 90000,
    influencerCost: 340000,
    adCost: 250000,
    otherCost: 85000,
    estimatedInfluencerRebate: 18000,
    executorPM: '461201641036347022',
    contentMediaIds: ['025210045032003179', '0108465227956083'],
    contractType: 'QUARTERLY_FRAME',
    settlementRules: '<p>按月结算，每月底结算</p>',
    kpi: '<p>目标新品销量：200万<br>目标市场份额：15%</p>',
    status: 'DRAFT', // 草稿项目
    createdBy: 'admin',
    updatedBy: 'admin'
  }
];

async function initSampleData() {
  try {
    console.log('🚀 开始初始化示例数据...');

    // 清理现有数据（可选）
    console.log('🧹 清理现有数据...');
    await prisma.project.deleteMany({});
    await prisma.brand.deleteMany({});

    // 创建品牌数据
    console.log('🏷️ 创建品牌数据...');
    for (const brand of sampleBrands) {
      await prisma.brand.create({
        data: brand
      });
      console.log(`✅ 创建品牌: ${brand.name}`);
    }

    // 创建项目数据
    console.log('📊 创建项目数据...');
    for (const project of sampleProjects) {
      await prisma.project.create({
        data: project
      });
      console.log(`✅ 创建项目: ${project.projectName} (状态: ${project.status})`);
    }

    console.log('🎉 示例数据初始化完成！');
    console.log(`📈 创建了 ${sampleBrands.length} 个品牌和 ${sampleProjects.length} 个项目`);
    
    // 显示统计信息
    const stats = await getStats();
    console.log('\n📊 数据统计:');
    console.log(`- 总项目数: ${stats.totalProjects}`);
    console.log(`- 进行中项目: ${stats.activeProjects}`);
    console.log(`- 已完成项目: ${stats.completedProjects}`);
    console.log(`- 草稿项目: ${stats.draftProjects}`);
    console.log(`- 总预算: ¥${stats.totalBudget.toLocaleString()}`);

  } catch (error) {
    console.error('❌ 初始化示例数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function getStats() {
  const [totalProjects, activeProjects, completedProjects, draftProjects, budgetSum] = await Promise.all([
    prisma.project.count(),
    prisma.project.count({ where: { status: 'ACTIVE' } }),
    prisma.project.count({ where: { status: 'COMPLETED' } }),
    prisma.project.count({ where: { status: 'DRAFT' } }),
    prisma.project.aggregate({
      _sum: {
        planningBudget: true,
      },
    }),
  ]);

  return {
    totalProjects,
    activeProjects,
    completedProjects,
    draftProjects,
    totalBudget: Number(budgetSum._sum.planningBudget || 0)
  };
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  initSampleData()
    .then(() => {
      console.log('✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

export { initSampleData };

