import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDatabase() {
  try {
    console.log('🔍 测试数据库连接...');
    
    // 测试连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 查询现有数据
    const brandCount = await prisma.brand.count();
    const projectCount = await prisma.project.count();
    
    console.log(`📊 当前数据统计:`);
    console.log(`- 品牌数量: ${brandCount}`);
    console.log(`- 项目数量: ${projectCount}`);
    
    // 创建一个测试品牌
    console.log('🧪 创建测试品牌...');
    const testBrand = await prisma.brand.create({
      data: {
        id: 'test-brand-001',
        name: '测试品牌',
        description: '这是一个测试品牌',
        status: 'ACTIVE',
        createdBy: 'test'
      }
    });
    console.log('✅ 测试品牌创建成功:', testBrand.name);
    
    // 创建一个测试项目
    console.log('🧪 创建测试项目...');
    const testProject = await prisma.project.create({
      data: {
        id: 'test-project-001',
        documentType: 'PROJECT_INITIATION',
        brandId: 'test-brand-001',
        projectName: '测试项目',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        planningBudget: 500000,
        influencerBudget: 200000,
        adBudget: 150000,
        otherBudget: 50000,
        influencerCost: 180000,
        adCost: 140000,
        otherCost: 45000,
        estimatedInfluencerRebate: 10000,
        executorPM: '6157664557692733',
        contentMediaIds: ['285500612326255579'],
        contractType: 'SINGLE',
        settlementRules: '<p>测试结算规则</p>',
        kpi: '<p>测试KPI</p>',
        status: 'ACTIVE',
        createdBy: 'test',
        updatedBy: 'test'
      }
    });
    console.log('✅ 测试项目创建成功:', testProject.projectName);
    
    // 再次查询数据
    const newBrandCount = await prisma.brand.count();
    const newProjectCount = await prisma.project.count();
    
    console.log(`📊 更新后数据统计:`);
    console.log(`- 品牌数量: ${newBrandCount}`);
    console.log(`- 项目数量: ${newProjectCount}`);
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

testDatabase()
  .then(() => {
    console.log('✅ 数据库测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 数据库测试失败:', error);
    process.exit(1);
  });
