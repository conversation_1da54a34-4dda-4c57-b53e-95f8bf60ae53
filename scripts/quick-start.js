#!/usr/bin/env node

/**
 * 钉钉应用快速启动和测试脚本
 * 自动启动服务器并运行完整的测试套件
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 配置
const CONFIG = {
    serverPort: process.env.PORT || 3000,
    serverStartTimeout: 10000, // 10秒
    testTimeout: 30000, // 30秒
    projectRoot: path.resolve(__dirname, '..')
};

// 检查必要文件
function checkRequiredFiles() {
    log('🔍 检查项目文件...', 'yellow');
    
    const requiredFiles = [
        'package.json',
        'src/app.ts',
        '.env.example'
    ];
    
    const missingFiles = [];
    
    for (const file of requiredFiles) {
        const filePath = path.join(CONFIG.projectRoot, file);
        if (!fs.existsSync(filePath)) {
            missingFiles.push(file);
        }
    }
    
    if (missingFiles.length > 0) {
        log('❌ 缺少必要文件:', 'red');
        missingFiles.forEach(file => log(`   - ${file}`, 'red'));
        return false;
    }
    
    log('✅ 项目文件检查通过', 'green');
    return true;
}

// 检查环境配置
function checkEnvironment() {
    log('🔍 检查环境配置...', 'yellow');
    
    const envPath = path.join(CONFIG.projectRoot, '.env');
    const envExamplePath = path.join(CONFIG.projectRoot, '.env.example');
    
    if (!fs.existsSync(envPath)) {
        if (fs.existsSync(envExamplePath)) {
            log('⚠️  .env 文件不存在，请复制 .env.example 并配置', 'yellow');
            log('   cp .env.example .env', 'cyan');
        } else {
            log('❌ 环境配置文件不存在', 'red');
        }
        return false;
    }
    
    // 检查关键环境变量
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = ['DINGTALK_APP_KEY', 'DINGTALK_APP_SECRET', 'DINGTALK_AGENT_ID'];
    const missingVars = [];
    
    for (const varName of requiredVars) {
        if (!envContent.includes(varName) || envContent.includes(`${varName}=`)) {
            missingVars.push(varName);
        }
    }
    
    if (missingVars.length > 0) {
        log('⚠️  以下环境变量需要配置:', 'yellow');
        missingVars.forEach(varName => log(`   - ${varName}`, 'yellow'));
        log('   请在 .env 文件中设置这些变量', 'cyan');
        return false;
    }
    
    log('✅ 环境配置检查通过', 'green');
    return true;
}

// 安装依赖
function installDependencies() {
    return new Promise((resolve, reject) => {
        log('📦 检查并安装依赖...', 'yellow');
        
        const nodeModulesPath = path.join(CONFIG.projectRoot, 'node_modules');
        if (fs.existsSync(nodeModulesPath)) {
            log('✅ 依赖已安装', 'green');
            resolve();
            return;
        }
        
        log('📥 正在安装依赖...', 'blue');
        const npm = spawn('npm', ['install'], {
            cwd: CONFIG.projectRoot,
            stdio: 'pipe'
        });
        
        npm.stdout.on('data', (data) => {
            process.stdout.write(data);
        });
        
        npm.stderr.on('data', (data) => {
            process.stderr.write(data);
        });
        
        npm.on('close', (code) => {
            if (code === 0) {
                log('✅ 依赖安装完成', 'green');
                resolve();
            } else {
                log('❌ 依赖安装失败', 'red');
                reject(new Error(`npm install failed with code ${code}`));
            }
        });
    });
}

// 启动服务器
function startServer() {
    return new Promise((resolve, reject) => {
        log('🚀 启动开发服务器...', 'yellow');
        
        const server = spawn('npm', ['run', 'dev'], {
            cwd: CONFIG.projectRoot,
            stdio: 'pipe'
        });
        
        let serverReady = false;
        const timeout = setTimeout(() => {
            if (!serverReady) {
                server.kill();
                reject(new Error('服务器启动超时'));
            }
        }, CONFIG.serverStartTimeout);
        
        server.stdout.on('data', (data) => {
            const output = data.toString();
            process.stdout.write(output);
            
            // 检查服务器是否启动成功
            if (output.includes('Server running') || output.includes(`localhost:${CONFIG.serverPort}`)) {
                serverReady = true;
                clearTimeout(timeout);
                log('✅ 服务器启动成功', 'green');
                resolve(server);
            }
        });
        
        server.stderr.on('data', (data) => {
            process.stderr.write(data);
        });
        
        server.on('close', (code) => {
            if (!serverReady) {
                reject(new Error(`服务器启动失败，退出码: ${code}`));
            }
        });
    });
}

// 等待服务器就绪
function waitForServer() {
    return new Promise((resolve) => {
        log('⏳ 等待服务器就绪...', 'blue');
        setTimeout(() => {
            log('✅ 服务器就绪', 'green');
            resolve();
        }, 3000);
    });
}

// 运行API测试
function runApiTests() {
    return new Promise((resolve, reject) => {
        log('🧪 运行API测试...', 'yellow');
        
        const testScript = path.join(CONFIG.projectRoot, 'scripts', 'test-api-endpoints.js');
        const test = spawn('node', [testScript], {
            cwd: CONFIG.projectRoot,
            stdio: 'pipe',
            env: {
                ...process.env,
                API_BASE_URL: `http://localhost:${CONFIG.serverPort}`
            }
        });
        
        test.stdout.on('data', (data) => {
            process.stdout.write(data);
        });
        
        test.stderr.on('data', (data) => {
            process.stderr.write(data);
        });
        
        test.on('close', (code) => {
            if (code === 0) {
                log('✅ API测试通过', 'green');
                resolve();
            } else {
                log('❌ API测试失败', 'red');
                reject(new Error(`API测试失败，退出码: ${code}`));
            }
        });
    });
}

// 显示访问信息
function showAccessInfo() {
    log('', 'reset');
    log('🎉 系统启动完成！', 'green');
    log('', 'reset');
    log('📱 访问地址:', 'cyan');
    log(`   🏠 首页: http://localhost:${CONFIG.serverPort}/index.html`, 'blue');
    log(`   🎨 完整功能: http://localhost:${CONFIG.serverPort}/dingtalk-design-demo.html`, 'blue');
    log(`   🔐 免登录演示: http://localhost:${CONFIG.serverPort}/dingtalk-login-demo.html`, 'blue');
    log(`   📱 官方演示: http://localhost:${CONFIG.serverPort}/official-login-demo.html`, 'blue');
    log(`   🧪 API测试: http://localhost:${CONFIG.serverPort}/test-api.html`, 'blue');
    log(`   🔑 签名测试: http://localhost:${CONFIG.serverPort}/signature-test.html`, 'blue');
    log('', 'reset');
    log('🔧 API接口:', 'cyan');
    log(`   📊 健康检查: http://localhost:${CONFIG.serverPort}/api/health`, 'blue');
    log(`   ⚙️  应用配置: http://localhost:${CONFIG.serverPort}/api/app/config`, 'blue');
    log('', 'reset');
    log('💡 提示:', 'yellow');
    log('   - 在钉钉客户端中访问以体验完整功能', 'yellow');
    log('   - 使用官方演示页面学习标准流程', 'yellow');
    log('   - 遇到问题时先运行API状态检查', 'yellow');
    log('', 'reset');
    log('按 Ctrl+C 停止服务器', 'magenta');
}

// 主函数
async function main() {
    log('🔧 钉钉应用快速启动工具', 'magenta');
    log('================================', 'magenta');
    log('', 'reset');
    
    try {
        // 1. 检查项目文件
        if (!checkRequiredFiles()) {
            process.exit(1);
        }
        
        // 2. 检查环境配置
        if (!checkEnvironment()) {
            log('', 'reset');
            log('💡 配置完成后重新运行此脚本', 'cyan');
            process.exit(1);
        }
        
        // 3. 安装依赖
        await installDependencies();
        
        // 4. 启动服务器
        const server = await startServer();
        
        // 5. 等待服务器就绪
        await waitForServer();
        
        // 6. 运行API测试
        try {
            await runApiTests();
        } catch (error) {
            log('⚠️  API测试失败，但服务器仍在运行', 'yellow');
        }
        
        // 7. 显示访问信息
        showAccessInfo();
        
        // 处理退出信号
        process.on('SIGINT', () => {
            log('', 'reset');
            log('🛑 正在停止服务器...', 'yellow');
            server.kill();
            process.exit(0);
        });
        
    } catch (error) {
        log(`💥 启动失败: ${error.message}`, 'red');
        process.exit(1);
    }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🔧 钉钉应用快速启动工具

功能:
  - 自动检查项目文件和环境配置
  - 安装必要的依赖包
  - 启动开发服务器
  - 运行API接口测试
  - 显示访问地址和使用提示

用法:
  node scripts/quick-start.js [选项]

选项:
  --help, -h     显示帮助信息

环境变量:
  PORT           服务器端口 (默认: 3000)

示例:
  node scripts/quick-start.js
  PORT=8080 node scripts/quick-start.js
`);
    process.exit(0);
}

// 运行主函数
main().catch(error => {
    log(`💥 运行失败: ${error.message}`, 'red');
    process.exit(1);
});
