/**
 * 测试脚本：验证预算重构功能
 * 
 * 这个脚本将测试：
 * 1. 数据库连接
 * 2. 预算CRUD操作
 * 3. 资金计划CRUD操作
 * 4. 审批流程
 * 5. 数据完整性
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🧪 开始测试预算重构功能...\n');

  try {
    // 1. 测试数据库连接
    console.log('1️⃣ 测试数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功\n');

    // 2. 测试预算功能
    console.log('2️⃣ 测试预算功能...');
    await testBudgetOperations();

    // 3. 测试资金计划功能
    console.log('3️⃣ 测试资金计划功能...');
    await testFundingPlanOperations();

    // 4. 测试审批流程
    console.log('4️⃣ 测试审批流程...');
    await testApprovalProcess();

    // 5. 测试数据完整性
    console.log('5️⃣ 测试数据完整性...');
    await testDataIntegrity();

    console.log('🎉 所有测试通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function testBudgetOperations() {
  console.log('  📝 测试预算CRUD操作...');

  // 首先确保有一个测试项目
  let testProject = await prisma.project.findFirst({
    where: { projectName: { contains: '测试项目' } }
  });

  if (!testProject) {
    console.log('  📋 创建测试项目...');

    // 先确保有一个测试品牌
    let testBrand = await prisma.brand.findFirst({
      where: { name: { contains: '测试品牌' } }
    });

    if (!testBrand) {
      testBrand = await prisma.brand.create({
        data: {
          name: '测试品牌-预算重构',
          description: '用于测试预算重构功能的品牌',
          status: 'ACTIVE',
          createdBy: 'test-user'
        }
      });
    }

    testProject = await prisma.project.create({
      data: {
        projectName: '测试项目-预算重构',
        documentType: 'PROJECT_INITIATION',
        brandId: testBrand.id,
        planningBudget: 100000,
        influencerBudget: 50000,
        adBudget: 30000,
        otherBudget: 20000,
        influencerCost: 45000,
        adCost: 25000,
        otherCost: 15000,
        estimatedInfluencerRebate: 5000,
        executorPM: 'test-pm',
        contractType: 'SINGLE',
        settlementRules: '测试结算规则',
        kpi: '测试KPI',
        status: 'ACTIVE',
        contractSigningStatus: 'PENDING',
        intermediaryCost: 0,
        createdBy: 'test-user',
        updatedBy: 'test-user'
      }
    });
    console.log(`  ✅ 测试项目创建成功: ${testProject.id}`);
  }

  // 创建预算
  console.log('  💰 创建测试预算...');
  const testBudget = await prisma.budget.create({
    data: {
      title: '测试预算-重构验证',
      serviceType: 'INFLUENCER',
      serviceContent: '测试服务内容',
      remarks: '这是一个测试预算',
      contractAmount: 10000,
      taxRate: 'GENERAL',
      totalPaidAmount: 0,
      remainingAmount: 10000,
      status: 'CREATED',
      projectId: testProject.id,
      createdBy: 'test-user',
      updatedBy: 'test-user'
    }
  });
  console.log(`  ✅ 预算创建成功: ${testBudget.id}`);

  // 查询预算
  console.log('  🔍 查询预算...');
  const foundBudget = await prisma.budget.findUnique({
    where: { id: testBudget.id },
    include: {
      project: true,
      fundingPlans: true
    }
  });
  console.log(`  ✅ 预算查询成功: ${foundBudget?.title}`);

  // 更新预算
  console.log('  ✏️ 更新预算...');
  const updatedBudget = await prisma.budget.update({
    where: { id: testBudget.id },
    data: {
      contractAmount: 12000,
      remainingAmount: 12000,
      updatedBy: 'test-user-updated'
    }
  });
  console.log(`  ✅ 预算更新成功: 合同金额 ${updatedBudget.contractAmount}`);

  console.log('  ✅ 预算CRUD操作测试通过\n');
  return testBudget;
}

async function testFundingPlanOperations() {
  console.log('  📅 测试资金计划CRUD操作...');

  // 获取测试预算
  const testBudget = await prisma.budget.findFirst({
    where: { title: { contains: '测试预算-重构验证' } }
  });

  if (!testBudget) {
    throw new Error('找不到测试预算');
  }

  // 创建资金计划
  console.log('  💸 创建测试资金计划...');
  const testFundingPlan = await prisma.fundingPlan.create({
    data: {
      title: '测试资金计划-2024年1月第1周',
      year: 2024,
      month: 1,
      weekOfMonth: 1,
      plannedAmount: 5000,
      paidAmount: 0,
      remainingAmount: 5000,
      status: 'DRAFT',
      approvalStatus: 'NONE',
      remarks: '这是一个测试资金计划',
      budgetId: testBudget.id,
      createdBy: 'test-user',
      updatedBy: 'test-user'
    }
  });
  console.log(`  ✅ 资金计划创建成功: ${testFundingPlan.id}`);

  // 查询资金计划
  console.log('  🔍 查询资金计划...');
  const foundFundingPlan = await prisma.fundingPlan.findUnique({
    where: { id: testFundingPlan.id },
    include: {
      budget: {
        include: {
          project: true
        }
      }
    }
  });
  console.log(`  ✅ 资金计划查询成功: ${foundFundingPlan?.title}`);

  // 更新资金计划
  console.log('  ✏️ 更新资金计划...');
  const updatedFundingPlan = await prisma.fundingPlan.update({
    where: { id: testFundingPlan.id },
    data: {
      status: 'SUBMITTED',
      approvalStatus: 'PENDING',
      updatedBy: 'test-user-updated'
    }
  });
  console.log(`  ✅ 资金计划更新成功: 状态 ${updatedFundingPlan.status}`);

  console.log('  ✅ 资金计划CRUD操作测试通过\n');
  return testFundingPlan;
}

async function testApprovalProcess() {
  console.log('  📋 测试审批流程...');

  // 获取测试资金计划
  const testFundingPlan = await prisma.fundingPlan.findFirst({
    where: { title: { contains: '测试资金计划' } }
  });

  if (!testFundingPlan) {
    throw new Error('找不到测试资金计划');
  }

  // 创建审批实例
  console.log('  📝 创建审批实例...');
  const testApproval = await prisma.approvalInstance.create({
    data: {
      processInstanceId: `test-process-${Date.now()}`,
      processCode: 'TEST_APPROVAL',
      title: '测试审批-资金计划',
      originatorUserId: 'test-user',
      status: 'PENDING',
      createTime: new Date(),
      approvalAmount: 5000,
      reason: '测试审批流程',
      fundingPlanId: testFundingPlan.id
    }
  });
  console.log(`  ✅ 审批实例创建成功: ${testApproval.id}`);

  // 模拟审批通过
  console.log('  ✅ 模拟审批通过...');
  const approvedInstance = await prisma.approvalInstance.update({
    where: { id: testApproval.id },
    data: {
      status: 'APPROVED',
      result: '审批通过',
      finishTime: new Date(),
      actualAmount: 5000
    }
  });
  console.log(`  ✅ 审批通过: ${approvedInstance.status}`);

  // 更新资金计划的已付金额（模拟回写逻辑）
  console.log('  💰 更新资金计划已付金额...');
  const updatedPlan = await prisma.fundingPlan.update({
    where: { id: testFundingPlan.id },
    data: {
      paidAmount: 5000,
      remainingAmount: 0,
      status: 'COMPLETED'
    }
  });
  console.log(`  ✅ 资金计划已付金额更新: ${updatedPlan.paidAmount}`);

  console.log('  ✅ 审批流程测试通过\n');
}

async function testDataIntegrity() {
  console.log('  🔒 测试数据完整性...');

  // 检查预算和资金计划的关联关系
  console.log('  🔗 检查预算和资金计划关联关系...');
  const budgetWithPlans = await prisma.budget.findFirst({
    where: { title: { contains: '测试预算-重构验证' } },
    include: {
      fundingPlans: true,
      project: true
    }
  });

  if (!budgetWithPlans) {
    throw new Error('找不到测试预算');
  }

  console.log(`  📊 预算信息:`);
  console.log(`    - 标题: ${budgetWithPlans.title}`);
  console.log(`    - 合同金额: ${budgetWithPlans.contractAmount}`);
  console.log(`    - 总已付金额: ${budgetWithPlans.totalPaidAmount}`);
  console.log(`    - 剩余金额: ${budgetWithPlans.remainingAmount}`);
  console.log(`    - 关联资金计划数量: ${budgetWithPlans.fundingPlans.length}`);

  // 检查资金计划和审批的关联关系
  console.log('  🔗 检查资金计划和审批关联关系...');
  const planWithApprovals = await prisma.fundingPlan.findFirst({
    where: { title: { contains: '测试资金计划' } },
    include: {
      approvalInstances: true,
      budget: true
    }
  });

  if (planWithApprovals) {
    console.log(`  📋 资金计划信息:`);
    console.log(`    - 标题: ${planWithApprovals.title}`);
    console.log(`    - 计划金额: ${planWithApprovals.plannedAmount}`);
    console.log(`    - 已付金额: ${planWithApprovals.paidAmount}`);
    console.log(`    - 剩余金额: ${planWithApprovals.remainingAmount}`);
    console.log(`    - 关联审批数量: ${planWithApprovals.approvalInstances.length}`);
  }

  console.log('  ✅ 数据完整性检查通过\n');
}

// 运行测试
main()
  .then(() => {
    console.log('✅ 所有测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
