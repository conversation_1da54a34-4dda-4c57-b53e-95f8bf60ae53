#!/usr/bin/env node

/**
 * 数据库设置脚本
 * 自动设置PostgreSQL数据库和Prisma
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 执行命令
function runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
        const child = spawn(command, args, {
            stdio: 'pipe',
            ...options
        });

        let stdout = '';
        let stderr = '';

        child.stdout.on('data', (data) => {
            stdout += data.toString();
            if (options.showOutput) {
                process.stdout.write(data);
            }
        });

        child.stderr.on('data', (data) => {
            stderr += data.toString();
            if (options.showOutput) {
                process.stderr.write(data);
            }
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve({ stdout, stderr });
            } else {
                reject(new Error(`Command failed with code ${code}: ${stderr}`));
            }
        });
    });
}

// 检查PostgreSQL是否安装
async function checkPostgreSQL() {
    log('🔍 检查PostgreSQL...', 'yellow');
    
    try {
        await runCommand('psql', ['--version']);
        log('✅ PostgreSQL已安装', 'green');
        return true;
    } catch (error) {
        log('❌ PostgreSQL未安装或不在PATH中', 'red');
        log('   请安装PostgreSQL: https://www.postgresql.org/download/', 'cyan');
        return false;
    }
}

// 检查Docker是否可用
async function checkDocker() {
    log('🔍 检查Docker...', 'yellow');
    
    try {
        await runCommand('docker', ['--version']);
        log('✅ Docker已安装', 'green');
        return true;
    } catch (error) {
        log('⚠️  Docker未安装，将跳过Docker设置', 'yellow');
        return false;
    }
}

// 使用Docker启动PostgreSQL
async function startPostgreSQLWithDocker() {
    log('🐳 使用Docker启动PostgreSQL...', 'yellow');
    
    try {
        // 检查容器是否已存在
        try {
            const { stdout } = await runCommand('docker', ['ps', '-a', '--filter', 'name=postgres-project-mgmt', '--format', '{{.Names}}']);
            if (stdout.trim() === 'postgres-project-mgmt') {
                log('   容器已存在，正在启动...', 'blue');
                await runCommand('docker', ['start', 'postgres-project-mgmt']);
                log('✅ PostgreSQL容器已启动', 'green');
                return true;
            }
        } catch (error) {
            // 容器不存在，继续创建
        }
        
        // 创建新容器
        log('   创建新的PostgreSQL容器...', 'blue');
        await runCommand('docker', [
            'run', '--name', 'postgres-project-mgmt',
            '-e', 'POSTGRES_PASSWORD=password',
            '-e', 'POSTGRES_DB=project_management',
            '-e', 'POSTGRES_USER=postgres',
            '-p', '5432:5432',
            '-d', 'postgres:15'
        ]);
        
        log('✅ PostgreSQL容器创建并启动成功', 'green');
        log('   数据库信息:', 'cyan');
        log('     主机: localhost', 'cyan');
        log('     端口: 5432', 'cyan');
        log('     数据库: project_management', 'cyan');
        log('     用户: postgres', 'cyan');
        log('     密码: password', 'cyan');
        
        return true;
    } catch (error) {
        log(`❌ Docker启动PostgreSQL失败: ${error.message}`, 'red');
        return false;
    }
}

// 检查环境变量
function checkEnvironment() {
    log('🔍 检查环境配置...', 'yellow');
    
    const envPath = path.join(process.cwd(), '.env');
    
    if (!fs.existsSync(envPath)) {
        log('⚠️  .env文件不存在，正在创建...', 'yellow');
        
        const envExamplePath = path.join(process.cwd(), '.env.example');
        if (fs.existsSync(envExamplePath)) {
            fs.copyFileSync(envExamplePath, envPath);
            log('✅ 已从.env.example创建.env文件', 'green');
        } else {
            // 创建基本的.env文件
            const envContent = `# 数据库配置
DATABASE_URL="postgresql://postgres:password@localhost:5432/project_management?schema=public"

# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key_here
DINGTALK_APP_SECRET=your_app_secret_here
DINGTALK_CORP_ID=your_corp_id_here
DINGTALK_AGENT_ID=your_agent_id_here

# 服务器配置
PORT=3000
NODE_ENV=development
`;
            fs.writeFileSync(envPath, envContent);
            log('✅ 已创建基本的.env文件', 'green');
        }
    }
    
    // 检查DATABASE_URL
    const envContent = fs.readFileSync(envPath, 'utf8');
    if (!envContent.includes('DATABASE_URL=') || envContent.includes('DATABASE_URL=""')) {
        log('⚠️  DATABASE_URL未配置，请编辑.env文件', 'yellow');
        log('   示例: DATABASE_URL="postgresql://postgres:password@localhost:5432/project_management?schema=public"', 'cyan');
        return false;
    }
    
    log('✅ 环境配置检查通过', 'green');
    return true;
}

// 安装Prisma依赖
async function installPrismaDependencies() {
    log('📦 安装Prisma依赖...', 'yellow');
    
    try {
        // 检查是否已安装
        const packageJsonPath = path.join(process.cwd(), 'package.json');
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        const hasPrisma = packageJson.dependencies?.['@prisma/client'] || packageJson.devDependencies?.['prisma'];
        
        if (hasPrisma) {
            log('✅ Prisma依赖已安装', 'green');
            return true;
        }
        
        log('   正在安装Prisma...', 'blue');
        await runCommand('npm', ['install', 'prisma', '@prisma/client'], { showOutput: true });
        
        log('✅ Prisma依赖安装完成', 'green');
        return true;
    } catch (error) {
        log(`❌ Prisma依赖安装失败: ${error.message}`, 'red');
        return false;
    }
}

// 初始化Prisma
async function initializePrisma() {
    log('🔧 初始化Prisma...', 'yellow');
    
    try {
        const prismaDir = path.join(process.cwd(), 'prisma');
        
        if (!fs.existsSync(prismaDir)) {
            log('   正在初始化Prisma...', 'blue');
            await runCommand('npx', ['prisma', 'init'], { showOutput: true });
        } else {
            log('   Prisma已初始化', 'blue');
        }
        
        // 生成Prisma客户端
        log('   生成Prisma客户端...', 'blue');
        await runCommand('npx', ['prisma', 'generate'], { showOutput: true });
        
        log('✅ Prisma初始化完成', 'green');
        return true;
    } catch (error) {
        log(`❌ Prisma初始化失败: ${error.message}`, 'red');
        return false;
    }
}

// 运行数据库迁移
async function runMigrations() {
    log('🗄️  运行数据库迁移...', 'yellow');
    
    try {
        // 等待数据库启动
        log('   等待数据库启动...', 'blue');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 运行迁移
        log('   执行数据库迁移...', 'blue');
        await runCommand('npx', ['prisma', 'migrate', 'dev', '--name', 'init'], { showOutput: true });
        
        log('✅ 数据库迁移完成', 'green');
        return true;
    } catch (error) {
        log(`❌ 数据库迁移失败: ${error.message}`, 'red');
        log('   请检查数据库连接和配置', 'yellow');
        return false;
    }
}

// 测试数据库连接
async function testDatabaseConnection() {
    log('🔌 测试数据库连接...', 'yellow');
    
    try {
        await runCommand('npx', ['prisma', 'db', 'pull'], { showOutput: false });
        log('✅ 数据库连接成功', 'green');
        return true;
    } catch (error) {
        log(`❌ 数据库连接失败: ${error.message}`, 'red');
        return false;
    }
}

// 显示完成信息
function showCompletionInfo() {
    log('', 'reset');
    log('🎉 数据库设置完成！', 'green');
    log('', 'reset');
    log('📋 接下来的步骤:', 'cyan');
    log('', 'reset');
    log('1. 配置钉钉应用信息 (编辑 .env 文件):', 'yellow');
    log('   DINGTALK_APP_KEY=your_app_key', 'blue');
    log('   DINGTALK_APP_SECRET=your_app_secret', 'blue');
    log('   DINGTALK_CORP_ID=your_corp_id', 'blue');
    log('   DINGTALK_AGENT_ID=your_agent_id', 'blue');
    log('', 'reset');
    log('2. 启动应用:', 'yellow');
    log('   npm run dev', 'blue');
    log('', 'reset');
    log('3. 访问项目管理页面:', 'yellow');
    log('   http://localhost:3000/project-management.html', 'blue');
    log('', 'reset');
    log('4. 可选 - 迁移现有数据:', 'yellow');
    log('   npm run db:seed', 'blue');
    log('', 'reset');
    log('5. 可选 - 打开Prisma Studio:', 'yellow');
    log('   npm run db:studio', 'blue');
    log('', 'reset');
    log('💡 提示:', 'magenta');
    log('   - 数据库运行在 localhost:5432', 'cyan');
    log('   - 使用 docker stop postgres-project-mgmt 停止数据库', 'cyan');
    log('   - 使用 docker start postgres-project-mgmt 重新启动数据库', 'cyan');
}

// 主函数
async function main() {
    log('🚀 项目管理系统数据库设置', 'magenta');
    log('=====================================', 'magenta');
    log('', 'reset');
    
    try {
        // 1. 检查PostgreSQL
        const hasPostgreSQL = await checkPostgreSQL();
        
        // 2. 如果没有PostgreSQL，尝试使用Docker
        if (!hasPostgreSQL) {
            const hasDocker = await checkDocker();
            if (hasDocker) {
                const dockerSuccess = await startPostgreSQLWithDocker();
                if (!dockerSuccess) {
                    log('❌ 无法设置数据库，请手动安装PostgreSQL', 'red');
                    process.exit(1);
                }
            } else {
                log('❌ 请安装PostgreSQL或Docker', 'red');
                process.exit(1);
            }
        }
        
        // 3. 检查环境配置
        const envOk = checkEnvironment();
        if (!envOk) {
            log('⚠️  请配置.env文件后重新运行此脚本', 'yellow');
            process.exit(1);
        }
        
        // 4. 安装Prisma依赖
        const prismaInstalled = await installPrismaDependencies();
        if (!prismaInstalled) {
            process.exit(1);
        }
        
        // 5. 初始化Prisma
        const prismaInitialized = await initializePrisma();
        if (!prismaInitialized) {
            process.exit(1);
        }
        
        // 6. 测试数据库连接
        const connectionOk = await testDatabaseConnection();
        if (!connectionOk) {
            log('⚠️  数据库连接失败，请检查配置', 'yellow');
        }
        
        // 7. 运行迁移
        const migrationOk = await runMigrations();
        if (!migrationOk) {
            log('⚠️  数据库迁移失败，请手动运行: npx prisma migrate dev', 'yellow');
        }
        
        // 8. 显示完成信息
        showCompletionInfo();
        
    } catch (error) {
        log(`💥 设置失败: ${error.message}`, 'red');
        process.exit(1);
    }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🚀 数据库设置工具

功能:
  - 自动检查和安装PostgreSQL (或使用Docker)
  - 配置Prisma ORM
  - 运行数据库迁移
  - 创建示例数据

用法:
  node scripts/setup-database.js [选项]

选项:
  --help, -h     显示帮助信息

示例:
  node scripts/setup-database.js
`);
    process.exit(0);
}

// 运行设置
main().catch(error => {
    log(`💥 设置工具运行失败: ${error.message}`, 'red');
    process.exit(1);
});
