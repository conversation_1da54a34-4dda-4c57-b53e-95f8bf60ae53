#!/usr/bin/env node

/**
 * 测试钉钉审批回调功能
 */

import { DingTalkStreamSimple } from '../dist/services/dingtalk-stream-simple.js';

async function testApprovalCallback() {
  console.log('🧪 开始测试钉钉审批回调功能...');
  
  const streamService = new DingTalkStreamSimple();
  
  // 设置审批相关事件监听器
  streamService.on('approval_change', (data) => {
    console.log('📋 收到审批变更事件:');
    console.log('  - 原始数据:', JSON.stringify(data.originalData, null, 2));
    console.log('  - 处理结果:', JSON.stringify(data.processResult, null, 2));
  });
  
  streamService.on('approval_error', (data) => {
    console.error('❌ 审批处理错误:');
    console.error('  - 错误信息:', data.error.message);
    console.error('  - 原始数据:', data.originalData);
  });
  
  streamService.on('callback', (data) => {
    console.log('📞 收到回调事件:', {
      type: data.type,
      topic: data.headers?.topic,
      eventType: data.headers?.eventType,
      messageId: data.headers?.messageId
    });
  });
  
  streamService.on('error', (error) => {
    console.error('❌ Stream服务错误:', error);
  });
  
  try {
    // 启动服务
    console.log('🚀 启动Stream服务...');
    await streamService.start();
    console.log('✅ Stream服务启动成功');
    
    // 显示连接状态
    const status = streamService.getConnectionStatus();
    console.log('📊 连接状态:', status);
    
    // 测试手动处理审批数据
    console.log('\n🔧 测试手动处理审批数据...');
    try {
      const testApprovalData = {
        processInstanceId: 'test-process-123',
        result: 'agree',
        type: 'finish',
        staffId: 'test-staff-456',
        createTime: Date.now(),
        finishTime: Date.now(),
        corpId: 'test-corp-789'
      };
      
      const result = await streamService.processApprovalData(testApprovalData);
      console.log('✅ 手动处理审批数据成功:', result);
    } catch (error) {
      console.log('⚠️ 手动处理审批数据失败（可能是数据库连接问题）:', error.message);
    }
    
    // 保持连接一段时间用于测试
    console.log('\n⏰ 保持连接60秒用于测试实际审批回调...');
    console.log('💡 请在钉钉中发起审批流程来测试实际的回调处理');
    console.log('💡 当审批状态变更时，系统会自动：');
    console.log('   1. 接收钉钉推送的审批事件');
    console.log('   2. 解析审批数据');
    console.log('   3. 调用ApprovalService处理状态变更');
    console.log('   4. 更新数据库中的相关记录');
    console.log('   5. 触发approval_change事件');
    
    await new Promise(resolve => setTimeout(resolve, 60000));
    
    // 停止服务
    console.log('\n🛑 停止Stream服务...');
    await streamService.stop();
    console.log('✅ Stream服务已停止');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.message.includes('400')) {
      console.log('\n🔍 400错误可能的原因:');
      console.log('1. 钉钉应用配置错误');
      console.log('2. 应用没有审批事件订阅权限');
      console.log('3. 请求参数格式不正确');
    }
    
    process.exit(1);
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在停止测试...');
  process.exit(0);
});

// 运行测试
testApprovalCallback()
  .then(() => {
    console.log('✅ 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
