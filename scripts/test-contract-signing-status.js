#!/usr/bin/env node

/**
 * 测试合同签署状态功能
 */

import { ProjectService } from '../dist/services/project.js';
import { ContractSigningStatus } from '../dist/types/project.js';

async function testContractSigningStatus() {
  console.log('🧪 开始测试合同签署状态功能...');
  
  const projectService = new ProjectService();
  
  try {
    // 1. 测试获取所有项目并查看合同签署状态
    console.log('\n📝 步骤1: 测试获取所有项目的合同签署状态...');
    const allProjects = await projectService.getProjects();
    console.log(`✅ 获取到 ${allProjects.projects.length} 个项目`);
    
    if (allProjects.projects.length > 0) {
      console.log('📋 项目合同签署状态:');
      allProjects.projects.forEach(project => {
        console.log(`  - ${project.projectName}: ${project.contractSigningStatus}`);
      });
    }
    
    // 2. 测试按合同签署状态过滤项目
    console.log('\n📝 步骤2: 测试按合同签署状态过滤项目...');
    
    const statusesToTest = [
      ContractSigningStatus.NO_CONTRACT,
      ContractSigningStatus.SIGNED,
      ContractSigningStatus.SIGNING,
      ContractSigningStatus.PENDING
    ];
    
    for (const status of statusesToTest) {
      try {
        const filteredProjects = await projectService.getProjects({
          contractSigningStatus: status
        });
        console.log(`✅ 状态为 "${status}" 的项目: ${filteredProjects.projects.length} 个`);
        
        if (filteredProjects.projects.length > 0) {
          filteredProjects.projects.forEach(project => {
            console.log(`    - ${project.projectName}`);
          });
        }
      } catch (error) {
        console.error(`❌ 过滤状态 "${status}" 失败:`, error.message);
      }
    }
    
    // 3. 测试创建项目时设置合同签署状态
    console.log('\n📝 步骤3: 测试创建项目时设置合同签署状态...');
    
    const testProject = {
      documentType: 'project_initiation',
      brandId: 'cmc3hky270000kr9kugy66ho9', // 使用实际存在的品牌ID
      projectName: '测试合同签署状态项目',
      period: {
        startDate: '2024-01-01',
        endDate: '2024-03-31'
      },
      budget: {
        planningBudget: 100000,
        influencerBudget: 60000,
        adBudget: 30000,
        otherBudget: 10000
      },
      cost: {
        influencerCost: 50000,
        adCost: 25000,
        otherCost: 8000,
        estimatedInfluencerRebate: 5000
      },
      executorPM: 'user-001',
      contentMediaIds: ['user-002'],
      contractType: 'single',
      contractSigningStatus: ContractSigningStatus.SIGNING, // 设置为签订中
      settlementRules: '<p>测试结算规则</p>',
      kpi: '<p>测试KPI</p>'
    };
    
    try {
      const createdProject = await projectService.createProject(testProject, 'test-user');
      console.log(`✅ 创建项目成功: ${createdProject.projectName}`);
      console.log(`   合同签署状态: ${createdProject.contractSigningStatus}`);
      
      // 4. 测试更新项目的合同签署状态
      console.log('\n📝 步骤4: 测试更新项目的合同签署状态...');
      
      const updateData = {
        id: createdProject.id,
        contractSigningStatus: ContractSigningStatus.SIGNED // 更新为已签订
      };
      
      const updatedProject = await projectService.updateProject(updateData, 'test-user');
      console.log(`✅ 更新项目成功: ${updatedProject.projectName}`);
      console.log(`   新的合同签署状态: ${updatedProject.contractSigningStatus}`);
      
      // 5. 测试获取单个项目的合同签署状态
      console.log('\n📝 步骤5: 测试获取单个项目的合同签署状态...');
      
      const singleProject = await projectService.getProject(createdProject.id);
      if (singleProject) {
        console.log(`✅ 获取项目详情成功: ${singleProject.projectName}`);
        console.log(`   合同签署状态: ${singleProject.contractSigningStatus}`);
      } else {
        console.log('⚠️ 未找到项目');
      }
      
      // 6. 清理测试数据
      console.log('\n📝 步骤6: 清理测试数据...');
      await projectService.deleteProject(createdProject.id);
      console.log('✅ 测试项目已删除');
      
    } catch (error) {
      console.error('❌ 创建测试项目失败:', error.message);
    }
    
    // 7. 测试合同签署状态枚举值
    console.log('\n📝 步骤7: 验证合同签署状态枚举值...');
    console.log('📋 可用的合同签署状态:');
    Object.values(ContractSigningStatus).forEach(status => {
      console.log(`  - ${status}`);
    });
    
    // 8. 测试组合过滤条件
    console.log('\n📝 步骤8: 测试组合过滤条件...');
    
    try {
      const combinedFilter = await projectService.getProjects({
        contractType: 'single',
        contractSigningStatus: ContractSigningStatus.NO_CONTRACT
      });
      console.log(`✅ 单次合同且无合同的项目: ${combinedFilter.projects.length} 个`);
      
      if (combinedFilter.projects.length > 0) {
        combinedFilter.projects.forEach(project => {
          console.log(`    - ${project.projectName} (${project.contractType}, ${project.contractSigningStatus})`);
        });
      }
    } catch (error) {
      console.error('❌ 组合过滤失败:', error.message);
    }
    
    console.log('\n✅ 合同签署状态功能测试完成');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在停止测试...');
  process.exit(0);
});

// 运行测试
testContractSigningStatus()
  .then(() => {
    console.log('\n🎉 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  });
