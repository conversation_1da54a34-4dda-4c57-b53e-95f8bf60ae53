# Kubernetes 开发环境部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cantv-ding-backend-dev
  namespace: cantv-ding-dev
  labels:
    app: cantv-ding-backend
    env: development
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cantv-ding-backend
      env: development
  template:
    metadata:
      labels:
        app: cantv-ding-backend
        env: development
        version: v1
    spec:
      containers:
      - name: cantv-ding-backend
        image: registry.cn-hangzhou.aliyuncs.com/cantv-ding/cantv-ding-backend:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          value: "development"
        - name: PORT
          value: "3000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets-dev
              key: database-url
        - name: DINGTALK_APP_KEY
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets-dev
              key: dingtalk-app-key
        - name: DINGTALK_APP_SECRET
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets-dev
              key: dingtalk-app-secret
        - name: DINGTALK_CORP_ID
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets-dev
              key: dingtalk-corp-id
        - name: DINGTALK_AGENT_ID
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets-dev
              key: dingtalk-agent-id
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets-dev
              key: jwt-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
      volumes:
      - name: app-logs
        emptyDir: {}
      imagePullSecrets:
      - name: aliyun-registry-secret

---
apiVersion: v1
kind: Service
metadata:
  name: cantv-ding-backend-service-dev
  namespace: cantv-ding-dev
  labels:
    app: cantv-ding-backend
    env: development
spec:
  selector:
    app: cantv-ding-backend
    env: development
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cantv-ding-backend-ingress-dev
  namespace: cantv-ding-dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: dev-api.cantv-ding.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cantv-ding-backend-service-dev
            port:
              number: 80

---
apiVersion: v1
kind: Secret
metadata:
  name: cantv-ding-secrets-dev
  namespace: cantv-ding-dev
type: Opaque
data:
  # 注意：这些值需要base64编码
  # 开发环境的配置值
  database-url: cG9zdGdyZXNxbDovL3VzZXI6cGFzc3dvcmRAZGV2LWhvc3Q6cG9ydC9kZXYtZGF0YWJhc2U=
  dingtalk-app-key: ZGV2LWRpbmd0YWxrLWFwcC1rZXk=
  dingtalk-app-secret: ZGV2LWRpbmd0YWxrLWFwcC1zZWNyZXQ=
  dingtalk-corp-id: ZGV2LWRpbmd0YWxrLWNvcnAtaWQ=
  dingtalk-agent-id: ZGV2LWRpbmd0YWxrLWFnZW50LWlk
  jwt-secret: ZGV2LWp3dC1zZWNyZXQta2V5
