# syntax=docker/dockerfile:1
# -------------------------
# 第一阶段：安装 + 构建
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

WORKDIR /app

# 1) 配置阿里云镜像源和 pnpm（使用 BuildKit 缓存挂载优化）
RUN --mount=type=cache,target=/var/cache/apk \
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update \
    && apk add --no-cache python3 make g++ \
    && npm config set registry=https://registry.npmmirror.com \
    && npm config set disturl=https://npmmirror.com/dist \
    && npm config set electron_mirror=https://npmmirror.com/mirrors/electron/ \
    && npm config set puppeteer_download_host=https://npmmirror.com/mirrors \
    && npm install -g pnpm --registry=https://registry.npmmirror.com

# 2) 配置 pnpm 使用阿里云镜像和本地缓存
RUN pnpm config set registry https://registry.npmmirror.com \
    && pnpm config set store-dir /root/.pnpm-store

# 3) 复制依赖文件（优化缓存层级）
COPY package.json pnpm-lock.yaml ./

# 4) 复制 Prisma schema
COPY prisma ./prisma/

# 5) 安装依赖（使用缓存挂载大幅提升速度）
RUN --mount=type=cache,target=/root/.pnpm-store \
    --mount=type=cache,target=/root/.npm \
    pnpm install --frozen-lockfile

# 6) 生成 Prisma 客户端
RUN pnpm db:generate

# 7) 复制源码并构建
COPY . .
RUN pnpm run build

# -------------------------
# 第二阶段：运行时环境
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

WORKDIR /app

# 1) 配置系统环境（使用缓存挂载）
RUN --mount=type=cache,target=/var/cache/apk \
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update \
    && apk add --no-cache tzdata curl dumb-init \
    && npm config set registry=https://registry.npmmirror.com \
    && npm install -g pnpm --registry=https://registry.npmmirror.com

# 2) 配置 pnpm 镜像源
RUN pnpm config set registry https://registry.npmmirror.com \
    && pnpm config set store-dir /root/.pnpm-store

# 3) 复制依赖配置文件
COPY package.json pnpm-lock.yaml ./

# 4) 复制 Prisma schema
COPY prisma ./prisma/

# 5) 安装生产依赖（使用缓存挂载）
RUN --mount=type=cache,target=/root/.pnpm-store \
    --mount=type=cache,target=/root/.npm \
    pnpm install --prod --frozen-lockfile

# 6) 生成 Prisma 客户端
RUN pnpm db:generate

# 7) 从 builder 阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 8) 复制运行时文件
COPY start.sh .env .env.production ./

# 9) 创建非 root 用户并设置权限
RUN addgroup -S nodejs \
    && adduser -S -G nodejs nodejs \
    && chown -R nodejs:nodejs /app \
    && chmod +x start.sh

USER nodejs

# 10) 配置运行时环境
EXPOSE 3000
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

ENV NODE_ENV=production \
    TZ=Asia/Shanghai

# 使用 dumb-init 作为 PID 1
ENTRYPOINT ["dumb-init", "--"]
CMD ["./start.sh"]