<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CanTV财务报表导出演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4F46E5;
        }

        .checkbox-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            flex: 1;
            padding: 14px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
            border: 2px solid #E5E7EB;
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .preview-section {
            background: #F8FAFC;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
        }

        .preview-section h3 {
            color: #1F2937;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .preview-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4F46E5;
        }

        .info-item .label {
            font-size: 12px;
            color: #6B7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .info-item .value {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
        }

        .features-list {
            background: white;
            border-radius: 8px;
            padding: 20px;
        }

        .features-list h4 {
            color: #1F2937;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #F3F4F6;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .features-list li::before {
            content: "✨";
            font-size: 14px;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }

        .status.success {
            background: #ECFDF5;
            color: #065F46;
            border: 1px solid #A7F3D0;
        }

        .status.error {
            background: #FEF2F2;
            color: #991B1B;
            border: 1px solid #FECACA;
        }

        .status.loading {
            background: #EFF6FF;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }

            .content {
                padding: 20px;
            }

            .btn-group {
                flex-direction: column;
            }

            .checkbox-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 CanTV财务报表导出</h1>
            <p>美化版Excel报表，专业企业级外观</p>
        </div>

        <div class="content">
            <form id="exportForm">
                <div class="form-group">
                    <label for="year">📅 导出年份</label>
                    <select id="year" name="year" required>
                        <option value="">请选择年份</option>
                        <option value="2024" selected>2024年</option>
                        <option value="2023">2023年</option>
                        <option value="2022">2022年</option>
                        <option value="2021">2021年</option>
                        <option value="2020">2020年</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="brandIds">🏷️ 品牌筛选（可选）</label>
                    <input type="text" id="brandIds" name="brandIds" 
                           placeholder="输入品牌ID，多个用逗号分隔，留空表示所有品牌">
                </div>

                <div class="form-group">
                    <label>📊 项目状态筛选</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="includeCompleted" name="includeCompleted" checked>
                            <label for="includeCompleted">包含已完成项目</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="includeCancelled" name="includeCancelled">
                            <label for="includeCancelled">包含已取消项目</label>
                        </div>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="button" class="btn btn-secondary" onclick="getPreview()">
                        👀 预览信息
                    </button>
                    <button type="submit" class="btn btn-primary">
                        📥 导出Excel
                    </button>
                </div>
            </form>

            <div class="preview-section">
                <h3>📋 导出预览</h3>
                <div class="preview-info">
                    <div class="info-item">
                        <div class="label">预计工作表数量</div>
                        <div class="value" id="estimatedSheets">3个</div>
                    </div>
                    <div class="info-item">
                        <div class="label">预计文件大小</div>
                        <div class="value" id="estimatedSize">1-5MB</div>
                    </div>
                    <div class="info-item">
                        <div class="label">预计生成时间</div>
                        <div class="value" id="estimatedTime">10-30秒</div>
                    </div>
                    <div class="info-item">
                        <div class="label">文件格式</div>
                        <div class="value">Excel (.xlsx)</div>
                    </div>
                </div>

                <div class="features-list">
                    <h4>🎨 美化特性</h4>
                    <ul>
                        <li>专业企业级外观设计</li>
                        <li>多彩表头和交替行颜色</li>
                        <li>状态颜色编码（绿色=完成，蓝色=进行中，红色=取消）</li>
                        <li>金额千分位分隔符格式化</li>
                        <li>冻结窗格便于浏览大表格</li>
                        <li>打印友好的页面设置</li>
                        <li>自动合计行计算</li>
                        <li>清晰的边框和对齐</li>
                    </ul>
                </div>
            </div>

            <div id="status" class="status"></div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE_URL = 'http://localhost:3000/api';
        
        // 模拟JWT Token（实际使用时从登录获取）
        const AUTH_TOKEN = 'your-jwt-token-here';

        // 表单提交处理
        document.getElementById('exportForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await exportFinancialReport();
        });

        // 导出财务报表
        async function exportFinancialReport() {
            const formData = new FormData(document.getElementById('exportForm'));
            const params = new URLSearchParams();
            
            // 构建查询参数
            params.append('year', formData.get('year'));
            
            if (formData.get('brandIds')) {
                params.append('brandIds', formData.get('brandIds'));
            }
            
            params.append('includeCompleted', formData.get('includeCompleted') ? 'true' : 'false');
            params.append('includeCancelled', formData.get('includeCancelled') ? 'true' : 'false');

            showStatus('loading', '🚀 正在生成财务报表，请稍候...');

            try {
                const response = await fetch(`${API_BASE_URL}/financial/export?${params}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // 获取文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                
                // 创建下载链接
                const a = document.createElement('a');
                a.href = url;
                a.download = `财务报表_${formData.get('year')}年_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showStatus('success', `✅ 财务报表导出成功！文件大小: ${(blob.size / 1024).toFixed(2)} KB`);

            } catch (error) {
                console.error('导出失败:', error);
                showStatus('error', `❌ 导出失败: ${error.message}`);
            }
        }

        // 获取预览信息
        async function getPreview() {
            const formData = new FormData(document.getElementById('exportForm'));
            const params = new URLSearchParams();
            
            params.append('year', formData.get('year') || '2024');
            
            if (formData.get('brandIds')) {
                params.append('brandIds', formData.get('brandIds'));
            }
            
            params.append('includeCompleted', formData.get('includeCompleted') ? 'true' : 'false');
            params.append('includeCancelled', formData.get('includeCancelled') ? 'true' : 'false');

            try {
                const response = await fetch(`${API_BASE_URL}/financial/export/preview?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                // 更新预览信息
                document.getElementById('estimatedSheets').textContent = `${data.data.estimatedSheets}个`;
                document.getElementById('estimatedSize').textContent = data.data.estimatedSize;
                document.getElementById('estimatedTime').textContent = data.data.estimatedTime;

                showStatus('success', '✅ 预览信息已更新');

            } catch (error) {
                console.error('获取预览失败:', error);
                showStatus('error', `❌ 获取预览失败: ${error.message}`);
            }
        }

        // 显示状态信息
        function showStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
            statusEl.style.display = 'block';

            if (type !== 'loading') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 5000);
            }
        }

        // 页面加载时获取预览信息
        window.addEventListener('load', () => {
            getPreview();
        });
    </script>
</body>
</html>
