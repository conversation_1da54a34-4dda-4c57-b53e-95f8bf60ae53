# -------------------------
# 第一阶段：安装 + 构建
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

WORKDIR /app

# 1) 配置包管理器源并安装 pnpm（合并到一层减少镜像层数）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && apk update \
  && npm config set registry=https://registry.npmmirror.com \
  && npm install -g pnpm --registry=https://registry.npmmirror.com

# 2) 复制依赖文件（优化缓存层级，依赖变化频率低）
COPY package.json pnpm-lock.yaml ./

# 3) 复制 Prisma schema（在安装依赖前，因为生成需要依赖）
COPY prisma ./prisma/

# 4) 安装所有依赖并生成 Prisma 客户端（合并减少层数）
RUN pnpm install && pnpm db:generate

# 5) 复制源码并构建（源码变化频率最高，放最后）
COPY . .
RUN pnpm run build

# -------------------------
# 第二阶段：运行时环境
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

WORKDIR /app

# 1) 配置系统环境（合并 APK 操作减少层数）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && apk update \
  && apk add --no-cache tzdata curl \
  && npm config set registry=https://registry.npmmirror.com \
  && npm install -g pnpm --registry=https://registry.npmmirror.com

# 2) 复制依赖配置文件
COPY package.json pnpm-lock.yaml ./

# 3) 复制 Prisma schema 用于生成客户端
COPY prisma ./prisma/

# 4) 安装生产依赖并生成 Prisma 客户端（从 builder 阶段复制会更快，但为了确保一致性还是重新安装）
RUN pnpm install --prod && pnpm db:generate

# 5) 从 builder 阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 6) 复制必要的运行时文件（保留 env 文件）
COPY start.sh .env .env.production ./

# 7) 创建非 root 用户并设置权限（合并到一层）
RUN addgroup -S nodejs \
  && adduser -S -G nodejs nodejs \
  && chown -R nodejs:nodejs /app \
  && chmod +x start.sh

USER nodejs

# 8) 配置运行时环境
EXPOSE 3000
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

ENV NODE_ENV=production \
  TZ=Asia/Shanghai

CMD ["./start.sh"]
