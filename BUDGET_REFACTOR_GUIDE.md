# 预算重构部署和验证指南

## 概述

本指南详细说明了如何部署和验证预算重构功能。重构将"周预算"改为"预算"，并新增"资金计划"表，实现更灵活的资金管理和审批流程。

## 重构内容

### 1. 数据库架构变更

- **新增表**: `budgets` (预算表)
- **新增表**: `funding_plans` (资金计划表)
- **保留表**: `weekly_budgets` (周预算表，用于兼容)
- **修改表**: `approval_instances` (审批实例表，新增 `fundingPlanId` 字段)

### 2. 核心功能变更

- **预算管理**: 去掉周的概念，支持更灵活的预算管理
- **资金计划**: 按年月周管理具体的资金计划
- **审批流程**: 审批关联到资金计划，支持单独周的审批
- **回写机制**: 审批通过后自动更新已付款金额

## 部署步骤

### 第一步：数据库迁移

1. **生成 Prisma 客户端**
   ```bash
   npx prisma generate
   ```

2. **创建数据库迁移**
   ```bash
   npx prisma migrate dev --name "refactor_budget_and_funding_plan"
   ```

3. **运行数据迁移脚本**
   ```bash
   node scripts/migrate-budget-refactor.js
   ```

### 第二步：验证功能

1. **运行测试脚本**
   ```bash
   node scripts/test-budget-refactor.js
   ```

2. **检查测试结果**
   - 确保所有测试通过
   - 验证数据完整性
   - 检查关联关系

3. **清理测试数据**
   ```bash
   node scripts/cleanup-test-data.js
   ```

### 第三步：启动服务

1. **启动应用**
   ```bash
   npm start
   ```

2. **验证API端点**
   - 预算管理: `/api/budgets`
   - 资金计划管理: `/api/funding-plans`
   - 项目预算: `/api/projects/:projectId/budgets`
   - 预算资金计划: `/api/budgets/:budgetId/funding-plans`

## API 端点说明

### 预算管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/projects/:projectId/budgets` | 创建项目预算 |
| GET | `/api/budgets` | 获取预算列表 |
| GET | `/api/projects/:projectId/budgets` | 获取项目预算列表 |
| GET | `/api/budgets/:id` | 获取预算详情 |
| PUT | `/api/budgets/:id` | 更新预算 |
| DELETE | `/api/budgets/:id` | 删除预算 |
| GET | `/api/budget-stats` | 获取预算统计 |

### 资金计划管理 API

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/funding-plans` | 创建资金计划 |
| GET | `/api/funding-plans` | 获取资金计划列表 |
| GET | `/api/budgets/:budgetId/funding-plans` | 获取预算的资金计划列表 |
| GET | `/api/funding-plans/:id` | 获取资金计划详情 |
| PUT | `/api/funding-plans/:id` | 更新资金计划 |
| DELETE | `/api/funding-plans/:id` | 删除资金计划 |
| POST | `/api/funding-plans/:id/approval` | 提交资金计划审批 |
| GET | `/api/funding-plan-stats` | 获取资金计划统计 |

## 数据模型说明

### 预算 (Budget)

```typescript
interface Budget {
  id: string;
  title: string;                    // 预算标题
  serviceType: ServiceType;         // 服务类型
  serviceContent: string;           // 服务内容描述
  remarks?: string;                 // 备注
  contractAmount: number;           // 合同金额
  taxRate: TaxRate;                 // 税率
  totalPaidAmount: number;          // 总已付金额
  remainingAmount: number;          // 剩余金额
  status: BudgetStatus;             // 预算状态
  projectId: string;                // 关联项目ID
  supplierId?: string;              // 关联供应商ID
  fundingPlans?: FundingPlan[];     // 关联的资金计划
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
```

### 资金计划 (FundingPlan)

```typescript
interface FundingPlan {
  id: string;
  title: string;                    // 计划标题
  year: number;                     // 年份
  month: number;                    // 月份 (1-12)
  weekOfMonth: number;              // 当月第几周 (1-5)
  plannedAmount: number;            // 计划资金
  paidAmount: number;               // 已付款
  remainingAmount: number;          // 剩余金额
  status: FundingPlanStatus;        // 计划状态
  approvalStatus: ApprovalStatus;   // 审批状态
  approvalAmount?: number;          // 审批金额
  approvalReason?: string;          // 审批原因
  remarks?: string;                 // 备注
  budgetId: string;                 // 关联预算ID
  budget?: Budget;                  // 关联的预算信息
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}
```

## 审批流程说明

### 1. 提交审批

- 用户选择资金计划提交审批
- 系统创建审批实例，关联到 `fundingPlanId`
- 审批状态设置为 `PENDING`

### 2. 审批处理

- 审批人员处理审批请求
- 系统更新审批实例状态
- 如果审批通过，触发回写逻辑

### 3. 回写逻辑

- 审批通过后，更新资金计划的 `paidAmount`
- 重新计算资金计划的 `remainingAmount`
- 更新关联预算的 `totalPaidAmount`
- 重新计算预算的 `remainingAmount`

## 兼容性说明

### 向后兼容

- 保留原有的 `weekly_budgets` 表
- 保留原有的 API 端点
- 审批实例同时支持 `weeklyBudgetId` 和 `fundingPlanId`

### 迁移策略

- 现有周预算数据自动迁移到新的预算和资金计划结构
- 按项目和服务类型分组创建预算
- 为每个周预算创建对应的资金计划
- 更新审批实例的关联关系

## 故障排除

### 常见问题

1. **Prisma 客户端未生成**
   ```bash
   npx prisma generate
   ```

2. **数据库连接失败**
   - 检查数据库配置
   - 确保数据库服务运行正常

3. **迁移失败**
   - 检查数据库权限
   - 确保没有数据冲突
   - 使用回滚脚本恢复数据

### 回滚方案

如果需要回滚到原有结构：

```bash
node scripts/rollback-budget-refactor.js
```

## 监控和维护

### 性能监控

- 监控新API端点的响应时间
- 检查数据库查询性能
- 观察内存使用情况

### 数据完整性检查

- 定期检查预算和资金计划的金额一致性
- 验证审批实例的关联关系
- 监控数据迁移的完整性

## 总结

预算重构提供了更灵活的资金管理方式，支持按周进行独立的审批流程。通过本指南，您可以安全地部署和验证新功能，确保系统的稳定性和数据完整性。
